# API Requirements: `/get-stripe-connect-account` Endpoint

## Overview
This document outlines the requirements for a new API endpoint that will retrieve Stripe Connect account details based on the account ID stored in the user's preferences.

## Endpoint Details
- **URL**: `/get-stripe-connect-account`
- **Method**: POST
- **Authentication**: Requires user authentication via `x-appwrite-user-id` header

## Request Body
```json
{
  "accountId": "acct_123456789"
}
```

## Response
The endpoint should return the Stripe Connect account details in JSON format. The response should include:

```json
{
  "id": "acct_123456789",
  "object": "account",
  "business_profile": {
    "name": "Business Name",
    "url": "https://example.com"
  },
  "email": "<EMAIL>",
  "capabilities": {
    "card_payments": "active",
    "transfers": "active"
  },
  "charges_enabled": true,
  "payouts_enabled": true,
  "details_submitted": true,
  "country": "US",
  "default_currency": "usd"
}
```

## Error Handling
- If the account ID is not provided, return a 400 error with a message indicating that the account ID is required.
- If the account is not found, return a 404 error with a message indicating that the account was not found.
- If there's an error retrieving the account details from Stripe, return a 500 error with a message indicating the issue.

## Implementation Notes
1. The endpoint should use the Stripe API to retrieve the account details using the provided account ID.
2. The endpoint should validate that the user making the request is authorized to access the account details.
3. The endpoint should handle errors gracefully and return appropriate error messages.

## Security Considerations
- Ensure that only the account owner can access their account details.
- Do not expose sensitive information in the response.
- Validate the account ID to prevent injection attacks.
