import { Env } from '@env';
import { Query } from 'react-native-appwrite';
import { create } from 'zustand';

import { databases } from '@/lib/appwrite';
import { trpc } from '@/lib/api';
import { useQuery } from '@tanstack/react-query';

export const AIRLINES = [
  { name: 'American Airlines', iata: 'AA' },
  { name: 'Delta Air Lines', iata: 'DL' },
  { name: 'United Airlines', iata: 'UA' },
  { name: 'Southwest Airlines', iata: 'WN' },
  { name: 'Lufthansa', iata: 'LH' },
  { name: 'British Airways', iata: 'BA' },
  { name: 'Air France', iata: 'AF' },
  { name: 'Emirates', iata: 'EK' },
  { name: 'Qatar Airways', iata: 'QR' },
  { name: 'Singapore Airlines', iata: 'SQ' },
  { name: 'Turkish Airlines', iata: 'TK' },
  { name: 'Air India', iata: 'AI' },
  { name: 'IndiGo', iata: '6E' },
  { name: 'Japan Airlines', iata: 'JL' },
  { name: 'Qantas', iata: 'QF' },
  { name: 'KLM Royal Dutch Airlines', iata: 'KL' },
  { name: 'Etihad Airways', iata: 'EY' },
  { name: 'China Southern Airlines', iata: 'CZ' },
  { name: 'China Eastern Airlines', iata: 'MU' },
  { name: 'Ryanair', iata: 'FR' },
];

export type Airport = {
  id: string;
  name: string;
  shortCode: string;
  airportLocation: string;
};

export type CreateAirportData = {
  name: string;
  shortCode: string;
  city: string;
  state: string;
  country: string;
  icao?: string;
  type?: string;
  timezone?: string;
  lat?: number;
  lon?: number;
};

type AirportState = {
  airports: Airport[];
  isLoading: boolean;
  hasLoaded: boolean;
  error: string | null;
  fetchAirports: () => Promise<void>;
  createAirport: (data: CreateAirportData) => Promise<Airport | null>;
};

export const useAirportStore = create<AirportState>()((set, get) => ({
  airports: [],
  isLoading: false,
  hasLoaded: false,
  error: null,
  fetchAirports: async () => {
    const { data: mappedAirports } = useQuery(trpc.airports.getAll.queryOptions());
    console.log('mappedAirportsmappedAirports');
    // If airports are already loaded, don't fetch again
    if (get().hasLoaded && get().airports.length > 0) {
      return;
    }

    set({ isLoading: true, error: null });

    try {
      console.log('mappedAirportsmappedAirports', mappedAirports);
      // const airportsResponse = await databases.listDocuments(
      //   Env.COLLECTION_ID,
      //   Env.COLLECTION_AIRPORTS,
      //   [
      //     Query.limit(100_000),
      //     Query.orderAsc('name'),
      //     Query.isNull('deletedAt'),
      //   ]
      // );

      // console.log(airportsResponse.documents);

      // const mappedAirports = airportsResponse.documents.map((item) => ({
      //   id: item.$id,
      //   name: item.name,
      //   shortCode: item.shortCode,
      //   // icao: item.icao,
      //   // type: item.type,
      //   // timezone: item.timezone,
      //   // lat: item.lat,
      //   // lon: item.lon,
      //   airportLocation: `${item.locationCity?.name} , ${item.locationCity?.state}`,
      // }));

      set({
        airports: mappedAirports ?? [],
        isLoading: false,
        hasLoaded: true,
      });
    } catch (err) {
      console.error('Error fetching airports:', err);
      set({
        error: err instanceof Error ? err.message : 'Failed to fetch airports',
        isLoading: false,
      });
    }
  },
  createAirport: async (data: CreateAirportData) => {
    set({ isLoading: true, error: null });

    try {
      // First, create the city if it doesn't exist
      let cityId = '';
      try {
        const cityResponse = await databases.createDocument(
          Env.COLLECTION_ID,
          Env.COLLECTION_LOCATION_CITY,
          'unique()',
          {
            name: data.city,
            state: data.state,
            country: data.country,
          }
        );
        cityId = cityResponse.$id;
      } catch (cityErr: any) {
        // If city already exists, try to find it
        if (cityErr.code === 409) {
          const existingCities = await databases.listDocuments(
            Env.COLLECTION_ID,
            Env.COLLECTION_LOCATION_CITY,
            [
              Query.equal('name', data.city),
              Query.equal('state', data.state),
              Query.equal('country', data.country),
            ]
          );
          if (existingCities.documents.length > 0) {
            cityId = existingCities.documents[0].$id;
          }
        } else {
          throw cityErr;
        }
      }

      // Create the airport
      const airportResponse = await databases.createDocument(
        Env.COLLECTION_ID,
        Env.COLLECTION_AIRPORTS,
        'unique()',
        {
          name: data.name,
          shortCode: data.shortCode,
          icao: data.icao || '',
          type: data.type || 'airport',
          timezone: data.timezone || 'UTC',
          lat: data.lat || 0,
          lon: data.lon || 0,
          locationCity: cityId,
        }
      );

      const newAirport: Airport = {
        id: airportResponse.$id,
        name: airportResponse.name,
        shortCode: airportResponse.shortCode,
        airportLocation: `${data.city} , ${data.state}`,
      };

      // Add the new airport to the local state
      set((state) => ({
        airports: [...state.airports, newAirport].sort((a, b) =>
          a.name.localeCompare(b.name)
        ),
        isLoading: false,
      }));

      return newAirport;
    } catch (err) {
      console.error('Error creating airport:', err);
      set({
        error: err instanceof Error ? err.message : 'Failed to create airport',
        isLoading: false,
      });
      return null;
    }
  },
}));
