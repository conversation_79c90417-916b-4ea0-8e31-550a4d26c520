import { create } from 'zustand';
type IAirport = {
  index: number;
  id: string;
  name: string;
};
export type IBookingStore = {
  selectedAirports: IAirport[];
  bookingTravellerId: string | null;
  countOfInterConnectedAirport: number;
  setBookingTravellerId: (id: string) => void;
  flightBookingId: string | null;
  addEmptyAirport: () => void;
  setFlightBookingid: (id: string) => void;
  setSelectedAirports: ({ index, id, name }: IAirport) => void;
  setCountOfInterConnectedAirport: () => void;
  removeAirport: (index: number) => void;
};
export const useBookingStore = create<IBookingStore>()((set, get) => ({
  selectedAirports: [
    {
      index: 0,
      id: '',
      name: '',
    },
    {
      index: 1,
      id: '',
      name: '',
    },
  ],
  bookingTravellerId: null,
  flightBookingId: null,
  countOfInterConnectedAirport: 0,
  setBookingTravellerId: (id: string | null) => {
    set({ bookingTravellerId: id });
  },
  setFlightBookingid: (id: string) => {
    set({ flightBookingId: id });
  },
  setCountOfInterConnectedAirport: () => {
    set((state) => {
      return {
        countOfInterConnectedAirport: state.countOfInterConnectedAirport + 1,
      };
    });
  },
  addEmptyAirport: () => {
    set((state) => {
      return {
        selectedAirports: [
          ...state.selectedAirports,
          { index: state.selectedAirports.length, id: '', name: '' },
        ],
      };
    });
  },
  setSelectedAirports: ({ index, id, name }: IAirport) => {
    set((state) => {
      let updatedAirports = [...state.selectedAirports];

      // if (state.countOfInterConnectedAirport > 0 && name === '') {
      //   //find the airport with max index
      //   const airportMaxIndex = Math.max(
      //     ...state.selectedAirports.map((airport) => Number(airport.index))
      //   );
      //   //details of the airport with max index
      //   const maxIndexAirportDetails = state.selectedAirports.find(
      //     (airport) => airport.index === airportMaxIndex
      //   );

      //   if (maxIndexAirportDetails) {
      //     // Create a new array with the modified index for the airport with the max index
      //     updatedAirports = state.selectedAirports.map((airport) => {
      //       if (airport.index === maxIndexAirportDetails.index) {
      //         return {
      //           ...airport,
      //           index: state.countOfInterConnectedAirport + 1,
      //         };
      //       }
      //       return airport;
      //     });
      //   }

      //   // updatedAirports.push({ id, name, index });
      //   return {
      //     selectedAirports: updatedAirports,
      //   };
      // } else {
      //   Find if an airport with the same index already exists
      const existingAirportIndex = state.selectedAirports.findIndex(
        (airport) => airport.index === index
      );

      if (existingAirportIndex !== -1) {
        // If exists, create a new array with the old airport replaced
        updatedAirports[existingAirportIndex] = { index, id, name };
      } else {
        updatedAirports.push({ index, id, name });
      }
      console.log('selected airports after modification', updatedAirports);
      return {
        selectedAirports: updatedAirports,
      };
      // }
    });
  },
  removeAirport: (index) =>
    set((state) => ({
      selectedAirports: state.selectedAirports.filter(
        (airport) => airport.index !== index
      ),
      countOfInterConnectedAirport: state.countOfInterConnectedAirport - 1,
    })),
}));
