// themeStore.ts (zustand store)
import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';

const THEME_KEY = 'theme-isDay'; // The key for AsyncStorage

interface ThemeState {
  isDay: boolean; // Boolean state for light/dark mode
  toggleTheme: () => void; // Function to toggle between themes
  initializeTheme: () => void; // Function to initialize theme from AsyncStorage
}

// Create Zustand store
export const useThemeStore = create<ThemeState>()((set) => ({
  isDay: true, // Default to light mode
  toggleTheme: async () => {
    set((state) => {
      const newIsDay = !state.isDay; // Toggle the theme (from light to dark or vice versa)
      AsyncStorage.setItem(THEME_KEY, JSON.stringify(newIsDay)); // Save the new theme to AsyncStorage
      return { isDay: newIsDay }; // Update the state
    });
  },
  initializeTheme: async () => {
    try {
      const storedTheme = await AsyncStorage.getItem(THEME_KEY); // Try to get stored theme
      if (storedTheme !== null) {
        set({ isDay: JSON.parse(storedTheme) }); // If theme is found, set the state from AsyncStorage
      }
    } catch (error) {
      console.error('Error loading theme from AsyncStorage:', error); // Handle any errors
    }
  },
}));
