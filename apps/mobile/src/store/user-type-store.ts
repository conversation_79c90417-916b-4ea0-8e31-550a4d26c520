import AsyncStorage from '@react-native-async-storage/async-storage';
import { type Models } from 'react-native-appwrite';
import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';

export enum UserType {
  TRAVELLER = 'TRAVELLER',
  COMPANION = 'COMPANION',
}

export type IUserTypeStore = {
  userType: UserType;
  user: Models.User<any> | null;
  changeUserType: (type: UserType) => void;
  changeUser: (user: Models.User<any>) => void;
};
export const userTypeStore = create<IUserTypeStore>()(persist((set, get) => ({
  userType: UserType.TRAVELLER,
  user: null,
  changeUserType: (type: UserType) => {
    set({ userType: type });
  },
  changeUser: (user: Models.User<any>) => {
    set({ user });
  },
}), {
  name: 'user-type-store',
  storage: createJSONStorage(() => AsyncStorage),
}));
