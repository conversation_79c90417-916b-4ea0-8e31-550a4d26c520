import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { StreamChat } from 'stream-chat';
import { Env } from '@env';
import { trpc } from '@/lib/api';

/**
 * Hook to get user token for GetStream authentication
 */
export const useGetStreamToken = ({
  enabled = true,
}: {
  enabled?: boolean;
} = {}) => {
  return useQuery(trpc.chat.getToken.queryOptions(undefined, { enabled }));
};

/**
 * Hook to create or update GetStream user
 */
export const useCreateGetStreamUser = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    ...trpc.chat.createUser.mutationOptions(),
    onSuccess: () => {
      // Invalidate token query to refresh
      queryClient.invalidateQueries({
        queryKey: trpc.chat.getToken.queryKey(),
      });
    },
  });
};

/**
 * Hook to get all user channels
 */
export const useGetUserChannels = () => {
  return useQuery(trpc.chat.getUserChannels.queryOptions());
};

/**
 * Hook to get a specific channel by connection request ID
 */
export const useGetChannel = (connectionRequestId: string) => {
  return useQuery(
    trpc.chat.getChannel.queryOptions({ connectionRequestId })
  );
};

/**
 * Hook to create a channel for an accepted connection
 */
export const useCreateChannel = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    ...trpc.chat.createChannel.mutationOptions(),
    onSuccess: () => {
      // Invalidate channels queries to refresh
      queryClient.invalidateQueries({
        queryKey: trpc.chat.getUserChannels.queryKey(),
      });
    },
  });
};

/**
 * Hook to send a message to a channel
 */
export const useSendMessage = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    ...trpc.chat.sendMessage.mutationOptions(),
    onSuccess: (_, variables) => {
      // Invalidate channel queries to refresh messages
      queryClient.invalidateQueries({
        queryKey: trpc.chat.getChannel.queryKey({ 
          connectionRequestId: variables.connectionRequestId 
        }),
      });
    },
  });
};

/**
 * Hook to delete a channel
 */
export const useDeleteChannel = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    ...trpc.chat.deleteChannel.mutationOptions(),
    onSuccess: () => {
      // Invalidate channels queries to refresh
      queryClient.invalidateQueries({
        queryKey: trpc.chat.getUserChannels.queryKey(),
      });
    },
  });
};

/**
 * GetStream client service for direct operations
 */
class StreamChatClient {
  private client: StreamChat | null = null;
  private currentUser: any = null;

  /**
   * Initialize the GetStream client
   */
  initializeClient() {
    if (!this.client) {
      this.client = StreamChat.getInstance(Env.STREAM_API_KEY);
    }
    return this.client;
  }

  /**
   * Connect user to GetStream
   */
  async connectUser(userId: string, userName: string, token: string, userImage?: string) {
    try {
      const client = this.initializeClient();
      
      // Connect user
      await client.connectUser(
        {
          id: userId,
          name: userName,
          image: userImage,
        },
        token
      );

      this.currentUser = client.user;
      return client;
    } catch (error) {
      console.error('Error connecting user to GetStream:', error);
      throw new Error('Failed to connect to chat');
    }
  }

  /**
   * Disconnect user from GetStream
   */
  async disconnectUser() {
    try {
      if (this.client) {
        await this.client.disconnectUser();
        this.client = null;
        this.currentUser = null;
      }
    } catch (error) {
      console.error('Error disconnecting user:', error);
    }
  }

  /**
   * Get the current client instance
   */
  getClient() {
    return this.client;
  }

  /**
   * Get the current user
   */
  getCurrentUser() {
    return this.currentUser;
  }

  /**
   * Check if user is connected
   */
  isConnected() {
    return this.client !== null && this.currentUser !== null;
  }
}

export const streamChatClient = new StreamChatClient(); 