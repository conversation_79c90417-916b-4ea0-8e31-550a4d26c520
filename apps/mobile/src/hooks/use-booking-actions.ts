import { useMutation, useQueryClient } from '@tanstack/react-query';
import { trpc } from '@/lib/api';
import { userTypeStore } from '@/store/user-type-store';
import { UserType } from '@/store/user-type-store';

export const useBookingActions = () => {
  const { userType } = userTypeStore();
  const queryClient = useQueryClient();

  // Traveler mutations
  const createTravelerMutation = useMutation({
    ...trpc.bookingTravelers.create.mutationOptions(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: trpc.bookingTravelers.getAllByUser.queryKey()
      });
    },
  });

  const updateTravelerMutation = useMutation({
    ...trpc.bookingTravelers.update.mutationOptions(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: trpc.bookingTravelers.getAllByUser.queryKey()
      });
    },
  });

  const deleteTravelerMutation = useMutation({
    ...trpc.bookingTravelers.delete.mutationOptions(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: trpc.bookingTravelers.getAllByUser.queryKey()
      });
    },
  });

  const upsertTravelerMutation = useMutation({
    ...trpc.bookingTravelers.upsert.mutationOptions(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: trpc.bookingTravelers.getAllByUser.queryKey()
      });
    },
  });

  const upsertTravelerFlightDetailsMutation = useMutation({
    ...trpc.bookingTravelers.upsertFlightDetails.mutationOptions(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: trpc.bookingTravelers.getAllByUser.queryKey()
      });
    },
  });

  const upsertTravelerPhotoUploadsMutation = useMutation({
    ...trpc.bookingTravelers.upsertPhotoUploads.mutationOptions(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: trpc.bookingTravelers.getAllByUser.queryKey()
      });
    },
  });

  // Companion mutations
  const createCompanionMutation = useMutation({
    ...trpc.bookingCompanions.create.mutationOptions(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: trpc.bookingCompanions.getAllByUser.queryKey()
      });
    },
  });

  const updateCompanionMutation = useMutation({
    ...trpc.bookingCompanions.update.mutationOptions(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: trpc.bookingCompanions.getAllByUser.queryKey()
      });
    },
  });

  const deleteCompanionMutation = useMutation({
    ...trpc.bookingCompanions.delete.mutationOptions(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: trpc.bookingCompanions.getAllByUser.queryKey()
      });
    },
  });

  const upsertCompanionMutation = useMutation({
    ...trpc.bookingCompanions.upsert.mutationOptions(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: trpc.bookingCompanions.getAllByUser.queryKey()
      });
    },
  });

  const upsertCompanionFlightDetailsMutation = useMutation({
    ...trpc.bookingCompanions.upsertFlightDetails.mutationOptions(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: trpc.bookingCompanions.getAllByUser.queryKey()
      });
    },
  });

  const upsertCompanionPhotoUploadsMutation = useMutation({
    ...trpc.bookingCompanions.upsertPhotoUploads.mutationOptions(),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: trpc.bookingCompanions.getAllByUser.queryKey()
      });
    },
  });

  // Return appropriate mutations based on user type
  if (userType === UserType.TRAVELLER) {
    return {
      createBooking: createTravelerMutation.mutate,
      updateBooking: updateTravelerMutation.mutate,
      deleteBooking: deleteTravelerMutation.mutate,
      upsertBooking: upsertTravelerMutation.mutate,
      upsertFlightDetails: upsertTravelerFlightDetailsMutation.mutate,
      upsertPhotoUploads: upsertTravelerPhotoUploadsMutation.mutate,
      isLoading: createTravelerMutation.isPending || 
                 updateTravelerMutation.isPending || 
                 deleteTravelerMutation.isPending ||
                 upsertTravelerMutation.isPending ||
                 upsertTravelerFlightDetailsMutation.isPending ||
                 upsertTravelerPhotoUploadsMutation.isPending,
      userType
    };
  } else {
    return {
      createBooking: createCompanionMutation.mutate,
      updateBooking: updateCompanionMutation.mutate,
      deleteBooking: deleteCompanionMutation.mutate,
      upsertBooking: upsertCompanionMutation.mutate,
      upsertFlightDetails: upsertCompanionFlightDetailsMutation.mutate,
      upsertPhotoUploads: upsertCompanionPhotoUploadsMutation.mutate,
      isLoading: createCompanionMutation.isPending || 
                 updateCompanionMutation.isPending || 
                 deleteCompanionMutation.isPending ||
                 upsertCompanionMutation.isPending ||
                 upsertCompanionFlightDetailsMutation.isPending ||
                 upsertCompanionPhotoUploadsMutation.isPending,
      userType
    };
  }
}; 