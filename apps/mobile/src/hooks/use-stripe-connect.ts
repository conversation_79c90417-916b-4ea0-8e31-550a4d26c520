import { useMutation, useQuery } from '@tanstack/react-query';

import { account } from '@/lib/appwrite';
import { trpc } from '@/lib/api';

export const useStripeConnect = () => {
  // For now, we'll use the traveler APIs since companion APIs might not be fully implemented
  // Query to get wallet balance
  const { data: accountDetails, refetch: refetchAccountDetails, isLoading: accountDetailsLoading } = useQuery(trpc.stripeCompanion.getConnectAccount.queryOptions());

  const createConnectAccountMutation = useMutation(trpc.stripeCompanion.createConnectAccount.mutationOptions());
  const createAccountLinkMutation = useMutation(trpc.stripeCompanion.createAccountLink.mutationOptions());

  return {
    accountDetails,
    accountDetailsLoading,
    refetchAccountDetails,
    createConnectAccountMutation,
    createAccountLinkMutation,
  };
};
