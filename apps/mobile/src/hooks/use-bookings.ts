import { useQuery } from '@tanstack/react-query';
import { trpc } from '@/lib/api';
import { userTypeStore } from '@/store/user-type-store';
import { UserType } from '@/store/user-type-store';
import type { BookingStatus } from '@/types';
import type { RouterOutputs } from '@/lib/api';

type BookingTraveler = RouterOutputs['bookingTravelers']['getAllByUser'][0];
type BookingCompanion = RouterOutputs['bookingCompanions']['getAllByUser'][0];
type Booking = BookingTraveler | BookingCompanion;

// Base hook for fetching bookings by status
export const useBookingsByStatus = (statuses: BookingStatus[]) => {
  const { userType } = userTypeStore();
  
  const query = userType === UserType.TRAVELLER 
    ? useQuery(trpc.bookingTravelers.getAllByUser.queryOptions())
    : useQuery(trpc.bookingCompanions.getAllByUser.queryOptions());
    
  return {
    ...query,
    data: query.data?.filter((booking: Booking) => statuses.includes(booking.status)) || [],
    userType
  };
};

// Hook for fetching draft bookings (DRAFT status)
export const useDraftBookings = () => {
  return useBookingsByStatus(['DRAFT']);
};

// Hook for fetching active bookings (ACTIVE, ASSIGNED, CONFIRMED status)
export const useActiveBookings = () => {
  return useBookingsByStatus(['ACTIVE', 'ASSIGNED', 'CONFIRMED']);
};

// Hook for fetching past bookings (COMPLETED or CANCELLED)
export const usePastBookings = () => {
  return useBookingsByStatus(['COMPLETED', 'CANCELLED']);
};

// Hook for fetching all bookings for current user
export const useAllBookings = () => {
  const { userType } = userTypeStore();
  
  const query = userType === UserType.TRAVELLER 
    ? useQuery(trpc.bookingTravelers.getAllByUser.queryOptions())
    : useQuery(trpc.bookingCompanions.getAllByUser.queryOptions());
    
  return {
    ...query,
    userType
  };
}; 