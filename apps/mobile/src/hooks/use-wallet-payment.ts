import { useStripe } from '@stripe/stripe-react-native';
import { useMutation, useQuery } from '@tanstack/react-query';

import { account } from '@/lib/appwrite';
import { trpc } from '@/lib/api';

interface CreatePaymentIntentResponse {
  ephemeralKey: string;
  paymentIntent: string;
}

export const useWalletPayment = () => {
  const { initPaymentSheet, presentPaymentSheet } = useStripe();
  const paymentIntentMutation = useMutation(trpc.stripeTraveler.createPaymentIntent.mutationOptions());

  // Query to get or create Stripe customer using tRPC
  // const { data: customer } = useQuery({
  //   queryKey: ['stripe-customer'],
  //   queryFn: async () => {
  //     console.log('useWalletPayment querying customer');
  //     const user = await account.get();

  //     console.log('useWalletPayment customer', user);
  //     if (!user?.phone) throw new Error('User phone number is required');

  //     // Use tRPC to create/get customer
  //     const result = await trpc.stripeTraveler.createCustomer.mutationOptions({
  //       name: user.name || 'User',
  //       email: user.email || '',
  //       phone: user.phone,
  //     });

  //     console.log('useWalletPayment customer from tRPC', result);
  //     return result.customer;
  //   },
  // });

  // Mutation to create payment intent using tRPC
  // const createPaymentIntent = useMutation({
  //   mutationFn: async (amount: number) => {
  //     const user = await account.get();

  //     console.log('Creating payment intent for amount:', amount);

      

  //     return {
  //       paymentIntent: result.paymentIntent,
  //       ephemeralKey: result.ephemeralKey,
  //     };
  //   },
  // });

  // Get wallet balance using tRPC
  const { data: walletBalance, refetch: refetchBalance } = useQuery(trpc.stripeTraveler.getWalletBalance.queryOptions());

  // Get payment history using tRPC
  const { data: paymentHistory, refetch: refetchHistory } = useQuery(trpc.stripeTraveler.getPaymentHistory.queryOptions());

  // Mutation to initialize and present payment sheet
  const processPayment = useMutation({
    mutationFn: async (amount: number) => {
      const user = await account.get();

      if (amount <= 0) {
        throw new Error('Amount must be greater than 0');
      }
      
      // Create payment intent (customer will be created automatically if needed)
      const result = await paymentIntentMutation.mutateAsync({
        amount: Math.round(amount * 100), // Convert to cents
        currency: 'usd',
      });

      if (!result || !result.paymentIntent || !result.ephemeralKey || !result.customerId) {
        throw new Error('Failed to create payment intent');
      }


      // Initialize payment sheet
      const { error: initError } = await initPaymentSheet({
        merchantDisplayName: 'Thedal',
        customerId: result.customerId,
        paymentIntentClientSecret: result.paymentIntent,
        customerEphemeralKeySecret: result.ephemeralKey,
        defaultBillingDetails: {
          phone: user?.phone || '',
        },
      });

      if (initError) throw initError;

      // Present payment sheet
      const { error: presentError } = await presentPaymentSheet();
      if (presentError) throw presentError;

      // Refresh wallet balance after successful payment
      await refetchBalance();
      await refetchHistory();

      return true;
    },
  });

  return {
    processPayment,
    walletBalance,
    paymentHistory,
    refetchBalance,
    refetchHistory,
  };
};
