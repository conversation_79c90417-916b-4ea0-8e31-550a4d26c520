import { UserType } from '@/store/user-type-store';
import type { BookingStatus } from '@/types';

// Get the appropriate router name based on user type
export const getBookingRouter = (userType: UserType) => {
  return userType === UserType.TRAVELLER 
    ? 'bookingTravelers' 
    : 'bookingCompanions';
};

// Get booking statuses for different categories
export const getBookingStatuses = (category: 'draft' | 'active' | 'past'): BookingStatus[] => {
  switch (category) {
    case 'draft':
      return ['DRAFT'];
    case 'active':
      return ['ACTIVE', 'ASSIGNED', 'CONFIRMED'];
    case 'past':
      return ['COMPLETED', 'CANCELLED'];
    default:
      return [];
  }
};

// Check if a booking is active (ACTIVE, ASSIGNED, CONFIRMED status)
export const isActiveBooking = (booking: { status: BookingStatus }) => {
  return ['ACTIVE', 'ASSIGNED', 'CONFIRMED'].includes(booking.status);
};

// Check if a booking is past (COMPLETED or CANCELLED)
export const isPastBooking = (booking: { status: BookingStatus }) => {
  return ['COMPLETED', 'CANCELLED'].includes(booking.status);
};

// Check if a booking is draft
export const isDraftBooking = (booking: { status: BookingStatus }) => {
  return booking.status === 'DRAFT';
};

// Get booking status color for UI
export const getStatusColor = (status: BookingStatus) => {
  switch (status) {
    // case 'DRAFT':
    //   return 'text-yellow-600 bg-yellow-100';
    // case 'ACTIVE':
    //   return 'text-green-600 bg-green-100';
    // case 'ASSIGNED':
    //   return 'text-blue-600 bg-blue-100';
    // case 'CONFIRMED':
    //   return 'text-purple-600 bg-purple-100';
    // case 'COMPLETED':
    //   return 'text-emerald-600 bg-emerald-100';
    // case 'CANCELLED':
    //   return 'text-red-600 bg-red-100';
    default:
      return 'text-gray-600 bg-gray-100';
  }
};

// Get booking status label for UI
export const getStatusLabel = (status: BookingStatus) => {
  switch (status) {
    case 'DRAFT':
      return 'Draft';
    case 'ACTIVE':
      return 'Active';
    case 'ASSIGNED':
      return 'Assigned';
    case 'CONFIRMED':
      return 'Confirmed';
    case 'COMPLETED':
      return 'Completed';
    case 'CANCELLED':
      return 'Cancelled';
    default:
      return status;
  }
};

// Format compensation value for display
export const formatCompensation = (value: number | null) => {
  if (!value) return '$0';
  return `$${(value / 100).toFixed(2)}`;
};

// Get booking type label based on user type
export const getBookingTypeLabel = (userType: UserType) => {
  return userType === UserType.TRAVELLER ? 'Traveler Booking' : 'Companion Booking';
}; 