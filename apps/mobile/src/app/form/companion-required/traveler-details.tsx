/* eslint-disable max-lines-per-function */
import { zodResolver } from '@hookform/resolvers/zod';
import { router, useFocusEffect, useLocalSearchParams } from 'expo-router';
import React, { useCallback, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Text, View } from 'react-native';
import { showMessage } from 'react-native-flash-message';
import { KeyboardAwareScrollView } from 'react-native-keyboard-controller';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useMutation, useQuery } from '@tanstack/react-query';
import { type z } from 'zod';

import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
import Stepper from '@/components/stepper';
import { Button, Switch, SelectionGroup } from '@/components/ui';
import GenderSelector from '@/components/ui/gender-selector';
import LanguagesSelector from '@/components/ui/languages-selector';
import { userSchema } from '@/form-schema/form-schema';
import { queryClient, trpc } from '@/lib/api';
import { account } from '@/lib/appwrite';

export default function TravelerDetails() {
  const { onboarding, id } = useLocalSearchParams();
  const [isLoading, setIsLoading] = useState(false);

  const { data: booking, isLoading: isLoadingBooking, isError: isErrorBooking } = useQuery(trpc.bookingCompanions.getById.queryOptions({
    id: id as string,
  }, {
    enabled: !!id,
  }));

  const upsertTravelerBookingMutation = useMutation(trpc.bookingCompanions.upsert.mutationOptions());

  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors },
  } = useForm<z.infer<typeof userSchema>>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      name: '',
      lastName: '',
      // phoneNo: '',
      // email: '',
      about: '',
      typeOfTraveler: 'SOLO',
      genderPreference: 'MALE',
      openToAllGenders: false,
      languages: [],
      bookingFor: 'SELF',
    },
  });

  const bookingFor = watch('bookingFor');
  const openToAllGenders = watch('openToAllGenders');

  const getDetails = async () => {
    const user = await account.get();
    console.log('Onboarding and id', onboarding, id);

    if (onboarding === 'true') {
      console.log('onboarding is true');
      try {
        // Get existing companion booking for the user
        const travelerDetails = booking;
        
        if (travelerDetails) {
          console.log('traveler details onboarding', travelerDetails);
          reset({
            name: user.name,
            lastName: travelerDetails.lastName || '',
            about: travelerDetails.about || '',
            typeOfTraveler: travelerDetails.typeOfTraveler as any,
            openToAllGenders: travelerDetails.openToAllGenders || false,
            genderPreference: travelerDetails.genderPreference as any,
            gender: travelerDetails.gender as any,
            bookingFor: travelerDetails.bookingFor,
            languages: travelerDetails.languages?.map((lang: any) => lang.id) || [],
          });
        } else {
          // Set default values for new user
          setValue('name', user.name);
        }
      } catch (error) {
        console.error('Error fetching companion details:', error);
        setValue('name', user.name);
      }
      return;
    }

    if (id) {
      try {
        // Get companion booking by ID
        const companionData = booking;
        console.log('traveller details', companionData);
        reset({
          name: companionData?.name || '',
          lastName: companionData?.lastName || '',
          about: companionData?.about || '',
          typeOfTraveler: companionData?.typeOfTraveler,
          openToAllGenders: companionData?.openToAllGenders || false,
          genderPreference: companionData?.genderPreference,
          gender: companionData?.gender,
          bookingFor: companionData?.bookingFor,
          // TODO: If selected someone else then set the field accordingly
          languages: companionData?.languages?.map((lang: any) => lang.language.id) || [],
        });
      } catch (error) {
        console.error('Error fetching companion details by ID:', error);
        showMessage({
          message: 'Error loading companion details',
          type: 'danger',
        });
      }
      return;
    }

    // Default case - set user name
    setValue('name', user.name);
  };

  const getTravellerDetails = async () => {
    if (bookingFor === 'SELF') {
      const user = await account.get();
      setValue('name', user.name);
    } else {
      setValue('name', '');
    }
  };

  // initial
  useFocusEffect(
    useCallback(() => {
      console.log('traveller details fetching');
      getDetails();
    }, [])
  );

  //if the booking is for someone else
  // useEffect(() => {
  //   console.log('traveller details bookingTravellerId');
  //   getDetails();
  // }, [bookingTravellerId]);

  //if the booking is for yourself
  // useFocusEffect(
  //   useCallback(() => {
  //     console.log('traveller details getTravellerDetails bookingFor');
  //     getTravellerDetails();
  //   }, [])
  // );

  //form submission
  const onSubmit = handleSubmit(
    async (data: z.infer<typeof userSchema>) => {
      setIsLoading(true);

      
      const companionData = {
        name: data.name,
        lastName: data.lastName || '',
        about: data.about,
        gender: data.gender,
        typeOfTraveler: data.typeOfTraveler,
        genderPreference: data.genderPreference || 'MALE',
        openToAllGender: data.openToAllGenders || false,
        languageIds: data.languages || [],
        ...(id && { id: id as string }), // Include ID if editing existing
      };

      console.log('Submitting companion data:', companionData);

      // Use tRPC to upsert companion booking via direct API call
      upsertTravelerBookingMutation.mutate(companionData, {
        onSuccess: (data) => {
          // Invalidate the getById booking query for the user after successful upsert
          queryClient.invalidateQueries({
            queryKey: [
              trpc.bookingTravelers.getById.queryKey({ id: data.id }),
              trpc.bookingTravelers.travelersAvailableForBooking.queryKey(),
              trpc.bookingTravelers.getAllByUser.queryKey(),
            ],
          });
          console.log('Companion booking saved successfully:', data);
          router.push({
            pathname: '/form/companion-required/flight-dates',
            params: {
              id: data.id,
              onboarding: onboarding ?? 'false',
            },
          });
          setIsLoading(false);
        },
        onError: (error) => {
          showMessage({
            message: 'Error saving companion booking',
            type: 'danger',
          });
          setIsLoading(false);
        },
      });
    },
    (errors) => {
      console.error('Form validation errors:', errors);
    }
  );

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
        <Stepper
          selectedStep={1}
          onPress={() => {
            router.back();
          }}
          onPress2={() => {
            router.back();
          }}
        />
        <View className="flex-1 gap-8">
          <View className="mt-4 gap-2">
            <Text className=" font-PoppinsBold text-[20px] font-bold leading-[30px] dark:text-black-50">
              Add Travelers Details
            </Text>
            <Text className="dark: font-inter text-sm font-medium leading-5 text-black-300">
              Add travelers details to connect with companion
            </Text>
          </View>

          <KeyboardAwareScrollView
            className="flex-1"
            showsVerticalScrollIndicator={false}
          >
            <View className="flex-1 gap-[18px] ">
              <InputLabelled label="Are you Booking for? *">
                <Controller
                  control={control}
                  name="bookingFor"
                  rules={{
                    validate: (value) => {
                      if (value != 'FATHER' || 'MOTHER' || 'RELATIVE') {
                        return 'Please select one of the options.';
                      }
                      return true;
                    },
                  }}
                  render={({ field: { onChange, value } }) => (
                    <SelectionGroup
                      options={[
                        { value: 'SELF', label: 'Yourself' },
                        { value: 'FATHER', label: 'Someone else' },
                      ]}
                      value={value === 'SELF' ? 'SELF' : 'FATHER'}
                      onChange={onChange}
                      error={errors.bookingFor?.message}
                    />
                  )}
                />
                {/* Show sub-options when "Someone else" is selected */}
                {bookingFor !== 'SELF' && (
                  <View className="mt-2">
                    <SelectionGroup
                      options={[
                        { value: 'FATHER', label: 'Father' },
                        { value: 'MOTHER', label: 'Mother' },
                        { value: 'RELATIVE', label: 'Relative' },
                      ]}
                      value={bookingFor}
                      onChange={(newValue) => {
                        if (typeof newValue === 'string') {
                          setValue('bookingFor', newValue as 'FATHER' | 'MOTHER' | 'RELATIVE');
                        }
                      }}
                      layout="horizontal"
                    />
                  </View>
                )}
              </InputLabelled>

              <InputLabelled label="First Name *">
                <Controller
                  control={control}
                  name="name"
                  render={({ field: { onChange, value } }) => (
                    <InputText
                      placeholder=""
                      value={value ?? ''}
                      onChangeText={onChange}
                    />
                  )}
                />
              </InputLabelled>
              <InputLabelled label="Last Name (Hidden from Companion)">
                <Controller
                  control={control}
                  name="lastName"
                  render={({ field: { onChange, value } }) => (
                    <InputText
                      placeholder=""
                      value={value ?? ''}
                      onChangeText={onChange}
                    />
                  )}
                />
              </InputLabelled>
              <InputLabelled label="Are you a ______ Traveler?">
                <Controller
                  control={control}
                  name="typeOfTraveler"
                  render={({ field: { onChange, value } }) => (
                    <SelectionGroup
                      options={[
                        { value: 'SOLO', label: 'SOLO' },
                        { value: 'FAMILY', label: 'FAMILY' },
                        { value: 'GROUP', label: 'Group' },
                      ]}
                      value={value}
                      onChange={onChange}
                      error={errors.typeOfTraveler?.message}
                    />
                  )}
                />
              </InputLabelled>

              <InputLabelled label="Your Gender?">
                <Controller
                  control={control}
                  name="gender"
                  render={({ field: { value, onChange } }) => (
                    <GenderSelector value={value} onChange={onChange} />
                  )}
                />
              </InputLabelled>
              {errors.gender && (
                <Text className="text-red-500">
                  {errors.gender.message?.toString()}
                </Text>
              )}

              <View>
                <View className="flex-row items-center justify-between">
                  <InputLabelled
                    label="Gender Preference"
                    className="flex-row items-center gap-4"
                  >
                    <Controller
                      control={control}
                      name="openToAllGenders"
                      render={({ field: { onChange, value } }) => (
                        <Switch
                          accessibilityLabel=""
                          label="Open for all genders"
                          className="text-black-300"
                          checked={value}
                          onChange={(checked) => {
                            onChange(checked);
                          }}
                        />
                      )}
                    />
                  </InputLabelled>
                </View>
                {!openToAllGenders && (
                  <Controller
                    control={control}
                    name="genderPreference"
                    render={({ field: { onChange, value } }) => (
                      <SelectionGroup
                        options={[
                          { value: 'MALE', label: 'Male' },
                          { value: 'FEMALE', label: 'Female' },
                          { value: 'OTHER', label: 'Other' },
                        ]}
                        value={value ?? 'MALE'}
                        onChange={onChange}
                        error={errors.genderPreference?.message}
                      />
                    )}
                  />
                )}
              </View>

              <InputLabelled label="Language Traveler Speaks">
                <Controller
                  control={control}
                  name="languages"
                  render={({ field: { onChange, value } }) => (
                    <LanguagesSelector
                      languageIds={value ?? []}
                      setLanguageIds={onChange}
                    />
                  )}
                />
              </InputLabelled>
              {errors.languages && (
                <Text className="text-red-500">
                  {errors.languages.message?.toString()}
                </Text>
              )}

              <InputLabelled label="Write about you">
                <Controller
                  control={control}
                  name="about"
                  render={({ field: { onChange, value } }) => (
                    <InputText
                      placeholder="Write about yourself"
                      value={value}
                      onChangeText={onChange}
                      numberOfLines={5}
                      multiline={true}
                      className="h-24"
                      textAlignVertical="top"
                    />
                  )}
                />
              </InputLabelled>

              {errors.about && (
                <Text className="text-red-500">
                  {errors.about.message?.toString()}
                </Text>
              )}
            </View>
          </KeyboardAwareScrollView>
        </View>

        <Button
          variant="secondary"
          label="Next"
          className="mb-2 mt-8"
          loading={isLoading}
          onPress={onSubmit}
        />
      </View>
    </SafeAreaView>
  );
}
