import { zodResolver } from '@hookform/resolvers/zod';
import {
  type DateTimePickerEvent,
} from '@react-native-community/datetimepicker';
import { addDays, format } from 'date-fns';
import { router, useFocusEffect } from 'expo-router';
import { useLocalSearchParams } from 'expo-router';
import React, { useCallback, useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  Alert,
  Image,
  Pressable,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { showMessage } from 'react-native-flash-message';
import { SafeAreaView } from 'react-native-safe-area-context';
import { type z } from 'zod';

import InputErrorMsg from '@/components/input-error-msg';
import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
import Stepper from '@/components/stepper';
import { Button } from '@/components/ui';
import AirportSelectionInput from '@/components/ui/airport-selection-input';
import { flightSchema } from '@/form-schema/form-schema';
import { useBookingStore } from '@/store/booking-store';
import TimezoneSelectionInput from '@/components/ui/timezone-selector';
import CustomDateTimePicker from '@/components/ui/custom-date-time-picker';
import { ITimezone, TIMEZONES } from '@/lib/time-zones';
import dayjs from 'dayjs';
import timezonePlugin from 'dayjs/plugin/timezone';
import utcPlugin from 'dayjs/plugin/utc';
import { useMutation, useQuery } from '@tanstack/react-query';
import { queryClient, trpc } from '@/lib/api';

dayjs.extend(timezonePlugin);
dayjs.extend(utcPlugin);

export default function TravelerFlightDates() {
  const { id, onboarding } = useLocalSearchParams();
  const [date, setDate] = useState(new Date());
  const [time, setTime] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [endTime, setEndTime] = useState(new Date());
  // const [swapped, setSwapped] = useState(false);
  const [loading, setLoading] = useState(false);
  const [timezone, setTimezone] = useState<ITimezone | null>(null);


  const { data: booking, isLoading: isLoadingBooking, isError: isErrorBooking } = useQuery(trpc.bookingCompanions.getById.queryOptions({
    id: id as string,
  }, {
    enabled: !!id,
  }));

  const upsertFlightBookingMutation = useMutation(trpc.bookingCompanions.upsertFlightDetails.mutationOptions());


  const onDateChange = (
    event: DateTimePickerEvent,
    selectedDate: Date | undefined
  ) => {
    const currentDate = selectedDate || date;
    // setShowDatePicker(Platform.OS === 'android' ? false : true);
    setDate(currentDate);

    const formattedDate = format(currentDate, 'yyyy-MM-dd');
    setValue('flightDate', formattedDate, { shouldValidate: true });
  };

  const onTimeChange = (
    event: DateTimePickerEvent,
    selectedTime: Date | undefined
  ) => {
    const currentTime = selectedTime || time;
    // setShowTimePicker(Platform.OS === 'android' ? false : true);
    setTime(currentTime);

    const formattedTime = currentTime.toTimeString().split(' ')[0];
    setValue('flightTime', formattedTime, { shouldValidate: true });
  };

  const onEndDateChange = (
    event: DateTimePickerEvent,
    selectedDate: Date | undefined
  ) => {
    const currentDate = selectedDate || endDate;
    // setShowEndDatePicker(Platform.OS === 'android' ? false : true);
    setEndDate(currentDate);

    const formattedDate = format(currentDate, 'yyyy-MM-dd');
    setValue('flightEndDate', formattedDate, { shouldValidate: true });
  };

  const onEndTimeChange = (
    event: DateTimePickerEvent,
    selectedTime: Date | undefined
  ) => {
    const currentTime = selectedTime || endTime;
    // setShowEndTimePicker(Platform.OS === 'android' ? false : true);
    setEndTime(currentTime);

    const formattedTime = currentTime.toTimeString().split(' ')[0];
    setValue('flightEndTime', formattedTime, { shouldValidate: true });
  };

  const selectedAirports = useBookingStore((state) =>
    state.selectedAirports.sort((a, b) => Number(a.index) - Number(b.index))
  );
  const setFlightBookingid = useBookingStore(
    (state) => state.setFlightBookingid
  );
  const setSelectedAirports = useBookingStore(
    (state) => state.setSelectedAirports
  );
  const removeAirport = useBookingStore((state) => state.removeAirport);
  const addEmptyAirport = useBookingStore((state) => state.addEmptyAirport);

  // Function to add a new flight to the list
  const addNewFlight = () => {
    addEmptyAirport();
    // const newCount = countOfInterConnectedAirport + 1;
    // setCountOfInterConnectedAirport();
    // setSelectedAirports({
    //   id: newCount.toString(),
    //   name: '',
    //   index: newCount.toString(),
    // });
  };

  const { control, handleSubmit, getValues, reset, setValue } = useForm<
    z.infer<typeof flightSchema>
  >({
    resolver: zodResolver(flightSchema),
    defaultValues: {
      // flightCompany: '',
      flightPNR: '',
      flightDate: format(addDays(new Date(), 1), 'yyyy-MM-dd'),
      flightTime: new Date().toTimeString().split(' ')[0],
      flightEndDate: format(addDays(new Date(), 1), 'yyyy-MM-dd'),
      flightEndTime: new Date().toTimeString().split(' ')[0],
    },
  });

  const onSubmit = handleSubmit(
    async (data) => {
      const allAirportSelected = selectedAirports.reduce(
        (prev, curr) => prev && curr.name !== '',
        true
      );

      console.log('allAirportSelected', selectedAirports);

      if (!allAirportSelected) {
        Alert.alert('Please select all destinations');
        return;
      }

      if (!timezone) {
        Alert.alert('Please select a timezone');
        return;
      }

      setLoading(true);
      console.log('submit with data', data, date, time, endDate, endTime, timezone?.tzCode);

      // Use dayjs with timezone plugin
      // Assumes dayjs and dayjs/plugin/timezone, dayjs/plugin/utc are imported and extended

      // Parse start date and time
      const flightDateTimeStr = `${data.flightDate}T${data.flightTime}`;
      console.log('flightDateTimeStr', flightDateTimeStr);
      const flightDateTime = dayjs.tz(dayjs(flightDateTimeStr, 'YYYY-MM-DDTHH:mm:ss'), timezone?.tzCode || 'UTC').second(0);
      console.log('flightDateTime', flightDateTime);
      // .second(0) ensures seconds are set to 0

      // Parse end date and time
      const flightEndDateTimeStr = `${data.flightEndDate}T${data.flightEndTime}`;
      const flightEndTime = dayjs.tz(dayjs(flightEndDateTimeStr, 'YYYY-MM-DDTHH:mm:ss'), timezone?.tzCode || 'UTC').second(0);

      // For logging/debugging
      console.log(
        'flightDateTime',
        flightDateTime.toISOString(),
        'flightEndTime',
        flightEndTime.toISOString()
      );

      console.log('endDateObj > dateObj', flightDateTime, flightEndTime);

      if (flightEndTime < flightDateTime) {
        Alert.alert('End time should be greater than start time');
        // showMessage({
        //   message: 'End time cannot be earlier than start time',
        //   type: 'danger',
        // });
        setLoading(false);
        return;
      }

      console.log('endDateObj > dateObj', flightEndTime, flightDateTime);

      try {
        const searchField = `${selectedAirports.map((airport) => airport.id).join('-')}-${data.flightPNR}-${flightDateTime.unix()}-${flightEndTime.unix()}`;
        
        const flightBooking = {
          id: id as string,
          flightPNR: data.flightPNR,
          flightTime: flightDateTime.toDate(),
          flightEndTime: flightEndTime.toDate(),
          timezone: timezone?.tzCode,
          bookingDestinations: selectedAirports.map((airport) => ({
            order: Number(airport.index),
            airports: airport.id,
          })),
          searchField,
        }

        upsertFlightBookingMutation.mutate(flightBooking, {
          onSuccess: (data) => {
            // Invalidate the getById booking query for the user after successful upsert
            queryClient.invalidateQueries({
              queryKey: [
                trpc.bookingCompanions.getById.queryKey({ id: id as string }),
                trpc.bookingCompanions.companionsAvailableForBooking.queryKey(),
                trpc.bookingCompanions.getAllByUser.queryKey(),
              ],
            }); 
            router.push({
              pathname: '/form/companion-required/photo-uploads',
              params: {
                id,
                onboarding: onboarding ?? 'false',
              },
            });
          },
          onError: (error) => {
            console.error('Error saving flight booking:', error);
          }
        });

        
      } catch (err: any) {
        setLoading(false);
        showMessage({
          message: 'Error in creating flight booking',
          type: 'danger',
        });
        console.log('error in flight booking is', err);
      }
      setLoading(false);
      
    },
    (errors) => {
      console.log('errors during form submission', errors, getValues());
    }
  );

  const getTravelerDetails = async () => {
    if (!booking) return;
    const travelerDetails = booking;
    console.log('flight details', travelerDetails);
    setDate(new Date(travelerDetails.flightTime || new Date()));
    setTime(new Date(travelerDetails.flightTime || new Date()));
    setEndDate(new Date(travelerDetails.flightEndTime || new Date()));
    setEndTime(new Date(travelerDetails.flightEndTime || new Date()));
    reset({
      flightPNR: travelerDetails.flightPNR || '',
      flightDate: format(travelerDetails.flightTime || new Date(), 'yyyy-MM-dd'),
      flightTime: format(travelerDetails.flightTime || new Date(), 'HH:mm'),
      flightEndDate: format(travelerDetails.flightEndTime || new Date(), 'yyyy-MM-dd'),
      flightEndTime: format(travelerDetails.flightEndTime || new Date(), 'HH:mm'),
    });
    travelerDetails.timezone &&
      setTimezone(
        TIMEZONES.find((t) => t.tzCode === travelerDetails.timezone) ?? null
      );
    console.log(
      'selectedAirports',
      travelerDetails.destinations[0].airport
    );
    travelerDetails.destinations.forEach((destination: any) => {
      setSelectedAirports({
        id: destination.airport.id,
        name: destination.airport.name,
        index: destination.order,
      });
    });
  };

  useFocusEffect(
    useCallback(() => {
      console.log('flight dates getTravelerDetails bookingFor');
      getTravelerDetails();
    }, [])
  );

  useEffect(() => {
    // Get the device's timezone
    const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    // Set the timezone as default if no timezone is selected
    if (!timezone) {
      const defaultTimezone = TIMEZONES.find((t) => t.tzCode === timeZone);
      if (defaultTimezone) {
        setTimezone(defaultTimezone);
      }
    }
  }, []);

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
        <Stepper
          selectedStep={1}
          onPress={() => {
            router.back();
          }}
          onPress2={() => {
            router.back();
          }}
        />

        <View className="flex-1 gap-8">
          <View className="mt-4 gap-2">
            <Text className="font-PoppinsBold text-[20px] font-bold leading-[30px] text-black-950 dark:text-black-0">
              Add Flight Details
            </Text>
            <Text className="font-inter text-[14px] font-medium leading-5 text-black-950 dark:text-black-300">
              Add traveler's flight details to connect with companion
            </Text>
          </View>

          <ScrollView
            className="mb-5 w-full flex-1"
            showsVerticalScrollIndicator={false}
          >
            <View className="w-full flex-1 gap-5">
              <View>
                <View className="flex-row items-center justify-between">
                  <Text className=" font-PoppinsMedium text-[16px] font-medium leading-[24px] text-black-950 dark:text-black-50">
                    From & To Destination
                  </Text>
                  {/* plus icon */}
                  <TouchableOpacity onPress={addNewFlight}>
                    <View className="rounded-[8px] border border-customborder bg-black-0 p-[6px] dark:bg-primary-950">
                      <Image
                        source={require('../../../../assets/images/plus.png')}
                        resizeMode="contain"
                        style={{ height: 20, width: 20 }}
                      />
                    </View>
                  </TouchableOpacity>
                </View>

                <View className="flex-row items-center gap-2">
                  <View className="mt-2 flex-1 flex-col">
                    {selectedAirports.map((airport, index) => (
                      <AirportSelectionInput
                        airport={airport}
                        index={index}
                        key={index}
                        setAirport={(airport) => {
                          setSelectedAirports({
                            ...airport,
                          });
                        }}
                        removeAirport={removeAirport}
                        totalFlights={selectedAirports.length}
                      />
                    ))}
                  </View>
                </View>
              </View>

              {/* <InputLabelled label="Airline Company">
                <Controller
                  name="flightCompany"
                  control={control}
                  render={({ field: { value, onChange }, fieldState }) => (
                    <View className="mt-2">
                      <ControlledSelect
                        name="flightCompany"
                        control={control}
                        options={AIRLINES.map((airline) => ({
                          label: airline.name,
                          value: airline.name,
                        }))}
                        placeholder="Select Airline"
                        value={value ?? ''}
                        onSelect={onChange}
                      />
                    </View>
                  )}
                />
              </InputLabelled> */}

              <InputLabelled label="Flight Number">
                <Controller
                  name="flightPNR"
                  control={control}
                  render={({ field: { value, onChange }, fieldState }) => (
                    <>
                      <InputText
                        placeholder=""
                        value={value}
                        onChangeText={(text) => {
                          //replace all the characters except 0-9 with ''
                          // const numericText = text.replace(/[^0-9]/g, '');
                          onChange(text);
                        }}
                        disabled={false}
                        // iconSourceFirst={require('../../../../assets/images/user-text-input.svg')}
                        iconStyleFirst={{ height: 16, width: 13 }}
                      />
                      {fieldState.error?.message && (
                        <InputErrorMsg message={fieldState.error.message} />
                      )}
                    </>
                  )}
                />
              </InputLabelled>
              <View className="flex-row items-center gap-4">
                <InputLabelled label="Start Date">
                  <CustomDateTimePicker
                    value={date}
                    mode="date"
                    onChange={onDateChange}
                  />
                </InputLabelled>

                <InputLabelled label="Start Time">
                  <CustomDateTimePicker
                    value={time}
                    mode="time"
                    onChange={onTimeChange}
                  />
                </InputLabelled>
              </View>
              <View className="flex-row items-center gap-4">
                <InputLabelled label="End Date">
                  <CustomDateTimePicker
                    value={endDate}
                    mode="date"
                    onChange={onEndDateChange}
                  />
                </InputLabelled>

                <InputLabelled label="End Time">
                  <CustomDateTimePicker
                    value={endTime}
                    mode="time"
                    onChange={onEndTimeChange}
                  />
                </InputLabelled>
              </View>
              <InputLabelled label="Timezone">
                <TimezoneSelectionInput
                  placeholder=""
                  timezone={timezone?.tzCode ?? ''}
                  setTimezone={(tz) => {
                    setTimezone(TIMEZONES.find((t) => t.tzCode === tz) ?? null);
                  }}
                />
              </InputLabelled>
            </View>
          </ScrollView>
        </View>

        <View className="mb-5 flex-row items-center justify-between px-5">
          <Pressable
            onPress={() =>
              router.replace({
                pathname: '/form/companion-required/traveler-details',
                params: {
                  id,
                  onboarding: onboarding ?? 'false',
                },
              })
            }
          >
            <View className="rounded-[12px] px-[24px] py-[14px] text-black-950 dark:bg-primary-950">
              <Text className="font-inter text-[16px] font-medium leading-6 text-black-950 dark:text-black-50">
                Back
              </Text>
            </View>
          </Pressable>
          <Button
            variant="secondary"
            label="Next"
            className="w-40"
            loading={upsertFlightBookingMutation.isPending}
            onPress={onSubmit}
          />
          {/* <Pressable onPress={onSubmit}>
            <View className="rounded-[12px] px-[24px] py-[14px] text-black-950 dark:bg-primary-50">
              <Text className="font-inter text-[16px] font-medium leading-6 text-black-950 dark:text-blue">
                Next
              </Text>
            </View>
          </Pressable> */}
        </View>
      </View>
    </SafeAreaView>
  );
}
