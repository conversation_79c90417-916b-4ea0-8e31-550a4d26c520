import { Stack } from 'expo-router';

export default function CompanionRequiredLayout() {
  return (
    <Stack initialRouteName="traveler-details">
      <Stack.Screen name="traveler-details" options={{ headerShown: false }} />
      <Stack.Screen name="search" options={{ headerShown: false }} />
      <Stack.Screen name="flight-dates" options={{ headerShown: false }} />
      <Stack.Screen
        name="destination-search"
        options={{ headerShown: false }}
      />
      <Stack.Screen name="photo-uploads" options={{ headerShown: false }} />

      <Stack.Screen
        name="available-companion"
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="profilepage-companion"
        options={{ headerShown: false }}
      />
    </Stack>
  );
}
