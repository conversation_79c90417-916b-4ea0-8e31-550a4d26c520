import { Env } from '@env';
import { router, useFocusEffect } from 'expo-router';
import { useLocalSearchParams } from 'expo-router';
import React, { useCallback, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { ScrollView, Text, View } from 'react-native';
import { showMessage } from 'react-native-flash-message';
import { SafeAreaView } from 'react-native-safe-area-context';
import { type z } from 'zod';

import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
import Stepper from '@/components/stepper';
import { Button } from '@/components/ui';
import ImagePickerComponent from '@/components/ui/image-picker';
// import PassportPhotoPicker from '@/components/ui/passport-photo-picker';
import { type bookingSchema } from '@/form-schema/form-schema';
import { databases } from '@/lib/appwrite';
import { updateUserData } from '@/lib/user-data-service';
import { queryClient, trpc } from '@/lib/api';
import { useMutation, useQuery } from '@tanstack/react-query';




export default function PhotoUploads() {
  const [loadingPhotoUpload, setLoadingPhotoUpload] = useState(false);
  const [loadingPassportImage, setLoadingPassportImage] = useState(false);
  const { selectedStep: initialStep, id, onboarding } = useLocalSearchParams();

  const [selectedStep, setSelectedStep] = useState(Number(initialStep) || 0);

  const { data: booking, isLoading: isLoadingBooking, isError: isErrorBooking } = useQuery(trpc.bookingCompanions.getById.queryOptions({
    id: id as string,
  }, {
    enabled: !!id,
  }));

  const upsertPhotoUploadsMutation = useMutation(trpc.bookingCompanions.upsertPhotoUploads.mutationOptions());


  const {
    control,
    formState: { errors },
    setValue,
    getValues,
    handleSubmit,
  } = useForm<z.infer<typeof bookingSchema>>({
    defaultValues: {
      travelersPhoto: '',
      passportPhoto: '',
      compensationValue: '',
    },
  });

  const getTravelerDetails = async () => {
    if (!booking) {
      return;
    }
    try {
      const travelerDetails = booking;
      console.log('photo uploads details', travelerDetails);

      if (travelerDetails) {
        setValue('travelersPhoto', travelerDetails.travelersPhoto || '');
        setValue('passportPhoto', travelerDetails.passportPhoto || '');
        if (travelerDetails.compensationValue) {
          setValue(
            'compensationValue',
              (travelerDetails.compensationValue / 100).toString()
          );
        }
      }
    } catch (err: any) {
      showMessage({
        message: 'Error fetching booking details',
        type: 'danger',
      });
      console.log('error in fetching booking is', err);
    }
  };

  useFocusEffect(
    useCallback(() => {
      getTravelerDetails();
    }, [])
  );



  const onSubmit = handleSubmit(
    async (data) => {
      console.log('data is', data, id);
      if (data.compensationValue === '') {
        showMessage({
          message: 'Please fill companion compensation value',
          type: 'danger',
        });
        return;
      }
      if (data.travelersPhoto === '') {
        showMessage({
          message: 'Please upload travelers photo',
          type: 'danger',
        });
        return;
      }
      try {
        setLoadingPhotoUpload(true);
        const booking = await upsertPhotoUploadsMutation.mutateAsync({
          id: id as string,
          compensationValue: Math.round(
            parseFloat(data.compensationValue) * 100
          ),
          passportPhoto: data.passportPhoto,
          travelersPhoto: data.travelersPhoto,
        });
        queryClient.invalidateQueries({
          queryKey: [
            trpc.bookingCompanions.getById.queryKey({ id: id as string }),
            trpc.bookingCompanions.companionsAvailableForBooking.queryKey(),
            trpc.bookingCompanions.getAllByUser.queryKey(),
          ],
        });
        console.log('onboarding is', onboarding);
        if (onboarding === 'true') {
          await updateUserData({
            travellerProfile: true,
          });

          // return;
        }
        router.dismissTo('/home');
        showMessage({
          message: 'Booking created successfully',
          type: 'success',
        });
        console.log('booking created is', booking);
      } catch (err) {
        console.log('error in creating booking is', err);
        showMessage({
          message: 'Error in creating booking',
          type: 'danger',
        });
      } finally {
        setLoadingPhotoUpload(false);
      }
    },
    (errors) => {
      console.log('errors is', errors);
      showMessage({
        message: 'Please fill all the fields',
        type: 'danger',
      });
    }
  );



  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
        <Stepper
          selectedStep={selectedStep}
          onPress={() => {
            router.back();
          }}
          onPress2={() => {
            router.back();
          }}
        />



        <View className="flex-1 gap-8">
          <View className="mt-4 ">
            <Text className="font-PoppinsBold text-xl font-bold leading-[30px] text-black-950 dark:text-black-50">
              Add Photo & Set Value
            </Text>
            <Text className="font-inter font-medium leading-5 dark:text-black-300">
              Add traveler's flight details to connect with companion
            </Text>
          </View>
          <ScrollView className="mb-10 flex-1">
            <View className="flex-1 justify-between ">
              <View>
                <Text className="font-PoppinsRegular text-[16px] font-normal leading-[24px] text-black-950 dark:text-black-50">
                  Upload Travelers Photo
                </Text>
                <View className="mt-2 flex-row items-center gap-2">
                  <Controller
                    control={control}
                    name="travelersPhoto"
                    render={({ field: { value }, fieldState: { error } }) => (
                      <>
                        <ImagePickerComponent
                          image={value}
                          onImageSelected={(url) => setValue('travelersPhoto', url)}
                          onImageRemoved={() => setValue('travelersPhoto', '')}
                          loading={loadingPhotoUpload}
                          onLoadingChange={setLoadingPhotoUpload}
                          filePrefix="traveller"
                          documentId={id as string}
                        />
                        {error?.message && (
                          <Text className="text-red-500">
                            {error.message?.toString()}
                          </Text>
                        )}
                      </>
                    )}
                  />
                </View>

                <View className="mt-6 gap-6">
                  {/* <InputLabelled label="Upload Passport">
                    <Controller
                      control={control}
                      name="passportPhoto"
                      render={({ field: { value }, fieldState: { error } }) => (
                        <>
                          <PassportPhotoPicker
                            value={value}
                            onImageSelected={(url) => setValue('passportPhoto', url)}
                            onImageRemoved={() => setValue('passportPhoto', '')}
                            loading={loadingPassportImage}
                            onLoadingChange={setLoadingPassportImage}
                            documentId={id as string}
                          />
                          {error?.message && (
                            <Text className="text-red-500">
                              {error.message?.toString()}
                            </Text>
                          )}
                        </>
                      )}
                    />

                    <Text className="mt-2 font-inter text-[11px] font-normal text-black-300 dark:text-black-100">
                      Passport details are for verification purpose only and it
                      will not be share with other users.
                    </Text>
                  </InputLabelled> */}

                  <InputLabelled label="Companion's Compensation Amount (US$)">
                    <Controller
                      control={control}
                      name="compensationValue"
                      render={({
                        field: { value, onChange },
                        fieldState: { error },
                      }) => (
                        <>
                          <InputText
                            value={value}
                            onChangeText={(text) => {
                              let doubleText = text.replace(/[^0-9.]/g, '');
                              const parts = doubleText.split('.');
                              if (parts.length > 2) {
                                doubleText =
                                  parts[0] + '.' + parts.slice(1).join('');
                              }
                              onChange(doubleText);
                            }}
                            placeholder=""
                          />
                          {error?.message && (
                            <Text className="text-red-500">
                              {error.message?.toString()}
                            </Text>
                          )}
                        </>
                      )}
                    />
                  </InputLabelled>
                    {errors.compensationValue && (
                    <Text className="text-red-500">
                      {errors.compensationValue.message?.toString()}
                    </Text>
                  )}
                </View>
              </View>
            </View>
          </ScrollView>
        </View>
        <View className="mb-4">
          <Button
            variant="secondary"
            label="Find Companion"
            loading={loadingPhotoUpload || loadingPassportImage}
            onPress={onSubmit}
          />
        </View>
      </View>
    </SafeAreaView>
  );
}
