import { zod<PERSON>esolver } from '@hookform/resolvers/zod';
import { router, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { ScrollView, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { type z } from 'zod';
import { useQuery, useMutation } from '@tanstack/react-query';

import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
import Stepper from '@/components/stepper';
import { Button, SelectionGroup, Switch } from '@/components/ui';
import { companionSchema } from '@/form-schema/form-schema';
import { userTypeStore } from '@/store/user-type-store';
import LanguagesSelector from '@/components/ui/languages-selector';
import GenderSelector from '@/components/ui/gender-selector';
import { queryClient, trpc } from '@/lib/api';

const CompanionDetails = () => {
  const [isLoading, setIsLoading] = useState(false);
  const params = useLocalSearchParams();
  const user = userTypeStore((state) => state.user);

  // tRPC mutations and queries
  const upsertCompanionMutation = useMutation(trpc.bookingTravelers.upsert.mutationOptions());
  const { data: existingCompanion, isLoading: isLoadingCompanion } = useQuery(trpc.bookingTravelers.getById.queryOptions(
    { id: params.id as string },
    { enabled: !!params.id }
  ));

  const {
    watch,
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<z.infer<typeof companionSchema>>({
    resolver: zodResolver(companionSchema),
    defaultValues: {
      name: '',
      lastName: '',
      about: '',
      typeOfTraveler: 'SOLO',
      genderPreference: 'MALE',
      openToAllGender: false,
      languages: [],
      gender: 'MALE',
    },
  });

  useEffect(() => {
    if (user) {
      setValue('name', user.name);
    }
  }, [user, setValue]);

  useEffect(() => {
    if (existingCompanion) {
      reset({
        name: existingCompanion.name || user?.name || '',
        lastName: existingCompanion.lastName || '',
        about: existingCompanion.about || '',
        typeOfTraveler: existingCompanion.typeOfTraveler || 'SOLO',
        genderPreference: existingCompanion.genderPreference || 'MALE',
        openToAllGender: !!existingCompanion.openToAllGenders,
        languages: existingCompanion.languages && existingCompanion.languages.length > 0 
          ? existingCompanion.languages.map((lang: any) => lang.language.id) 
          : [],
        gender: existingCompanion.gender || 'MALE',
      });
    }
  }, [existingCompanion, user, reset, setValue]);

  const onSubmit = handleSubmit(
    async (data: z.infer<typeof companionSchema>) => {
      console.log('data is', data);
      setIsLoading(true);
      try {
        //prepare the companion data
        const companionData = {
          name: data.name,
          lastName: data.lastName || '',
          about: data.about,
          typeOfTraveler: data.typeOfTraveler,
          gender: data.gender,
          openToAllGender: !!data.openToAllGender,
          genderPreference: data.genderPreference,
          languageIds: data.languages && data.languages.length > 0 ? data.languages : [],
          ...(params.id && { id: params.id as string }), // Include ID if editing existing
        };

        console.log('Submitting companion data:', companionData);

        // Use tRPC to upsert companion booking
        upsertCompanionMutation.mutate(companionData, {
          onSuccess: (data) => {
            console.log('Companion booking saved successfully:', data);
            // Invalidate the getById booking query for the user after successful upsert
            queryClient.invalidateQueries({
              queryKey: [
                trpc.bookingTravelers.getById.queryKey({ id: data.id }),
                trpc.bookingCompanions.companionsAvailableForBooking.queryKey(),
                trpc.bookingCompanions.getAllByUser.queryKey(),
              ],
            }); 
            router.push({
              pathname: '/form/ready-as-companion/flight-details',
              params: {
                id: data.id,
              },
            });
            setIsLoading(false);
          },
          onError: (error) => {
            console.error('Error saving companion details:', error);
            setIsLoading(false);
          },
        });
      } catch (error) {
        console.error('Error saving user details:', error);
        setIsLoading(false);
      }
    },
    (errors) => {
      console.log('errors in form submission is', errors);
    }
  );

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
        <Stepper
          selectedStep={1}
          onPress={() => {
            router.back();
          }}
          onPress2={() => {
            router.back();
          }}
        />
        <View className="flex-1 gap-8">
          <View className="mt-8 gap-2">
            <Text className=" font-PoppinsBold text-[20px] font-bold leading-[30px] dark:text-black-50">
              Add Companion Details
            </Text>
            <Text className="dark: font-inter text-sm font-medium leading-5 text-black-300">
              Add Companion details to connect with traveler
            </Text>
          </View>

          <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
            <View className="flex-1 gap-[18px] ">
              <InputLabelled label="First Name *">
                <Controller
                  control={control}
                  name="name"
                  render={({ field: { value, onChange } }) => (
                    <InputText
                      value={value}
                      onChangeText={onChange}
                      placeholder="Enter your name"
                      placeholderTextColor={''}
                      iconSourceFirst={require('../../../../assets/images/user-text-input.svg')}
                      iconStyleFirst={{
                        height: 20,
                        width: 20,
                        tintColor: 'white',
                      }}
                    />
                  )}
                />
              </InputLabelled>
              {errors.name && (
                <Text className="text-red-500">
                  {errors.name.message?.toString()}
                </Text>
              )}
              <InputLabelled label="Last Name (Hidden from Traveller)">
                <Controller
                  control={control}
                  name="lastName"
                  render={({ field: { value, onChange } }) => (
                    <InputText
                      value={value}
                      onChangeText={onChange}
                      placeholder=""
                      placeholderTextColor={''}
                      iconSourceFirst={require('../../../../assets/images/user-text-input.svg')}
                      iconStyleFirst={{
                        height: 20,
                        width: 20,
                        tintColor: 'white',
                      }}
                    />
                  )}
                />
              </InputLabelled>
              {errors.lastName && (
                <Text className="text-red-500">
                  {errors.lastName.message?.toString()}
                </Text>
              )}

              <InputLabelled label="Are you a ______ Traveler?">
                <Controller
                  name="typeOfTraveler"
                  control={control}
                  render={({ field: { onChange, value } }) => (
                    <SelectionGroup
                      options={[
                        { value: 'SOLO', label: 'Solo' },
                        { value: 'FAMILY', label: 'Family' },
                        { value: 'GROUP', label: 'Group' },
                      ]}
                      value={value}
                      onChange={onChange}
                      error={errors.typeOfTraveler?.message}
                    />
                  )}
                />
              </InputLabelled>

              <InputLabelled label="Your Gender?">
                <Controller
                  control={control}
                  name="gender"
                  render={({ field: { value, onChange } }) => (
                    <GenderSelector value={value} onChange={onChange} />
                  )}
                />
              </InputLabelled>
              {errors.gender && (
                <Text className="text-red-500">
                  {errors.gender.message?.toString()}
                </Text>
              )}

              <View>
                <View className="flex-row items-center justify-between">
                  <InputLabelled
                    label="Gender Preference"
                    className="flex-row items-center gap-4"
                  >
                    <Controller
                      control={control}
                      name="openToAllGender"
                      render={({ field: { onChange, value } }) => (
                        <Switch
                          accessibilityLabel=""
                          label="Open for all genders"
                          className="text-black-300"
                          checked={value}
                          onChange={(checked) => {
                            onChange(checked);
                          }}
                        />
                      )}
                    />
                  </InputLabelled>
                </View>
                {!watch('openToAllGender') && (
                  <Controller
                    control={control}
                    name="genderPreference"
                    render={({ field: { onChange, value } }) => (
                      <SelectionGroup
                        options={[
                          { value: 'MALE', label: 'Male' },
                          { value: 'FEMALE', label: 'Female' },
                          { value: 'OTHER', label: 'Other' },
                        ]}
                        value={value}
                        onChange={onChange}
                        error={errors.genderPreference?.message}
                      />
                    )}
                  />
                )}
              </View>

              <InputLabelled label="Language Companion Speaks *">
                <Controller
                  control={control}
                  name="languages"
                  render={({ field: { value, onChange } }) => (
                    <LanguagesSelector
                      languageIds={value ?? []}
                      setLanguageIds={onChange}
                    />
                  )}
                />
              </InputLabelled>
              {errors.languages && (
                <Text className="text-red-500">
                  {errors.languages.message?.toString()}
                </Text>
              )}

              <InputLabelled label="Write About You">
                <Controller
                  control={control}
                  name="about"
                  render={({ field: { onChange, value } }) => (
                    <InputText
                      placeholder="Write about yourself"
                      value={value}
                      onChangeText={onChange}
                    />
                  )}
                />
              </InputLabelled>
              {errors.about && (
                <Text className="text-red-500">
                  {errors.about.message?.toString()}
                </Text>
              )}
            </View>
          </ScrollView>
        </View>

        <Button
          variant="secondary"
          label="Next"
          className="mb-2 mt-8"
          loading={isLoading}
          onPress={onSubmit}
        />
      </View>
    </SafeAreaView>
  );
};

export default CompanionDetails;
