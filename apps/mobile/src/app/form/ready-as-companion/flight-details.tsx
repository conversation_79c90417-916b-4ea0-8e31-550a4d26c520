import { zodResolver } from '@hookform/resolvers/zod';
import DateTimePicker, {
  type DateTimePickerEvent,
} from '@react-native-community/datetimepicker';
import { Image } from 'expo-image';
import { router, useLocalSearchParams, useFocusEffect } from 'expo-router';
import React, { useState, useCallback, useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  Alert,
  Pressable,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { type z } from 'zod';
import { format, addDays } from 'date-fns';
import { showMessage } from 'react-native-flash-message';
import dayjs from 'dayjs';
import timezonePlugin from 'dayjs/plugin/timezone';
import utcPlugin from 'dayjs/plugin/utc';
import { useQuery, useMutation } from '@tanstack/react-query';

import InputErrorMsg from '@/components/input-error-msg';
import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
import Stepper from '@/components/stepper';
import { flightSchema } from '@/form-schema/form-schema';
import CustomDateTimePicker from '@/components/ui/custom-date-time-picker';
import { Button } from '@/components/ui';
import { useBookingStore } from '@/store/booking-store';
import AirportSelectionInput from '@/components/ui/airport-selection-input';
import TimezoneSelectionInput from '@/components/ui/timezone-selector';
import { ITimezone, TIMEZONES } from '@/lib/time-zones';
import { queryClient, trpc } from '@/lib/api';

dayjs.extend(timezonePlugin);
dayjs.extend(utcPlugin);

type Flight = {
  id: number;
  name: string;
};

const CompanionFlightDetails = () => {
  const { id, onboarding } = useLocalSearchParams();
  const selectedAirports = useBookingStore((state) =>
    state.selectedAirports.sort((a, b) => Number(a.index) - Number(b.index))
  );
  const setFlightBookingid = useBookingStore(
    (state) => state.setFlightBookingid
  );
  const setSelectedAirports = useBookingStore(
    (state) => state.setSelectedAirports
  );
  const removeAirport = useBookingStore((state) => state.removeAirport);
  const addEmptyAirport = useBookingStore((state) => state.addEmptyAirport);
  const [loading, setLoading] = useState(false);
  const [date, setDate] = useState(new Date());
  const [time, setTime] = useState(new Date());
  const [endDate, setEndDate] = useState(new Date());
  const [endTime, setEndTime] = useState(new Date());
  const [timezone, setTimezone] = useState<ITimezone | null>(null);

  // tRPC mutations and queries
  const upsertFlightDetailsMutation = useMutation(trpc.bookingTravelers.upsertFlightDetails.mutationOptions());
  const { data: existingCompanion, isLoading: isLoadingCompanion } = useQuery(trpc.bookingTravelers.getById.queryOptions(
    { id: id as string },
    { enabled: !!id }
  ));

  // State to store the added interconnected flights
  const [flights, setFlights] = useState<Flight[]>([]);

  // Function to add a new flight to the list
  const toggleAddInterCF = () => {
    setFlights([
      ...flights,
      {
        id: flights.length,
        name: 'InterConnected Flight',
      },
    ]);
  };

  const onDateChange = (
    event: DateTimePickerEvent,
    selectedDate: Date | undefined
  ) => {
    const currentDate = selectedDate || date;
    setDate(currentDate);

    const formattedDate = format(currentDate, 'yyyy-MM-dd');
    setValue('flightDate', formattedDate, { shouldValidate: true });
  };

  const onTimeChange = (
    event: DateTimePickerEvent,
    selectedTime: Date | undefined
  ) => {
    const currentTime = selectedTime || time;
    setTime(currentTime);

    const formattedTime = currentTime.toTimeString().split(' ')[0];
    setValue('flightTime', formattedTime, { shouldValidate: true });
  };

  const onEndDateChange = (
    event: DateTimePickerEvent,
    selectedDate: Date | undefined
  ) => {
    const currentDate = selectedDate || endDate;
    setEndDate(currentDate);

    const formattedDate = format(currentDate, 'yyyy-MM-dd');
    setValue('flightEndDate', formattedDate, { shouldValidate: true });
  };

  const onEndTimeChange = (
    event: DateTimePickerEvent,
    selectedTime: Date | undefined
  ) => {
    const currentTime = selectedTime || endTime;
    setEndTime(currentTime);

    const formattedTime = currentTime.toTimeString().split(' ')[0];
    setValue('flightEndTime', formattedTime, { shouldValidate: true });
  };

  // Function to add a new flight to the list
  const addNewFlight = () => {
    addEmptyAirport();
  };

  const {
    control,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = useForm<z.infer<typeof flightSchema>>({
    resolver: zodResolver(flightSchema),
    defaultValues: {
      flightPNR: '',
      flightDate: format(addDays(new Date(), 1), 'yyyy-MM-dd'),
      flightTime: new Date().toTimeString().split(' ')[0],
      flightEndDate: format(addDays(new Date(), 1), 'yyyy-MM-dd'),
      flightEndTime: new Date().toTimeString().split(' ')[0],
    },
  });

  const onSubmit = handleSubmit(
    async (data: z.infer<typeof flightSchema>) => {
      const allAirportSelected = selectedAirports.reduce(
        (prev, curr) => prev && curr.name !== '',
        true
      );

      if (!allAirportSelected) {
        Alert.alert('Please select all destinations');
        return;
      }

      if (!timezone) {
        Alert.alert('Please select a timezone');
        return;
      }

      setLoading(true);

      // Parse start date and time
      const flightDateTimeStr = `${data.flightDate}T${data.flightTime}`;
      const flightDateTime = dayjs.tz(dayjs(flightDateTimeStr, 'YYYY-MM-DDTHH:mm:ss'), timezone?.tzCode || 'UTC').second(0);

      // Parse end date and time
      const flightEndDateTimeStr = `${data.flightEndDate}T${data.flightEndTime}`;
      const flightEndTime = dayjs.tz(dayjs(flightEndDateTimeStr, 'YYYY-MM-DDTHH:mm:ss'), timezone?.tzCode || 'UTC').second(0);

      if (flightEndTime < flightDateTime) {
        Alert.alert('End time should be greater than start time');
        setLoading(false);
        return;
      }

      try {
        const searchField = `${selectedAirports.map((airport) => airport.id).join('-')}-${data.flightPNR}-${flightDateTime.unix()}-${flightEndTime.unix()}`;
        
        const flightData = {
          id: id as string,
          flightPNR: data.flightPNR,
          flightTime: flightDateTime.toDate(),
          flightEndTime: flightEndTime.toDate(),
          timezone: timezone?.tzCode || 'UTC',
          bookingDestinations: selectedAirports.map((airport) => ({
            order: Number(airport.index),
            airports: airport.id,
          })),
          searchField,
        };

        console.log('Submitting flight data:', flightData);

        // Use tRPC to upsert flight details
        upsertFlightDetailsMutation.mutate(flightData, {
          onSuccess: (companion) => {
            setFlightBookingid(companion.id);
            console.log('flight booking updated successfully', companion);
            // Invalidate the getById booking query for the user after successful upsert
            queryClient.invalidateQueries({
              queryKey: [
                trpc.bookingTravelers.getById.queryKey({ id: companion.id }),
                trpc.bookingTravelers.travelersAvailableForBooking.queryKey(),
                trpc.bookingTravelers.getAllByUser.queryKey(),
              ],
            });
            router.push({
              pathname: '/form/ready-as-companion/photo-uploads',
              params: {
                selectedStep: 3,
                id: companion.id,
                onboarding: onboarding === 'true' ? 'true' : 'false',
              },
            });
            setLoading(false);
          },
          onError: (error) => {
            setLoading(false);
            showMessage({
              message: 'Error in updating flight booking',
              type: 'danger',
            });
            console.log('error in flight booking is', error, id);
          },
        });
      } catch (err: any) {
        setLoading(false);
        showMessage({
          message: 'Error in updating flight booking',
          type: 'danger',
        });
        console.log('error in flight booking is', err, id);
      }
    },
    (errors) => {
      console.log('Form validation failed:', errors);
      showMessage({
        message: 'Please fill all the fields',
        type: 'danger',
      });
    }
  );

  useEffect(() => {
    if (existingCompanion) {
      console.log('flight details', existingCompanion);
      
      if (existingCompanion.flightTime) {
        setDate(new Date(existingCompanion.flightTime));
        setTime(new Date(existingCompanion.flightTime));
      }
      
      if (existingCompanion.flightEndTime) {
        setEndDate(new Date(existingCompanion.flightEndTime));
        setEndTime(new Date(existingCompanion.flightEndTime));
      }
      
      reset({
        flightPNR: existingCompanion.flightPNR || '',
        flightDate: existingCompanion.flightTime ? format(new Date(existingCompanion.flightTime), 'yyyy-MM-dd') : format(addDays(new Date(), 1), 'yyyy-MM-dd'),
        flightTime: existingCompanion.flightTime ? format(new Date(existingCompanion.flightTime), 'HH:mm') : new Date().toTimeString().split(' ')[0],
        flightEndDate: existingCompanion.flightEndTime ? format(new Date(existingCompanion.flightEndTime), 'yyyy-MM-dd') : format(addDays(new Date(), 1), 'yyyy-MM-dd'),
        flightEndTime: existingCompanion.flightEndTime ? format(new Date(existingCompanion.flightEndTime), 'HH:mm') : new Date().toTimeString().split(' ')[0],
      });
      
      if (existingCompanion.timezone) {
        setTimezone(
          TIMEZONES.find((t) => t.tzCode === existingCompanion.timezone) ?? null
        );
      }
      
      if (existingCompanion.destinations) {
        existingCompanion.destinations.forEach((destination: any) => {
          setSelectedAirports({
            id: destination.airport.id,
            name: destination.airport.name,
            index: destination.order,
          });
        });
      }
    }
  }, [existingCompanion, reset, setSelectedAirports]);



  useEffect(() => {
    // Get the device's timezone
    const timeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;

    // Set the timezone as default if no timezone is selected
    if (!timezone) {
      const defaultTimezone = TIMEZONES.find((t) => t.tzCode === timeZone);
      if (defaultTimezone) {
        setTimezone(defaultTimezone);
      }
    }
  }, []);

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
        <Stepper
          selectedStep={2}
          onPress={() => {
            router.back();
          }}
          onPress2={() => {
            router.back();
          }}
        />
        <View className=" flex-1 gap-8">
          <View className="mt-4 gap-2 ">
            <Text className="font-PoppinsBold text-[20px] font-bold leading-[30px] text-black-950 dark:text-black-0 ">
              Add Flight Details
            </Text>
            <Text className="font-inter text-[14px] font-medium leading-5 text-black-950 dark:text-black-300">
              Add companion flight details to connect with traveler
            </Text>
          </View>

          <ScrollView
            className="mb-5 w-full flex-1"
            showsVerticalScrollIndicator={false}
          >
            <View className=" w-full flex-1 gap-5">
              <View className="">
                <View className="flex-row items-center justify-between">
                  <Text className="font-PoppinsMedium text-[16px] font-medium leading-[24px] text-black-950 dark:text-black-50">
                    From & To Destination
                  </Text>
                  <TouchableOpacity onPress={addNewFlight}>
                    <View className="border-coustomborder rounded-[8px] border bg-black-0 p-[6px] dark:bg-primary-950">
                      <Image
                        source={require('../../../../assets/images/plus.png')}
                        contentFit="contain"
                        style={{ height: 20, width: 20 }}
                      />
                    </View>
                  </TouchableOpacity>
                </View>
                <View className="flex-row items-center gap-2">
                  <View className="mt-2 flex-1 flex-col">
                    {selectedAirports.map((airport, index) => (
                      <AirportSelectionInput
                        airport={airport}
                        index={index}
                        key={index}
                        setAirport={(airport) => {
                          setSelectedAirports({
                            ...airport,
                          });
                        }}
                        removeAirport={removeAirport}
                        totalFlights={selectedAirports.length}
                      />
                    ))}
                  </View>
                </View>
              </View>

              <InputLabelled label="Flight Number">
                <Controller
                  name="flightPNR"
                  control={control}
                  render={({ field, fieldState }) => {
                    return (
                      <>
                        <InputText
                          placeholder=""
                          value={field.value}
                          onChangeText={(value) => {
                            field.onChange(value);
                          }}
                          iconSourceFirst={require('../../../../assets/images/user-text-input.svg')}
                          iconStyleFirst={{ height: 15.981, width: 13.333 }}
                        />

                        {fieldState.error?.message && (
                          <InputErrorMsg
                            message={fieldState.error.message}
                          ></InputErrorMsg>
                        )}
                      </>
                    );
                  }}
                />
              </InputLabelled>

              <View className="flex-row items-center gap-4">
                <InputLabelled label="Start Date">
                  <CustomDateTimePicker
                    value={date}
                    mode="date"
                    onChange={onDateChange}
                  />
                </InputLabelled>

                <InputLabelled label="Start Time">
                  <CustomDateTimePicker
                    value={time}
                    mode="time"
                    onChange={onTimeChange}
                  />
                </InputLabelled>
              </View>

              <View className="flex-row items-center gap-4">
                <InputLabelled label="End Date">
                  <CustomDateTimePicker
                    value={endDate}
                    mode="date"
                    onChange={onEndDateChange}
                  />
                </InputLabelled>

                <InputLabelled label="End Time">
                  <CustomDateTimePicker
                    value={endTime}
                    mode="time"
                    onChange={onEndTimeChange}
                  />
                </InputLabelled>
              </View>

              <InputLabelled label="Timezone">
                <TimezoneSelectionInput
                  placeholder=""
                  timezone={timezone?.tzCode ?? ''}
                  setTimezone={(tz) => {
                    setTimezone(TIMEZONES.find((t) => t.tzCode === tz) ?? null);
                  }}
                />
              </InputLabelled>
            </View>
          </ScrollView>
        </View>

        <View className="mb-5 flex-row items-center justify-between px-5">
          <Pressable
            onPress={() => {
              router.back();
            }}
          >
            <View className="rounded-[12px] px-[24px] py-[14px] text-black-950 dark:bg-primary-950">
              <Text className="font-inter text-[16px] font-medium leading-6 text-black-950 dark:text-black-50">
                Back
              </Text>
            </View>
          </Pressable>
          <Button
            variant="secondary"
            label="Next"
            className="w-40"
            loading={loading}
            onPress={onSubmit}
          />
        </View>
      </View>
    </SafeAreaView>
  );
};

export default CompanionFlightDetails;
