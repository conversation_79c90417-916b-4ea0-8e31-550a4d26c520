import { router, useFocusEffect } from 'expo-router';
import { useLocalSearchParams } from 'expo-router';
import React, { useState, useEffect } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { ScrollView, Text, View } from 'react-native';
import { showMessage } from 'react-native-flash-message';
import { SafeAreaView } from 'react-native-safe-area-context';
import { type z } from 'zod';
import { useQuery, useMutation } from '@tanstack/react-query';

import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
import Stepper from '@/components/stepper';
import { Button } from '@/components/ui';
import ImagePickerComponent from '@/components/ui/image-picker';
import { companionPhotoSchema } from '@/form-schema/form-schema';
import { updateUserData } from '@/lib/user-data-service';
import { queryClient, trpc } from '@/lib/api';

export default function PhotoUploads() {
  const [loadingPhotoUpload, setLoadingPhotoUpload] = useState(false);
  const { id, onboarding } = useLocalSearchParams();

  // tRPC mutations and queries
  const upsertPhotoUploadsMutation = useMutation(trpc.bookingTravelers.upsertPhotoUploads.mutationOptions());
  const { data: existingCompanion } = useQuery(trpc.bookingTravelers.getById.queryOptions(
    { id: id as string },
    { enabled: !!id }
  ));

  const {
    control,
    formState: { errors },
    setValue,
    handleSubmit,
  } = useForm<z.infer<typeof companionPhotoSchema>>({
    defaultValues: {
      companionPhoto: '',
      passportPhoto: '',
      compensationValue: '',
    },
  });

  useEffect(() => {
    if (existingCompanion) {
      console.log('photo uploads details', existingCompanion);

      if (existingCompanion) {
        setValue('companionPhoto', existingCompanion.companionPhoto ?? '');
        setValue('passportPhoto', existingCompanion.passportPhoto ?? '');
        if (existingCompanion.compensationValue) {
          setValue(
            'compensationValue',
            (existingCompanion.compensationValue / 100).toString()
          );
        }
      }
    }
  }, [existingCompanion, setValue]);

  const onSubmit = handleSubmit(
    async (data) => {
      console.log('data is', data, id);
      if (data.compensationValue === '') {
        showMessage({
          message: 'Please fill compensation value',
          type: 'danger',
        });
        return;
      }
      if (data.companionPhoto === '') {
        showMessage({
          message: 'Please upload companion photo',
          type: 'danger',
        });
        return;
      }
      try {
        setLoadingPhotoUpload(true);
        
        const photoData = {
          id: id as string,
          companionPhoto: data.companionPhoto,
          passportPhoto: data.passportPhoto || null,
          compensationValue: Math.round(parseFloat(data.compensationValue) * 100),
        };

        console.log('Submitting photo data:', photoData);

        // Use tRPC to upsert photo uploads
        upsertPhotoUploadsMutation.mutate(photoData, {
          onSuccess: (companion) => {
            console.log('onboarding is', onboarding);
            // Invalidate the getById booking query for the user after successful upsert
            queryClient.invalidateQueries({
              queryKey: [
                trpc.bookingTravelers.getById.queryKey({ id: companion.id }),
                trpc.bookingTravelers.travelersAvailableForBooking.queryKey(),
                trpc.bookingTravelers.getAllByUser.queryKey(),
              ],
            });
            if (onboarding === 'true') {
              updateUserData({
                companionProfile: true,
              });
            }
            router.dismissTo('/home');
            showMessage({
              message: 'Companion profile created successfully',
              type: 'success',
            });
            console.log('companion created is', companion);
            setLoadingPhotoUpload(false);
          },
          onError: (error) => {
            console.log('error in creating companion is', error);
            showMessage({
              message: 'Error in creating companion profile',
              type: 'danger',
            });
            setLoadingPhotoUpload(false);
          },
        });
      } catch (err) {
        console.log('error in creating companion is', err);
        showMessage({
          message: 'Error in creating companion profile',
          type: 'danger',
        });
        setLoadingPhotoUpload(false);
      }
    },
    (errors) => {
      console.log('errors is', errors);
      showMessage({
        message: 'Please fill all the fields',
        type: 'danger',
      });
    }
  );

  return (
    <SafeAreaView className="flex-1">
      <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
        <Stepper
          selectedStep={3}
          onPress={() => {
            router.back();
          }}
          onPress2={() => {
            router.back();
          }}
        />

        <View className="flex-1 gap-8">
          <View className="mt-4 ">
            <Text className="font-PoppinsBold text-xl font-bold leading-[30px] text-black-950 dark:text-black-50">
              Add Photo & Set Value
            </Text>
            <Text className="font-inter font-medium leading-5 dark:text-black-300">
              Add companion's photo and set compensation value
            </Text>
          </View>
          <ScrollView className="mb-10 flex-1">
            <View className="flex-1 justify-between ">
              <View>
                <Text className="font-PoppinsRegular text-[16px] font-normal leading-[24px] text-black-950 dark:text-black-50">
                  Upload Companion Photo
                </Text>
                <View className="mt-2 flex-row items-center gap-2">
                  <Controller
                    control={control}
                    name="companionPhoto"
                    render={({ field: { value }, fieldState: { error } }) => (
                      <>
                        <ImagePickerComponent
                          image={value}
                          onImageSelected={(url) => setValue('companionPhoto', url)}
                          onImageRemoved={() => setValue('companionPhoto', '')}
                          loading={loadingPhotoUpload}
                          onLoadingChange={setLoadingPhotoUpload}
                          filePrefix="companion"
                          documentId={id as string}
                        />
                        {error?.message && (
                          <Text className="text-red-500">
                            {error.message?.toString()}
                          </Text>
                        )}
                      </>
                    )}
                  />
                </View>

                <View className="mt-6 gap-6">
                  <InputLabelled label="Compensation Amount (US$)">
                    <Controller
                      control={control}
                      name="compensationValue"
                      render={({
                        field: { value, onChange },
                        fieldState: { error },
                      }) => (
                        <>
                          <InputText
                            value={value}
                            onChangeText={(text) => {
                              let doubleText = text.replace(/[^0-9.]/g, '');
                              const parts = doubleText.split('.');
                              if (parts.length > 2) {
                                doubleText =
                                  parts[0] + '.' + parts.slice(1).join('');
                              }
                              onChange(doubleText);
                            }}
                            placeholder=""
                          />
                          {error?.message && (
                            <Text className="text-red-500">
                              {error.message?.toString()}
                            </Text>
                          )}
                        </>
                      )}
                    />
                    <Text className="mt-2 font-inter text-base font-normal text-black-300 dark:text-black-100">
                      Note: 10% of the amount paid to you will be deducted as platform fees.
                    </Text>
                  </InputLabelled>
                  {errors.compensationValue && (
                    <Text className="text-red-500">
                      {errors.compensationValue.message?.toString()}
                    </Text>
                  )}
                </View>
              </View>
            </View>
          </ScrollView>
        </View>
        <View className="mb-4">
          <Button
            variant="secondary"
            label="Save Booking"
            loading={loadingPhotoUpload}
            onPress={onSubmit}
          />
        </View>
      </View>
    </SafeAreaView>
  );
}
