import Entypo from '@expo/vector-icons/Entypo';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Pressable, Text, View } from 'react-native';
import { ID } from 'react-native-appwrite';
import { CountryPicker } from 'react-native-country-codes-picker';
import { z } from 'zod';

import BackButton from '@/components/back-button';
import InputErrorMsg from '@/components/input-error-msg';
import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
import { Button } from '@/components/ui';
import { account } from '@/lib/appwrite';

const zodValidation = z.object({
  phone: z
    .string({ required_error: 'Number is Required' })
    .min(1, 'Number is Required')
    .max(10, 'Number is too long'),
});

export default function LoginGuest() {
  const [show, setShow] = useState(false);
  const [countryCode, setCountryCode] = useState('+91');
  const { control, handleSubmit } = useForm<z.infer<typeof zodValidation>>({
    resolver: zodResolver(zodValidation),
    defaultValues: {
      phone: '',
    },
  });

  const [isLoading, setIsLoading] = useState(false);

  const onSubmit = handleSubmit(async (data) => {
    const phoneNumber = `${countryCode}${data.phone}`;

    try {
      setIsLoading(true);

      // Simulate API call for phone token creation
      const token = await account.createPhoneToken(ID.unique(), phoneNumber);

      const userId = token.userId;
      console.log('Submitted data, userId:', data, userId);

      // After successful submission, navigate to verification page
      router.push(`/verification?userId=${userId}&phone=${phoneNumber}`);
    } catch (error) {
      // Handle error if needed, e.g., show an alert
      console.error('Error during login:', error);
    } finally {
      // Reset loading state after API call is complete
      setIsLoading(false);
    }
  });

  return (
    <View className="w-full flex-1 bg-black-0 dark:bg-primary-950 ">
      <View className="flex w-full flex-1 gap-8 p-5">
        <BackButton
          text2=""
          text={'Back'}
          onPress={() => {
            router.back();
          }}
        />
        <View>
          <Text className="font-inter text-[24px] font-bold leading-[34px] text-black-900 dark:text-black-0">
            Login {/*as Guest*/}
          </Text>
          <Text className="font-inter text-base font-normal leading-6 text-black-900 dark:text-black-300">
            Login to seamlessly enjoy the app
          </Text>
        </View>
        <View className="gap-[12px]">
          <InputLabelled label="Phone">
            <Controller
              name="phone"
              control={control}
              render={({ field, fieldState }) => {
                return (
                  <>
                    {/* Country Code Picker */}
                    <View className="flex-row items-center justify-center gap-1">
                      <Pressable
                        className="mt-2 w-1/5 flex-row items-center justify-center rounded-xl border border-customborder py-3.5 dark:bg-bgtextInput"
                        onPress={() => setShow(true)}
                      >
                        <Text className="text-black-50">
                          {countryCode} {/* Display current country code */}
                        </Text>
                        <Entypo
                          name="chevron-small-down"
                          size={17}
                          color="#F2F2F2"
                        />
                      </Pressable>
                      {/* Phone Input */}
                      <InputText
                        keyboardType="number-pad"
                        maxLength={10}
                        value={field.value}
                        onChangeText={field.onChange}
                        placeholder="Enter Your number"
                        iconStyleFirst={{ height: 15.981, width: 13.333 }}
                        // onPress={() => setShow(true)}
                        className="w-[79%]"
                      />
                    </View>
                    {fieldState.error?.message && (
                      <InputErrorMsg message={fieldState.error.message} />
                    )}
                  </>
                );
              }}
            />
          </InputLabelled>
        </View>
        <View className="">
          <Button
            onPress={onSubmit}
            label="Get OTP"
            variant="secondary"
            // TODO: add right icon for the button
            // rightIcon={<AntDesign name="right" size={20} color="#071952" />}
            loading={isLoading}
          />
        </View>

        {/* Country Picker Modal */}
        <CountryPicker
          show={show}
          style={{
            modal: {
              height: '75%',
            },
          }} // Correct modal styling
          pickerButtonOnPress={(item) => {
            setCountryCode(item.dial_code); // Set selected country code
            setShow(false); // Close picker
          }}
          lang={'en'}
        />
      </View>
    </View>
  );
}
