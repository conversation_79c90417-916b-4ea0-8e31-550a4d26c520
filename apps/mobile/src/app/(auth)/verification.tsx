import { router, useLocalSearchParams } from 'expo-router';
import React, { useState } from 'react';
import { KeyboardAvoidingView, Platform, Text, View } from 'react-native';
import { OtpInput } from 'react-native-otp-entry';

import BackButton from '@/components/back-button';
import { Button, colors, showError, showTextError } from '@/components/ui';
import { account } from '@/lib/appwrite';
import { signIn } from '@/lib/auth';
import { type TokenType } from '@/lib/auth/utils';
import { useAuthContext } from '@/lib/auth/auth-context';

const keyboardBehavior = Platform.OS === 'ios' ? 'padding' : 'height';

export default function Verification() {
  const [otp, setOtp] = useState('');
  const { userId, phone } = useLocalSearchParams();
  const { refreshUser } = useAuthContext();
  const [isLoadingVerify, setIsLoadingVerify] = useState(false); // Loading state for "Verify" button
  const [isLoadingResend, setIsLoadingResend] = useState(false); // Loading state for "Resend OTP" button

  const verifyOTP = async () => {
    if (!userId || !phone) {
      console.error('Missing user ID or phone number');
      return;
    }

    if (!otp || otp.length !== 6) {
      console.error('Please enter a valid OTP');
      return;
    }

    try {
      setIsLoadingVerify(true);

      const session = await account.createSession(userId.toString(), otp);

      const token: TokenType = {
        access: session.$id, // Using session ID as access token
        refresh: userId.toString(), // Using userId as refresh token
      };

      signIn(token);
      await refreshUser();
      router.push('/');
    } catch (error) {
      showTextError('Please check if OTP is valid.');
      console.error('Error during OTP verification:', error);
    } finally {
      setIsLoadingVerify(false);
    }
  };

  // const resendOTP = async () => {
  //   try {
  //     setIsLoadingResend(true); // Set loading to true while resending OTP

  //     // Assuming there's a method like this to resend OTP
  //     await account.sendOtp(phone);  // Adjust based on your actual method for resending OTP

  //     console.log("OTP Resent successfully");
  //   } catch (error) {
  //     console.error("Error during OTP resend:", error);
  //   } finally {
  //     setIsLoadingResend(false); // Set loading to false once the resend is complete
  //   }
  // };

  return (
    <KeyboardAvoidingView behavior={keyboardBehavior} style={{ flex: 1 }}>
      <View className="w-full flex-1 gap-8 bg-primary-950 px-5 pt-2">
        <BackButton
          text="Back"
          text3="Change Number"
          onPress={() => {
            router.back();
          }}
          onPress3={() => {
            router.back();
          }}
        />
        <View className="w-full flex-1 justify-between">
          <View className="gap-10">
            <View className="w-full items-center">
              <Text className="font-inter text-[24px] font-bold leading-[34px] text-black-900 dark:text-black-0 ">
                Enter authentication code
              </Text>
              <Text className="font-inter text-[16px] font-normal leading-6 text-black-700 dark:text-black-300">
                Enter the 6-digit code that we have sent via the
              </Text>
              <Text className="font-inter text-[16px] font-normal leading-6 text-black-700 dark:text-black-300">
                phone number {phone}
              </Text>
            </View>

            <View className="px-5">
              <OtpInput
                numberOfDigits={6}
                onTextChange={(text) => {
                  setOtp(text);
                }}
                focusColor={colors.black[0]}
                theme={{
                  pinCodeTextStyle: {
                    color: 'white',
                  },
                }}
              />
            </View>
          </View>

          <View className="mb-10 gap-[18px]">
            <Button
              onPress={verifyOTP}
              label="Verify"
              textClassName="text-black-950 font-medium font-inter text-[16px] leading-6 dark:text-blue "
              variant="secondary"
              className="bg-blue dark:bg-primary-50"
              loading={isLoadingVerify} // Use loading state for this button
            />

            <Button
              onPress={
                // resendOTP
                () => {}
              }
              label="Resend OTP"
              textClassName="text-black-0 font-medium text-[16px] font-inter leading-6 dark:text-black-50"
              variant="secondary"
              className="bg-black-0 dark:bg-primary-900"
              loading={isLoadingResend} // Use loading state for this button
            />
          </View>
        </View>
      </View>
    </KeyboardAvoidingView>
  );
}
