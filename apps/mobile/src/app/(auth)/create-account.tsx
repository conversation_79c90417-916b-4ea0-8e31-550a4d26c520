import Entypo from '@expo/vector-icons/Entypo';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from 'expo-router';
import { useColorScheme } from 'nativewind';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { Pressable, ScrollView, Text, View } from 'react-native';
import { CountryPicker } from 'react-native-country-codes-picker';
import { z } from 'zod';

import BackButton from '@/components/back-button';
import ButtonWithIcon from '@/components/button-with-icon';
import InputErrorMsg from '@/components/input-error-msg';
import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
// import PasswordInput from '@/components/password-input';
import { FocusAwareStatusBar } from '@/components/ui';
import { Button } from '@/components/ui';

export default function CreateAccount() {
  const [show, setShow] = useState(false);
  const [countryCode, setCountryCode] = useState('+91');
  const [isChecked, setIsChecked] = useState<boolean>(false);

  const handleChange = (changed: boolean) => {
    setIsChecked(changed);
  };
  const { colorScheme } = useColorScheme();

  const iconColor = colorScheme === 'dark' ? 'white' : 'white';

  useEffect(() => {
    console.log('Color scheme changed to:', colorScheme);
  }, [colorScheme]);

  const zodValidation = z.object({
    Name: z
      .string({ required_error: 'Name is required' })
      .min(1, 'Name is required'),
    email: z
      .string({ required_error: 'Email is required' })
      .min(1, 'Email is required')
      .email(),
    phone: z
      .string({ required_error: 'Phone number is required' })
      .min(1, 'Phone number is required'),
    // password: z
    //   .string({ required_error: 'Password is required' })
    //   .min(8, 'Password is required'),
  });

  const { control, handleSubmit } = useForm<z.infer<typeof zodValidation>>({
    resolver: zodResolver(zodValidation),
    defaultValues: {
      Name: '',
      email: '',
      phone: '',
      // password: '',
    },
  });

  const onSubmit = handleSubmit((data) => {
    
    console.log('Submitted data:', data);
    // router.push('/login');
    router.push('/verification');
  });

  return (
    <View className="w-full flex-1 bg-white dark:bg-primary-950">
      <View className="flex w-full flex-1 gap-8 p-5 ">
        <FocusAwareStatusBar />

        <BackButton
          text2=""
          text={'Back'}
          onPress={() => {
            router.back();
          }}
        />

        <View>
          <Text className="font-inter text-[24px] font-bold leading-[34px] text-black-900 dark:text-black-50">
            Create your account
          </Text>
          <Text className="font-inter text-base font-normal leading-[24px] text-black-900 dark:text-black-200">
            create account to find your companion
          </Text>
        </View>

        <ScrollView className="flex-1 " showsVerticalScrollIndicator={false}>
          <View className="gap-[30px]">
            <View className="gap-3">
              <View className="gap-5">
                <InputLabelled label="Name">
                  <Controller
                    name="Name"
                    control={control}
                    render={({ field, fieldState }) => (
                      <>
                        <InputText
                          iconSourceFirst={require('../../../assets/images/user-text-input.svg')}
                          iconStyleFirst={{
                            height: 15.981,
                            width: 13.333,
                            tintColor: 'white',
                          }}
                          placeholder="Enter your name"
                          value={field.value}
                          onChangeText={(value) => field.onChange(value)}
                        />
                        {fieldState.error?.message && (
                          <InputErrorMsg message={fieldState.error.message} />
                        )}
                      </>
                    )}
                  />
                </InputLabelled>

                <InputLabelled label="Email">
                  <Controller
                    name="email"
                    control={control}
                    render={({ field, fieldState }) => (
                      <>
                        <InputText
                          placeholder="Enter your Email"
                          value={field.value}
                          onChangeText={(value) => field.onChange(value)}
                        />
                        {fieldState.error?.message && (
                          <InputErrorMsg message={fieldState.error.message} />
                        )}
                      </>
                    )}
                  />
                </InputLabelled>
                <InputLabelled label="Phone">
                  <Controller
                    name="phone"
                    control={control}
                    render={({ field, fieldState }) => (
                      <>
                        <View className="flex-row items-center justify-center gap-1">
                          <Pressable
                            className="mt-2 w-1/5 flex-row items-center justify-center rounded-xl border border-customborder py-3.5 dark:bg-bgtextInput"
                            onPress={() => setShow(true)}
                          >
                            <Text className="text-black-50">
                              {countryCode} {/* Display current country code */}
                            </Text>
                            <Entypo
                              name="chevron-small-down"
                              size={17}
                              color="#F2F2F2"
                            />
                          </Pressable>
                          {/* Phone Input */}
                          <InputText
                            keyboardType="number-pad"
                            maxLength={10}
                            value={field.value}
                            onChangeText={field.onChange}
                            placeholder="Enter Your number"
                            iconStyleFirst={{ height: 15.981, width: 13.333 }}
                            // onPress={() => setShow(true)}
                            className="w-[79%]"
                          />
                        </View>
                        {/*<InputText*/}
                        {/*  maxLength={10}*/}
                        {/*  keyboardType="number-pad"*/}
                        {/*  placeholder="Enter your phone no."*/}
                        {/*  value={field.value}*/}
                        {/*  onChangeText={(value) => field.onChange(value)}*/}
                        {/*/>*/}
                        {fieldState.error?.message && (
                          <InputErrorMsg message={fieldState.error.message} />
                        )}
                      </>
                    )}
                  />
                </InputLabelled>
              </View>
            </View>

            <View className="flex-1 gap-8">
              <View className="">
                <Button
                  onPress={onSubmit}
                  label="Get OTP"
                  variant="secondary"
                  className="bg-blue dark:bg-primary-50"
                  textClassName=""
                  // TODO: add right icon for the button
                  // rightIcon={
                  //   <AntDesign name="right" size={16} color={'#071952'} />
                  // }
                />
              </View>

              <View className="w-full flex-row items-center gap-2   ">
                <View className="flex-1 border border-secondary-0"></View>
                <Text className="text-center font-inter text-[12px] font-medium leading-4 text-black-900 dark:text-black-0">
                  or continue with
                </Text>
                <View className="flex-1 border border-secondary-0"></View>
              </View>

              <ButtonWithIcon
                onPress={() => {
                  router.push('/login');
                }}
              />
            </View>
          </View>
        </ScrollView>
        {/* Country Picker Modal */}
        <CountryPicker
          show={show}
          style={{
            modal: {
              height: '75%',
            },
          }} // Correct modal styling
          pickerButtonOnPress={(item) => {
            setCountryCode(item.dial_code); // Set selected country code
            setShow(false); // Close picker
          }}
          lang={'en'}
        />
      </View>
    </View>
  );
}
