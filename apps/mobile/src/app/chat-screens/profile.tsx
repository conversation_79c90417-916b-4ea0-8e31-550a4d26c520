import { router } from 'expo-router';
import React from 'react';
import {
  Alert,
  Image,
  ImageBackground,
  ScrollView,
  Text,
  View,
} from 'react-native';

import BackButton from '@/components/back-button';
import { Button } from '@/components/ui';

export default function UserProfile() {
  return (
    <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
      <View className="mt-2.5 py-1.5">
        <BackButton
          text="Back"
          onPress={() => {
            router.back();
          }}
        />
      </View>

      <View className="w-full flex-1 justify-between ">
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          <View className="relative w-full ">
            <View className="mt-3">
              <ImageBackground
                source={require('../../../assets/images/photos.png')}
                resizeMode="contain"
                style={{ height: 430, width: '100%' }}
              />
            </View>

            <View className="absolute  bottom-0 px-[20px] py-[30px]">
              <View className="flex-row">
                <View className="rounded-[12px] bg-black-0 px-[10px] py-2 dark:bg-secondary-650">
                  <Text className="font-inter text-[12px] font-semibold leading-4 text-black-950 dark:text-black-0 ">
                    Regular User
                  </Text>
                </View>
              </View>
              <View className="gap-[24px]">
                <View className="w-full flex-row items-center justify-between ">
                  <View className="">
                    <Text className="font-PoppinsBold text-2xl font-bold leading-[34px] text-black-0 ">
                      Kaushiki
                    </Text>
                    <Text className="font-inter text-[16px] font-normal leading-6 text-black-0">
                      Female, 35 yrs, Indian
                    </Text>
                  </View>

                  <View>
                    <View className="flex-row items-center gap-1 rounded-md bg-black-950 p-2">
                      <Image
                        source={require('../../../assets/images/single-star.png')}
                        style={{ height: 12, width: 10 }}
                        resizeMode="contain"
                      />
                      <Image
                        source={require('../../../assets/images/single-star.png')}
                        style={{ height: 12, width: 10 }}
                        resizeMode="contain"
                      />
                      <Image
                        source={require('../../../assets/images/single-star.png')}
                        style={{ height: 12, width: 10 }}
                        resizeMode="contain"
                      />
                      <Image
                        source={require('../../../assets/images/nostar.png')}
                        style={{ height: 12, width: 10 }}
                        resizeMode="contain"
                      />
                      <Image
                        source={require('../../../assets/images/nostar.png')}
                        style={{ height: 12, width: 10 }}
                        resizeMode="contain"
                      />
                    </View>
                    <Text className="mt-2.5 font-inter text-[14px] font-normal leading-5 text-black-0">
                      Rated by: 56
                    </Text>
                  </View>
                </View>

                <View className="gap-[14px]">
                  <View className="flex-row items-center gap-4">
                    <Image
                      source={require('../../../assets/images/genderpreference.png')}
                      resizeMode="contain"
                      style={{ height: 32, width: 32 }}
                    />
                    <View className="flex-row items-center gap-2 rounded-[12px] bg-secondary-350  px-[10px] py-2 dark:bg-charcoal-500">
                      <Image
                        source={require('../../../assets/images/gender.png')}
                        resizeMode="contain"
                        style={{ height: 12, width: 12 }}
                      />
                      <Text className="text-black-950 dark:text-black-0">
                        Male
                      </Text>
                    </View>

                    <View className="flex-row items-center gap-2 rounded-[12px] bg-secondary-350 px-[10px]  py-2 dark:bg-charcoal-500">
                      <Image
                        source={require('../../../assets/images/gender.png')}
                        resizeMode="contain"
                        style={{ height: 12, width: 12 }}
                      />
                      <Text className="text-black-950 dark:text-black-0">
                        Female
                      </Text>
                    </View>
                  </View>

                  <View className="flex-row items-center gap-4">
                    <Image
                      source={require('../../../assets/images/text-icon.png')}
                      resizeMode="contain"
                      style={{ height: 22.779, width: 26.667 }}
                    />
                    <View className="flex-row items-center gap-2 rounded-[12px] bg-secondary-350  px-[10px] py-2 dark:bg-charcoal-500">
                      <Text className="text-black-950 dark:text-black-0">
                        Hindi
                      </Text>
                    </View>
                    <View className="flex-row items-center gap-2 rounded-[12px] bg-secondary-350 px-[10px] py-2 dark:bg-charcoal-500">
                      <Text className="text-black-950 dark:text-black-0">
                        English
                      </Text>
                    </View>

                    <Text className="text-black-0">+5 More</Text>
                  </View>
                </View>
              </View>
            </View>
          </View>

          <View className="mb-2 mt-5">
            <View className="w-full gap-3 rounded-[12px] bg-black-0 px-4 py-6 dark:bg-blue">
              <View className="flex-row gap-2">
                <View className="flex-row items-center gap-1 rounded-[8px] bg-black-950 px-[10px] py-[6px] dark:bg-black-50">
                  <Image
                    source={require('../../../assets/images/plane.png')}
                    resizeMode="contain"
                    style={{ height: 9, width: 8.999 }}
                    tintColor={'#071952'}
                  />
                  <Text className="font-inter text-[12px] font-medium leading-[14px] text-black-950 dark:text-blue">
                    Solo Traveler
                  </Text>
                </View>
                <View className="rounded-[8px] bg-black-950 px-[10px] py-[6px] dark:bg-black-50">
                  <Text className="font-inter text-[12px] font-medium leading-[14px] text-black-950 dark:text-blue">
                    Assistance: 45 Trips
                  </Text>
                </View>
              </View>

              <View className="gap-2">
                <View className="flex-row items-center gap-2">
                  <Image
                    source={require('../../../assets/images/gender.png')}
                    resizeMode="contain"
                    tintColor={'#7291F3'}
                    style={{ height: 12, width: 12 }}
                  />
                  <Text className="font-inter text-[16px] font-semibold leading-6 text-black-950 dark:text-black-50">
                    About Companion
                  </Text>
                </View>
                <Text className="font-inter text-[12px] font-normal leading-5 dark:text-black-100">
                  Solo traveler on the introverted side, seeking a
                  conversation-friendly companion for my next flight. More
                  interested in relaxing and unwinding during the journey, but
                  open to friendly conversation if the vibes are good.
                  Passionate about photography and capturing the beauty of
                  different destinations.
                </Text>
              </View>

              <View className="rounded-[12px] bg-black-950 p-5 dark:bg-bgdark ">
                <View className="gap-2">
                  <View className="flex-row items-center gap-2">
                    <Image
                      source={require('../../../assets/images/text-icon.png')}
                      resizeMode="contain"
                      style={{ height: 20, width: 20 }}
                    />
                    <Text className="font-inter text-[12px] font-normal leading-4 text-black-950 dark:text-black-50">
                      Language Spoke
                    </Text>
                  </View>

                  <View className="flex flex-row flex-wrap gap-3">
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Hindi
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      English
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Punjabi
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Sanskrit
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Telugu
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Marathi
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Gujrati
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Gujrati
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Gujarati
                    </Text>
                  </View>
                </View>
              </View>

              <View className="rounded-[12px] bg-black-950 p-5 dark:bg-bgdark ">
                <View className="gap-2">
                  <View className="flex-row items-center gap-2">
                    <Image
                      source={require('../../../assets/images/text-icon.png')}
                      resizeMode="contain"
                      style={{ height: 20, width: 20 }}
                    />
                    <Text className="font-inter text-[12px] font-normal leading-4 text-black-950 dark:text-black-0">
                      Language Spoke
                    </Text>
                  </View>

                  <View className="flex flex-row flex-wrap gap-3">
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Hindi
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      English
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Punjabi
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Sanskrit
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Telgu
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Marathi
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Gujrati
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Gujrati
                    </Text>
                    <Text className="font-inter font-medium text-black-950 dark:text-black-0">
                      Gujarati
                    </Text>
                  </View>
                </View>
              </View>
            </View>
          </View>
        </ScrollView>

        <View className="mb-8">
          <Button
            variant="secondary"
            label="Continue Chatting"
            onPress={() => Alert.alert('Function not Implemented yet')}
          />
        </View>
      </View>
    </View>
  );
}
