import Entypo from '@expo/vector-icons/Entypo';
import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { router, useNavigation, useLocalSearchParams } from 'expo-router';
import React, { useRef, useState, useEffect } from 'react';
import { ActivityIndicator, Alert, SafeAreaView } from 'react-native';
import {
  Image,
  Pressable,
  ScrollView,
  Text,
  TextInput,
  View,
} from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { trpc } from '@/lib/api';
import { useGetChannel, useSendMessage, useGetStreamToken, streamChatClient } from '@/hooks/use-stream-chat';
import {
  Channel,
  MessageInput,
  MessageList,
  OverlayProvider,
} from 'stream-chat-react-native';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

export default function ChatScreen() {
  const navigation = useNavigation();
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const [isVisible, setIsVisible] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [channel, setChannel] = useState<any | null>(null);
  
  // Get connection request ID from route params
  const { connectionRequestId } = useLocalSearchParams<{ connectionRequestId: string }>();

  // Get connection request details
  const { data: connectionRequestData, isLoading: isLoadingConnection } = useQuery(
    trpc.connectionRequests.getById.queryOptions({ id: connectionRequestId })
  );

  // Get channel details
  const { data: channelData, isLoading: isLoadingChannel } = useGetChannel(connectionRequestId);


  // Send message mutation
  const sendMessageMutation = useSendMessage();

  useEffect(() => {
    const connectToChat = async () => {
      streamChatClient.initializeClient();
      console.log('channelData', channelData);
      if (!channelData) return;
      try {
        const channel = await streamChatClient.getClient()?.channel(channelData.type, channelData.id);
        console.log('channel', streamChatClient.getClient(), channel);
        setChannel(channel);
        setIsConnected(true);
      } catch (error) {
        console.error('Failed to connect to chat:', error);
        Alert.alert('Error', 'Failed to connect to chat. Please try again.');
      }
    };

    connectToChat();

    // Cleanup on unmount
    return () => {
      // streamChatClient.disconnectUser();
    };
  }, [connectionRequestData, channelData]);

  const handlePresentModal = () => {
    bottomSheetModalRef.current?.present();
    setIsVisible(true);
  };

  const handleDismissModal = () => {
    bottomSheetModalRef.current?.dismiss();
    setIsVisible(false);
  };

  // Get other user info for header
  const isTraveler = connectionRequestData?.userId === connectionRequestData?.bookingCompanion?.appwriteUserId;
  const otherUser = isTraveler ? connectionRequestData?.bookingTraveler : connectionRequestData?.bookingCompanion;
  const otherUserName = `${otherUser?.name || ''}`.trim();

  // Create flight route string
  const destinations = isTraveler 
    ? (connectionRequestData?.bookingTraveler as any)?.destinations 
    : (connectionRequestData?.bookingCompanion as any)?.destinations;
  
  const flightRoute = destinations?.map((d: any) => d.airport?.shortCode).join(' → ') || '';

  return (
    <View className="flex-1" style={{ paddingBottom: 50 }}>
    {/* <KeyboardAvoidingView style={{ flex: 1 }}> */}
      {/* {isVisible ? (
        <View className="absolute z-10 size-full flex-1 bg-[black] opacity-70" />
      ) : null} */}
      <View className="pt-safe flex-1">
        {/* Header */}
        <View className="flex-row items-center justify-between bg-[#20202080] px-5 py-3">
          {/* Profile details and back button */}
          <View className="flex-row items-center">
            <Pressable onPress={() => navigation.goBack()}>
              <Entypo
                name="chevron-thin-left"
                size={18}
                color="#CCCCCC"
                className="pr-3"
              />
            </Pressable>
            <Pressable
              className="flex-row items-center"
              onPress={() => router.push('/chat-screens/profile')}
            >
              <View className="ml-3 size-10 rounded-full bg-gray-300 overflow-hidden">
                { connectionRequestData?.bookingCompanion?.travelersPhoto || connectionRequestData?.bookingTraveler?.companionPhoto ? (
                  <Image
                    source={{ uri: isTraveler ? connectionRequestData?.bookingTraveler?.companionPhoto || '' : connectionRequestData?.bookingCompanion?.travelersPhoto || '' }}
                    className="w-full h-full"
                    resizeMode="cover"
                  />
                ) : (
                  <View className="w-full h-full bg-black-900 items-center justify-center">
                    <Text className="text-white font-semibold text-lg">
                      {otherUserName.charAt(0).toUpperCase()}
                    </Text>
                  </View>
                )}
              </View>
              {isLoadingConnection || isLoadingChannel ? (
                <View className="px-3 w-32">
                  <View className="h-5 w-24 bg-gray-400/30 rounded mb-1" />
                  <View className="h-4 w-20 bg-gray-400/20 rounded" />
                </View>
              ) : (
                <View className="px-3">
                  <Text className="font-PoppinsMedium text-base font-medium text-charcoal-50">
                    {otherUserName || 'Chat'}
                  </Text>
                  {flightRoute && (
                    <View className="flex-row items-center">
                      <Text className="font-PoppinsRegular text-xs font-normal text-black-300">
                        {flightRoute}
                      </Text>
                    </View>
                  )}
                </View>
              )}
            </Pressable>
          </View>

          <View className="flex-row items-center">
            {/* type */}
            {/* <Pressable
              className="mx-3 rounded-lg bg-primary-50 px-6 py-3"
              onPress={() => router.push('/payment/')}
            >
              <Text className="font-inter text-base font-medium text-blue">
                Pay
              </Text>
            </Pressable> */}

            {/* header 3 dot menu */}
            {/* <Pressable className="rounded-lg " onPress={handlePresentModal}>
              <Image
                source={require('../../../assets/images/dotwhite.png')}
                className=" size-6"
              />
            </Pressable> */}
          </View>
        </View>

        {/* Chat screen */}
        {isLoadingConnection || isLoadingChannel ? (
          <View className="flex-1 items-center justify-center">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="text-gray-500 mt-2">Loading chat...</Text>
          </View>
        ) : !connectionRequestData ? (
          <View className="flex-1 items-center justify-center">
            <Text className="text-red-500 text-lg">Connection not found</Text>
            <Text className="text-gray-500 mt-2">This chat conversation doesn't exist.</Text>
          </View>
        ) : !isConnected ? (
          <View className="flex-1 items-center justify-center">
            <ActivityIndicator size="large" color="#3B82F6" />
            <Text className="text-gray-500 mt-2">Connecting to chat...</Text>
          </View>
        ) : (
          <View className="flex-1">
            {channel ? (
              <Channel channel={channel}>
                <View className="flex-1">
                  <MessageList />
                  <MessageInput />
                </View>
              </Channel>
            ) : (
              <View className="flex-1 items-center justify-center p-8">
                <Text className="text-gray-500 text-center text-lg mb-2">
                  Chat channel not found
                </Text>
                <Text className="text-gray-400 text-center text-sm">
                  The chat channel for this connection hasn't been created yet.
                </Text>
              </View>
            )}
          </View>
        )}

        {/* Bottom Sheet Modal */}
        <BottomSheetModal
          ref={bottomSheetModalRef}
          index={0}
          snapPoints={['50%']}
          onDismiss={handleDismissModal}
          backgroundStyle={{ backgroundColor: '#020717' }}
          handleIndicatorStyle={{ backgroundColor: 'transparent' }}
          enablePanDownToClose={true}
        >
          <View className="flex-1 px-5">
            <Pressable onPress={handleDismissModal} className="py-5">
              <Image
                source={require('../../../assets/images/cancel.png')}
                resizeMode="contain"
                className="size-7"
              />
            </Pressable>

            <Pressable className="flex-row items-center justify-between py-5">
              <View className="flex-row items-center">
                <Image
                  source={require('../../../assets/images/profile1.png')}
                  resizeMode="contain"
                  className="mr-5 size-6"
                />
                <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                  View Profile
                </Text>
              </View>
              <Entypo name="chevron-thin-right" size={18} color="#FDFDFD" />
            </Pressable>

            <Pressable className="flex-row items-center justify-between py-5">
              <View className="flex-row items-center">
                <Image
                  source={require('../../../assets/images/helpcenter.png')}
                  resizeMode="contain"
                  className="mr-5 size-6"
                />
                <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                  Help Center
                </Text>
              </View>
              <Entypo name="chevron-thin-right" size={18} color="#FDFDFD" />
            </Pressable>

            <Pressable className="flex-row items-center justify-between py-5">
              <View className="flex-row items-center">
                <Image
                  source={require('../../../assets/images/report.png')}
                  resizeMode="contain"
                  className="mr-5 size-6"
                />
                <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                  Report Companion
                </Text>
              </View>
              <Entypo name="chevron-thin-right" size={18} color="#FDFDFD" />
            </Pressable>

            <Pressable className="flex-row items-center justify-between py-5">
              <View className="flex-row items-center">
                <Image
                  source={require('../../../assets/images/Bin.png')}
                  resizeMode="contain"
                  className="mr-5 size-6"
                />
                <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                  Delete Chat
                </Text>
              </View>
            </Pressable>

            <Pressable className="flex-row items-center justify-between py-5">
              <View className="flex-row items-center">
                <Image
                  source={require('../../../assets/images/idk.png')}
                  resizeMode="contain"
                  className="mr-5 size-6"
                />
                <View className="flex-row flex-wrap items-center">
                  <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                    Close Chat
                  </Text>
                  <Text className="font-PoppinsMedium text-xs font-medium text-black-100">
                    (this option only available when user have paid the monetary
                    value to companion)
                  </Text>
                </View>
              </View>
            </Pressable>

            <Pressable className="flex-row items-center justify-between py-5">
              <View className="flex-row items-center">
                <Image
                  source={require('../../../assets/images/block.png')}
                  resizeMode="contain"
                  className="mr-5 size-6"
                />
                <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                  Block
                </Text>
              </View>
            </Pressable>
          </View>
        </BottomSheetModal>
      </View>
    {/* </KeyboardAvoidingView> */}
    </View>
  );
}
