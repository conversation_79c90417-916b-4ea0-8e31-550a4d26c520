import { router } from 'expo-router';
import React, { useState } from 'react';
import { ActivityIndicator, Alert, Image, ImageBackground, RefreshControl, ScrollView, Text, View } from 'react-native';

import BackButton from '@/components/back-button';
import BookingsCard from '@/components/bookings-card';
import { Button, FocusAwareStatusBar, Modal, useModal } from '@/components/ui';
import { useLocalSearchParams } from 'expo-router';
import { useEffect } from 'react';
import { databases } from '@/lib/appwrite';
import { Env } from '@env';
import * as MailComposer from 'expo-mail-composer';
import { showMessage } from 'react-native-flash-message';
import { BookingCompanion, BookingTraveler } from '@/types';
import { Query } from 'react-native-appwrite';
import CompanionBookingsCard from '@/components/companion-booking-card';
import CompanionDetailCard from '@/components/companion-detail-card';
import { useQuery } from '@tanstack/react-query';
import { trpc } from '@/lib/api';

function BookingDetailsCompanion({ bookingCompanion }: { bookingCompanion: BookingCompanion }) {
  const {ref: modalRef, present, dismiss} = useModal();
  const [selectedBookingTraveler, setSelectedBookingTraveler] = useState<BookingTraveler | null>(null);
  const { data, isLoading, isRefetching, isError, refetch } = useQuery(trpc.bookingCompanions.companionsAvailableForBooking.queryOptions({ bookingCompanionId: bookingCompanion.id }, {
    staleTime: 1000 * 10, // 10 seconds
  }));


  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator />
      </View>
    );
  }

  if (isError || !data) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-red-500">Error fetching companions</Text>
        <Button
          variant="secondary"
          label="Retry"
          onPress={() => {
            refetch();
          }}
        />
      </View>
    );
  }

  console.log('data booking details companion', JSON.stringify(data));

  return (
    <View  className="flex-1 gap-8 ">
      <View className="flex-row items-center justify-between">
        <Text className="leading-5 font-inter font-normal  text-black-50">Total Companions: {data.total}</Text>
        <Button variant="secondary" label="Refresh" loading={isRefetching} fullWidth={false} onPress={refetch} />
      </View>
      {data.companions.map((bookingTraveler) => (
        <CompanionDetailCard key={bookingTraveler.id} bookingCompanion={bookingCompanion} bookingTraveler={bookingTraveler} onPress={() => {
          setSelectedBookingTraveler(bookingTraveler);
          present();
        }} />
      ))}
      <Modal ref={modalRef} snapPoints={['95%']}  style={{backgroundColor: '#030303'}}>
      <View className="w-full flex-1 justify-between bg-black-950 px-4 py-4 rounded-[12px] gap-4 mt-4">
        <ScrollView refreshControl={<RefreshControl refreshing={isLoading} onRefresh={refetch} />} className="flex-1" showsVerticalScrollIndicator={false}>
          <View className="relative w-full ">
            <View className="mt-3">
              <ImageBackground
                source={{uri: selectedBookingTraveler?.companionPhoto ?? 'https://placehold.co/430x430'}}
                resizeMode="contain"
                style={{ height: 430, width: '100%' }}
              />
            </View>

            <View className="absolute  bottom-0 px-[20px] py-[30px]">
              <View className="flex-row">
                <View className="rounded-[12px] bg-black-0 px-[10px] py-2 dark:bg-secondary-650">
                  <Text className="font-inter text-[12px] font-semibold leading-4 text-black-950 dark:text-black-0 ">
                    Regular User
                  </Text>
                </View>
              </View>
              <View className="gap-[24px]">
                <View className="w-full flex-row items-center justify-between ">
                  <View className="">
                    <Text className="font-PoppinsBold text-2xl font-bold leading-[34px] text-black-0 ">
                      {selectedBookingTraveler?.name}
                    </Text>
                    <Text className="font-inter text-[16px] font-normal leading-6 text-black-0">
                      {selectedBookingTraveler?.gender}
                    </Text>
                  </View>

                  <View>
                    <View className="flex-row items-center gap-1 rounded-md bg-black-950 p-2">
                      <Image
                        source={require('../../../assets/images/single-star.png')}
                        style={{ height: 12, width: 10 }}
                        resizeMode="contain"
                      />
                      <Image
                        source={require('../../../assets/images/single-star.png')}
                        style={{ height: 12, width: 10 }}
                        resizeMode="contain"
                      />
                      <Image
                        source={require('../../../assets/images/single-star.png')}
                        style={{ height: 12, width: 10 }}
                        resizeMode="contain"
                      />
                      <Image
                        source={require('../../../assets/images/nostar.png')}
                        style={{ height: 12, width: 10 }}
                        resizeMode="contain"
                      />
                      <Image
                        source={require('../../../assets/images/nostar.png')}
                        style={{ height: 12, width: 10 }}
                        resizeMode="contain"
                      />
                    </View>
                    <Text className="mt-2.5 font-inter text-[14px] font-normal leading-5 text-black-0">
                      Rated by: 56
                    </Text>
                  </View>
                </View>

                <View className="gap-[14px]">
                  <View className="flex-row items-center gap-4">
                    <Image
                      source={require('../../../assets/images/genderpreference.png')}
                      resizeMode="contain"
                      style={{ height: 32, width: 32 }}
                    />
                    <View className="flex-row items-center gap-2 rounded-[12px] bg-secondary-350  px-[10px] py-2 dark:bg-charcoal-500">
                      <Image
                        source={require('../../../assets/images/gender.png')}
                        resizeMode="contain"
                        style={{ height: 12, width: 12 }}
                      />
                      <Text className="text-black-950 dark:text-black-0">
                        {selectedBookingTraveler?.gender}
                      </Text>
                    </View>

                    <View className="flex-row items-center gap-2 rounded-[12px] bg-secondary-350 px-[10px]  py-2 dark:bg-charcoal-500">
                      <Image
                        source={require('../../../assets/images/genderpreference.png')}
                        resizeMode="contain"
                        style={{ height: 12, width: 12 }}
                      />
                      <Text className="text-black-950 dark:text-black-0">
                        {selectedBookingTraveler?.genderPreference}
                      </Text>
                    </View>
                  </View>

                  <View className="flex-row items-center gap-4">
                    <Image
                      source={require('../../../assets/images/text-icon.png')}
                      resizeMode="contain"
                      style={{ height: 22.779, width: 26.667 }}
                    />
                    {selectedBookingTraveler?.languages.slice(0, 3)?.map((language) => (
                      <View key={language.id} className="flex-row items-center gap-2 rounded-[12px] bg-secondary-350  px-[10px] py-2 dark:bg-charcoal-500">
                        <Text className="text-black-950 dark:text-black-0">
                          {language.language.name}
                        </Text>
                      </View>
                    ))}

                    {selectedBookingTraveler?.languages?.length && selectedBookingTraveler?.languages?.length > 3 && <Text className="text-black-0">+{selectedBookingTraveler?.languages?.length - 3} More</Text>}
                  </View>
                </View>
              </View>
            </View>
          </View>

          <View className="mb-2 mt-5">
            <View className="w-full gap-3 rounded-[12px] bg-black-0 px-4 py-6 dark:bg-blue">
              <View className="flex-row gap-2">
                <View className="flex-row items-center gap-1 rounded-[8px] bg-black-950 px-[10px] py-[6px] dark:bg-black-50">
                  <Image
                    source={require('../../../assets/images/plane.png')}
                    resizeMode="contain"
                    style={{ height: 9, width: 8.999 }}
                    tintColor={'#071952'}
                  />
                  <Text className="font-inter text-[12px] font-medium leading-[14px] text-black-950 dark:text-blue">
                    {selectedBookingTraveler?.typeOfTraveler ?? 'SOLO'}
                  </Text>
                </View>
                <View className="rounded-[8px] bg-black-950 px-[10px] py-[6px] dark:bg-black-50">
                  <Text className="font-inter text-[12px] font-medium leading-[14px] text-black-950 dark:text-blue">
                    Assistance: 45 Trips
                  </Text>
                </View>
              </View>

              <View className="gap-2">
                <View className="flex-row items-center gap-2">
                  <Image
                        source={require('../../../assets/images/gender.png')}
                    resizeMode="contain"
                    tintColor={'#7291F3'}
                    style={{ height: 12, width: 12 }}
                  />
                  <Text className="font-inter text-[16px] font-semibold leading-6 text-black-950 dark:text-black-50">
                    About Companion
                  </Text>
                </View>
                <Text className="font-inter text-[12px] font-normal leading-5 dark:text-black-100">
                  {selectedBookingTraveler?.about}
                </Text>
              </View>


              <View className="rounded-[12px] bg-black-950 p-5 dark:bg-bgdark ">
                <View className="gap-2">
                  <View className="flex-row items-center gap-2">
                    <Image
                      source={require('../../../assets/images/text-icon.png')}
                      resizeMode="contain"
                      style={{ height: 20, width: 20 }}
                    />
                    <Text className="font-inter text-[12px] font-normal leading-4 text-black-950 dark:text-black-0">
                      Language Spoke
                    </Text>
                  </View>

                  <View className="flex flex-row flex-wrap gap-3">
                    {selectedBookingTraveler?.languages?.map((language) => (
                      <Text key={language.id} className="font-inter font-medium text-black-950 dark:text-black-0">
                        {language.language.name}
                      </Text>
                    ))}
                  </View>
                </View>
              </View>
            </View>
          </View>
        </ScrollView> 

        <View className="mb-8">
          {/* <Button
            variant="secondary"
            label="Send Request"
              onPress={() => {
              // TODO: Implement send request logic here
              // if (typeof companion.onSendRequest === 'function') {
              //   companion.onSendRequest(companion);
              // }
            }}
          /> */}
        </View>
        </View>
      </Modal>
    </View>
  );
}


export default function BookingDetails() {
  const { id } = useLocalSearchParams();
  
  const { data: bookingCompanion, isLoading, isError, refetch } = useQuery(trpc.bookingCompanions.getById.queryOptions({
    id: id as string,
  }));

  if (isLoading) {
    return <ActivityIndicator />;
  }

  if (isError) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-red-500">Error fetching bookings</Text>
        <Button
          variant="secondary"
          label="Retry"
          onPress={() => {
            refetch();
          }}
        />
      </View>
    );
  }

  if (!bookingCompanion) {
    return <Text>No booking details found</Text>;
  }

  return (
    <View className="flex-1 dark:bg-primary-950">
      <FocusAwareStatusBar />
      <View className="gap-2.5 px-5 py-3 dark:bg-[#20202080]">
        <BackButton
          text="Back"
          text2="Booking Details"
          text3="Help"
          onPress={() => {
            router.back();
          }}
          onPress3={() => {
            MailComposer.composeAsync({
              recipients: ['<EMAIL>'],
              subject: `Booking #${bookingCompanion.id}`,
              body: ``,
            }).catch((err) => {
              showMessage({
                message: 'Error in sending email',
                type: 'danger',
              });
              console.log('error in sending email', err);
            });
          }}
        />
        {bookingCompanion && (
          <BookingsCard
            booking={bookingCompanion}
          />
        )}
        {bookingCompanion.isEditable && <Button
          variant="secondary"
          label="Edit Booking"
          onPress={() => {
            router.push(`/form/companion-required/traveler-details?id=${bookingCompanion.id}`);
          }}
        />}
      </View>

      <View className="flex-1 gap-8 px-5 ">
        <View className="flex-row items-center justify-between">
          {bookingCompanion && <BookingDetailsCompanion bookingCompanion={bookingCompanion} />}
        </View>
      </View>
      {/* <View className="flex-1 gap-8 px-5 ">
        <View className="mt-8 gap-6 rounded-xl border border-primary-900  p-5 dark:bg-primary-950">
          {/* Row First *\/}
          <View className="gap-3.5   border-b border-primary-900 pb-4">
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                {' '}
                Payment on
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                {' '}
                9 Decemeber, 2023 | 10:00 AM
              </Text>
            </View>
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                {' '}
                Booked Date
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                {' '}
                19 December, 2023
              </Text>
            </View>
          </View>
          {/* Row Second *\/}
          <View className="gap-8  border-b border-primary-900 pb-4">
            <View className="gap-3.5">
              <View className="flex-row items-center justify-between">
                <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                  Amount
                </Text>
                <Text className="font-inter text-sm font-semibold dark:text-black-50">
                  {' '}
                  $90.50
                </Text>
              </View>
              <View className="flex-row items-center justify-between">
                <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                  {' '}
                  Tax
                </Text>
                <Text className="font-inter text-sm font-semibold dark:text-black-50">
                  {' '}
                  $5.00
                </Text>
              </View>
            </View>

            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                Total
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                $95.50
              </Text>
            </View>
          </View>
          {/* Row Thired *\/}
          <View className="gap-3.5 ">
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                Name
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                {' '}
                Raj Kumar
              </Text>
            </View>
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                Phone Number
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                +91 8949952520
              </Text>
            </View>
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                Transaction ID
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                #IRM878748G548
              </Text>
            </View>
          </View>
        </View>
        <Button
          variant="secondary"
          label="Download Invoice"
          onPress={() => {}}
        />
      </View> */}
    </View>
  );
}
