import { FlashList } from '@shopify/flash-list';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React from 'react';
import { Pressable, ScrollView, Text, View } from 'react-native';

import BackButton from '@/components/back-button';
import TransactionsContainer from '@/components/transaction-container';
import { FocusAwareStatusBar } from '@/components/ui';

const TransactionsPage = () => {
  return (
    <View className="flex-1 bg-primary-950 px-5">
      <FocusAwareStatusBar />
      <BackButton
        text="Back"
        text2="Transactions"
        onPress={() => {
          router.back();
        }}
      />

      <View className="mb-4 mt-6 flex-row items-center gap-6">
        <Pressable className="items-center justify-center rounded-[8px] bg-black-0 p-[6px] dark:bg-primary-950">
          <Image
            source={require('../../../assets/images/search-icon.png')}
            contentFit="contain"
            style={{ height: 14, width: 14 }}
          />
        </Pressable>
        <Pressable className="flex-row items-center gap-2">
          <Image
            source={require('../../../assets/images/filter.svg')}
            contentFit="contain"
            style={{ height: 18, width: 18 }}
            tintColor={'white'}
          />
          <Text className="font-inter text-sm font-normal text-blue  dark:text-black-0">
            Filter newest transactions
          </Text>
        </Pressable>
      </View>

      <View className="flex-1">
        <ScrollView
          className="flex-1 gap-4"
          showsVerticalScrollIndicator={false}
        >
          <View className="flex-1">
            <FlashList
              showsVerticalScrollIndicator={false}
              data={Data}
              keyExtractor={(item) => item.id.toString()}
              estimatedItemSize={50}
              renderItem={({ item }) => (
                <TransactionsContainer
                  text={item.text}
                  price={item.price}
                  profileImage={item.profileImage}
                  arrowImage={item.arrowImage}
                />
              )}
            />
          </View>
          <View className="mb-8" />
        </ScrollView>
      </View>
    </View>
  );
};

export default TransactionsPage;

const Data = [
  {
    id: 1,
    text: 'Received on $500 / 24 July',
    price: '$999.23',
    profileImage: require('../../../assets/images/profile.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 2,
    text: 'Received on $9990 / 5  August',
    price: '$505.23',
    profileImage: require('../../../assets/images/avatar.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 3,
    text: 'Received on $500 / 24 July',
    price: '$999.23',
    profileImage: require('../../../assets/images/profile.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 4,
    text: 'Received on $9990 / 5  August',
    price: '$505.23',
    profileImage: require('../../../assets/images/avatar.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 5,
    text: 'Received on $500 / 24 July',
    price: '$999.23',
    profileImage: require('../../../assets/images/profile.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 6,
    text: 'Received on $9990 / 5  August',
    price: '$505.23',
    profileImage: require('../../../assets/images/avatar.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 7,
    text: 'Received on $500 / 24 July',
    price: '$999.23',
    profileImage: require('../../../assets/images/profile.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 8,
    text: 'Received on $9990 / 5  August',
    price: '$505.23',
    profileImage: require('../../../assets/images/avatar.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 9,
    text: 'Received on $500 / 24 July',
    price: '$999.23',
    profileImage: require('../../../assets/images/profile.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 10,
    text: 'Received on $9990 / 5  August',
    price: '$505.23',
    profileImage: require('../../../assets/images/avatar.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 11,
    text: 'Received on $500 / 24 July',
    price: '$999.23',
    profileImage: require('../../../assets/images/profile.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 12,
    text: 'Received on $9990 / 5  August',
    price: '$505.23',
    profileImage: require('../../../assets/images/avatar.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 13,
    text: 'Received on $500 / 24 July',
    price: '$999.23',
    profileImage: require('../../../assets/images/profile.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 14,
    text: 'Received on $9990 / 5  August',
    price: '$505.23',
    profileImage: require('../../../assets/images/avatar.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 15,
    text: 'Received on $500 / 24 July',
    price: '$999.23',
    profileImage: require('../../../assets/images/profile.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
  {
    id: 16,
    text: 'Received on $9990 / 5  August',
    price: '$505.23',
    profileImage: require('../../../assets/images/avatar.png'),
    arrowImage: require('../../../assets/images/downarrow.svg'),
  },
];
