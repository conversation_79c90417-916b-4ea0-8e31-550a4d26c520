import { FlashList } from '@shopify/flash-list';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React from 'react';
import { ScrollView, View, RefreshControl, Text } from 'react-native';
import { useFocusEffect } from 'expo-router';
import { usePastBookings } from '@/hooks/use-bookings';
import BookingsCard from '@/components/bookings-card';
import BookingListSkeleton from '@/components/bookings/booking-list-skeleton';
import EmptyState from '@/components/bookings/empty-state';
import { UserType } from '@/store/user-type-store';

const handleBookingPress = (bookingId: string) => {
  router.push(`/booking-details/${bookingId}`);
};

export default function PastBookings() {
  const { data: bookings, isLoading, isError, refetch, userType } = usePastBookings();

  useFocusEffect(
    React.useCallback(() => {
      refetch();
    }, [refetch])
  );

  if (isLoading) {
    return (
      <View className="w-full flex-1 px-5 dark:bg-primary-950">
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false} refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={refetch}
          />
        }>
          {/* <View className="mt-6">
            <Image
              source={require('../../../assets/images/greatoffer.png')}
              contentFit="contain"
              className="w-full h-[138px]"
            />
          </View> */}
          <View className="mt-3 flex-1">
            <BookingListSkeleton count={3} />
          </View>
        </ScrollView>
      </View>
    );
  }

  if (isError) {
    return (
      <View className="w-full flex-1 px-5 dark:bg-primary-950">
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false} refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={refetch}
          />
        }>
          {/* <View className="mt-6">
            <Image
              source={require('../../../assets/images/greatoffer.png')}
              contentFit="contain"
              className="w-full h-[138px]"
            />
          </View> */}
          <View className="mt-3 flex-1 items-center justify-center">
            <Text className="text-center text-red-500 mb-4">
              Failed to load past bookings
            </Text>
            <Text 
              className="text-center text-blue-500 underline"
              onPress={() => refetch()}
            >
              Tap to retry
            </Text>
          </View>
        </ScrollView>
      </View>
    );
  }

  if (!bookings || bookings.length === 0) {
    return (
      <View className="w-full flex-1 px-5 dark:bg-primary-950">
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false} refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={refetch}
          />
        }>
          {/* <View className="mt-6">
            <Image
              source={require('../../../assets/images/greatoffer.png')}
              contentFit="contain"
              className="w-full h-[138px]"
            />
          </View> */}
          <View className="mt-3 flex-1">
            <EmptyState category="past" userType={userType} />
          </View>
        </ScrollView>
      </View>
    );
  }

  return (
    <View className="w-full flex-1 px-5 dark:bg-primary-950">
      <ScrollView
        className="flex-1"
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isLoading}
            onRefresh={refetch}
          />
        }
      >
        {/* <View className="mt-6">
          <Image
            source={require('../../../assets/images/greatoffer.png')}
            contentFit="contain"
            className="w-full h-[138px]"
          />
        </View> */}
        <View className="mt-3 flex-1">
          <FlashList
            data={bookings}
            showsVerticalScrollIndicator={false}
            estimatedItemSize={50}
            keyExtractor={(item) => item.id}
            renderItem={({ item }) => (
              <BookingsCard 
                booking={item} 
                onPress={() => handleBookingPress(item.id)} 
              />
            )}
          />
          <View className="pb-6" />
        </View>
      </ScrollView>
    </View>
  );
}
