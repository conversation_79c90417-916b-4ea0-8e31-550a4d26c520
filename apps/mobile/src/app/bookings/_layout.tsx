import { Stack } from 'expo-router';

// const Tab = createMaterialTopTabNavigator();
export default function BookingsLayout() {
  return (
    // <View className="flex-1 w-full dark:bg-primary-950">
    //   {/* BackButton */}
    //   <View className="flex-row w-full items-center px-5 justify-between">
    //     <Pressable
    //       className="flex-row items-center gap-2"
    //       onPress={() => {
    //         router.back();
    //       }}
    //     >
    //       <Image
    //         source={require('../../../assets/images/back-icon.svg')}
    //         contentFit="contain"
    //         style={{ height: 12, width: 6 }}
    //       />
    //       <Text className="font-inter text-sm font-medium dark:text-black-50 ">
    //         Back
    //       </Text>
    //     </Pressable>

    //     <Text className="font-PoppinsBold text-xl font-bold dark:text-black-100">
    //       Bookings
    //     </Text>
    //     <Pressable className="flex-row items-center gap-4">
    //       <Image
    //         source={require('../../../assets/images/search-icon.png')}
    //         contentFit="contain"
    //         style={{ height: 18, width: 18 }}
    //       />
    //     </Pressable>
    //   </View>
    // </View>

    <Stack>
      <Stack.Screen name="index" options={{ headerShown: false }} />
      <Stack.Screen name="past-booking" options={{ headerShown: false }} />
      <Stack.Screen name="upcoming-booking" options={{ headerShown: false }} />
    </Stack>
  );
}
