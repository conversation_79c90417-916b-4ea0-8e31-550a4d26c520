import { BottomSheetModal } from '@gorhom/bottom-sheet';
import { useFocusEffect } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import { router } from 'expo-router';
import React, { useCallback, useRef, useState } from 'react';
import { Alert, Dimensions, Image, Pressable, Text, View } from 'react-native';

import BackButton from '@/components/back-button';
const { height, width } = Dimensions.get('screen');

interface LeaderBoardProps {
  id: number;
  selectedId: number | null;
  onSelect: (id: number) => void;
  item: any;
}

function LeaderBoardComponent({ id, item }: LeaderBoardProps) {
  const [selectedId, setSelectedId] = useState<number | null>(null);
  const handleSelect = (id: number) => {
    setSelectedId((prevSelectedId) => (prevSelectedId === id ? null : id));
  };

  return (
    <Pressable
      className={`mb-3 flex-row items-center justify-between p-3 ${selectedId === id ? 'bg-[#E8EDFD]' : 'bg-[#20202033]'} rounded-2xl`}
      onPress={() => handleSelect(id)}
    >
      {/* 1st part */}
      <View className="flex-row items-center">
        <View
          className={` items-center ${selectedId === id ? 'bg-blue' : 'bg-[#20202080]'} rounded-2xl px-3 py-2`}
        >
          <Text className="font-inter text-[10px] font-bold text-black-50">
            {item.id}
          </Text>
        </View>
        <Image
          source={require('../../../assets/images/profile.png')}
          className="ml-2 mr-1 size-8 rounded-full"
        />
        <View>
          <Text
            className={`mb-0.5 font-PoppinsSemiBold text-xs font-semibold  ${selectedId === id ? 'text-blue' : 'text-black-50'}`}
          >
            {item.name}
          </Text>
          <Text
            className={`font-inter text-[10px] font-medium ${selectedId === id ? 'text-primary-950' : 'text-black-300'}`}
          >
            {item.location}
          </Text>
        </View>
      </View>

      {/* 2nd part */}
      <View className="flex-row items-center">
        <View
          className={`mr-2 ${selectedId === id ? 'bg-[#20202033]' : 'bg-[#FDFDFD1A]'} rounded-full p-2`}
        >
          <Image
            className="size-3.5"
            source={require('../../../assets/images/trophy.png')}
          />
        </View>
        <View
          className={`mr-2 ${selectedId === id ? 'bg-[#20202033]' : 'bg-[#FDFDFD1A]'} rounded-full p-2`}
        >
          <Image
            className="size-3.5"
            source={require('../../../assets/images/medal.png')}
          />
        </View>
      </View>

      {/* 3rd part */}
      <View>
        <Text
          className={`font-PoppinsSemiBold text-base font-semibold ${selectedId === id ? 'text-blue' : 'text-black-50'}`}
        >
          12069
        </Text>
        <Text
          className={`font-PoppinsSemiBold text-base font-semibold ${selectedId === id ? 'text-blue' : 'text-black-50'}`}
        >
          Points
        </Text>
      </View>
    </Pressable>
  );
}

export default function LeaderBoard() {
  const bottomSheetModalRef = useRef<BottomSheetModal>(null);
  const [isModalVisible, setIsModalVisible] = useState(true);

  useFocusEffect(
    useCallback(() => {
      // When the screen comes into focus
      if (isModalVisible && bottomSheetModalRef.current) {
        bottomSheetModalRef.current.present();
      }

      // Optional: Close the modal when navigating away
      return () => {
        if (bottomSheetModalRef.current) {
          bottomSheetModalRef.current.dismiss();
        }
        setIsModalVisible(false);
      };
    }, [isModalVisible])
  );

  return (
    <View className="items-center justify-center">
      <View className="w-full px-5">
        {/* Top heading */}
        <View className="flex-row items-center justify-between">
          <BackButton
            text="Back"
            text2="Leaderboard"
            text3="Help"
            onPress={() => {
              router.back();
            }}
            onPress3={() => {
              Alert.alert('Error', 'This function is not defined');
            }}
          />
        </View>

        <View>
          {/* leaderboard */}
          <View className="mb-1 mt-6 flex-row items-center justify-between">
            {/* 2nd */}
            <View className="mt-9 items-center">
              <Image
                source={require('../../../assets/images/profile.png')}
                className="size-20 rounded-full border border-secondary-600"
              />
              <View className="absolute relative inset-x-0 bottom-4 items-center justify-center self-center rounded-full bg-secondary-550 px-2.5">
                <Text className="font-PoppinsSemiBold text-base font-semibold text-black-0">
                  2
                </Text>
              </View>

              {/* text part */}
              <View
                className="self-start rounded-xl bg-[#20202080] px-2 py-1"
                style={{ height: height * 0.0644 }}
              >
                <Text className="font-PoppinsBold text-base font-bold text-black-50">
                  Meghan Jes...
                </Text>
                <View className="flex-row items-center">
                  <Image
                    source={require('../../../assets/images/2nd.png')}
                    className="size-5"
                  />
                  <Text className="font-inter text-xs font-normal text-charcoal-50">
                    1.8 Lakh pts
                  </Text>
                </View>
              </View>
            </View>

            {/* 1st */}
            <View className=" items-center">
              <Image
                source={require('../../../assets/images/crown.png')}
                className="mb-1 size-8 "
              />
              <Image
                source={require('../../../assets/images/profile.png')}
                className="size-20 rounded-full border border-yellow"
              />
              <View className="absolute relative inset-x-0 bottom-4 items-center justify-center self-center rounded-full bg-yellow px-2.5">
                <Text className="font-PoppinsSemiBold text-base font-semibold text-black-0">
                  1
                </Text>
              </View>

              {/* text part */}
              <View
                className="self-start rounded-xl bg-[#20202080] px-2 py-1"
                style={{ height: height * 0.0783 }}
              >
                <Text className="font-PoppinsBold text-base font-bold text-black-50">
                  Suraj Sharma
                </Text>
                <View className="flex-row items-center">
                  <Image
                    source={require('../../../assets/images/1st.png')}
                    className="size-5"
                  />
                  <Text className="font-inter text-xs font-normal text-charcoal-50">
                    4+ Lakh pts
                  </Text>
                </View>
              </View>
            </View>

            {/* 3rd */}
            <View className="mt-9 items-center">
              <Image
                source={require('../../../assets/images/profile.png')}
                className="size-20 rounded-full border border-secondary-600"
              />
              <View className="absolute relative inset-x-0 bottom-4 items-center justify-center self-center rounded-full bg-secondary-550 px-2.5">
                <Text className="font-PoppinsSemiBold text-base font-semibold text-black-0">
                  3
                </Text>
              </View>

              {/* text part */}
              <View
                className="self-start rounded-xl bg-[#20202080] px-2 py-1"
                style={{ height: height * 0.0558 }}
              >
                <Text className="font-PoppinsBold text-base font-bold text-black-50">
                  Rohan
                </Text>
                <View className="flex-row items-center">
                  <Image
                    source={require('../../../assets/images/3rd.png')}
                    className="size-5"
                  />
                  <Text className="font-inter text-xs font-normal text-charcoal-50">
                    70,000 pts
                  </Text>
                </View>
              </View>
            </View>
          </View>

          <View>
            <Image
              source={require('../../../assets/images/banner.png')}
              style={{ height: height * 0.159, width: width * 0.907 }}
            />
          </View>
        </View>
      </View>

      {/* Bottom Sheet Modal */}
      <BottomSheetModal
        ref={bottomSheetModalRef}
        snapPoints={['50%', '80%']}
        backgroundStyle={{ backgroundColor: '#071952' }}
        handleIndicatorStyle={{
          backgroundColor: '#7291F3',
          width: 82,
          height: 5,
          borderRadius: 100,
        }}
        enablePanDownToClose={false}
      >
        <View className="flex-1 px-5">
          <FlashList
            data={Data}
            showsVerticalScrollIndicator={false}
            estimatedItemSize={50}
            renderItem={({ item }) => (
              <View>
                <LeaderBoardComponent
                  id={item.id}
                  item={item}
                  selectedId={null}
                  onSelect={function (id: number): void {
                    throw new Error('Function not implemented.');
                  }}
                />
              </View>
            )}
          />
          <View className="mb-10" />
        </View>
      </BottomSheetModal>
    </View>
  );
}

const Data = [
  {
    id: 1,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 2,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 3,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 4,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 5,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 6,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 7,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 8,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 9,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 10,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 11,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 12,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 13,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 14,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 15,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 16,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 17,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 18,
    name: 'Suraj Khandwal',
    location: 'Jaipur, Rajsthan',
    points: '1900',
    source: require('../../../assets/images/av13.png'),
  },
];
