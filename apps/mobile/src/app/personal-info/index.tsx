import { Env } from '@env';
import { zodResolver } from '@hookform/resolvers/zod';
import { Image } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import {
  Modal,
  Pressable,
  ScrollView,
  Text,
  TouchableOpacity,
  View,
} from 'react-native';
import { ID } from 'react-native-appwrite';
import { type z } from 'zod';

import BackButton from '@/components/back-button';
import InputLabelled from '@/components/input-labelled';
import InputText from '@/components/input-txt';
import { Button, SelectionGroup, Switch } from '@/components/ui';
import { userSchema } from '@/form-schema/form-schema';
import { account, storage } from '@/lib/appwrite';
import { getUserData, updateUserData } from '@/lib/user-data-service';
import { useMutation, useQuery } from '@tanstack/react-query';
import { trpc } from '@/lib/api';
import LanguagesSelector from '@/components/ui/languages-selector';
import GenderSelector from '@/components/ui/gender-selector';
import { Ionicons } from '@expo/vector-icons';

const PersonalInfoPage = () => {
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [profilePicUrl, setProfilePicUrl] = useState<string | null>(null);

  // Getting the type of user from zustand store
  // const userType = userTypeStore((state) => state.userType);

  const { data: profile, refetch, isLoading: isLoadingProfile, isError: isErrorProfile } = useQuery(trpc.userProfiles.get.queryOptions());

  const updateProfileMutation = useMutation(trpc.userProfiles.update.mutationOptions());
  const updateProfilePictureMutation = useMutation(trpc.userProfiles.updateProfilePicture.mutationOptions());

  const {
    watch,
    control,
    handleSubmit,
    reset,
    setValue,
    formState: { errors },
  } = useForm<z.infer<typeof userSchema>>({
    resolver: zodResolver(userSchema),
    defaultValues: {
      name: '',
      phone: '',
      email: '',
      about: '',
      typeOfTraveler: 'SOLO',
      genderPreference: 'MALE',
      openToAllGenders: false,
      languages: [],
      gender: 'MALE',
      bookingFor: 'SELF',
      // typeOfUser: userType,
    },
  });

  useEffect(() => {
    if (profile) {
      reset({
        name: profile.name,
        phone: profile.phone ?? '',
        email: profile.email ?? '',
        about: profile.about ?? '',
        typeOfTraveler: profile.typeOfTraveller ?? 'SOLO',
        gender: profile.gender ?? 'MALE',
        genderPreference: profile.genderPreference ?? 'MALE',
        openToAllGenders: profile.openToAllGenders ?? false,
        languages: profile.languages.map((language) => language.id) ?? [],
        userProfile: profile.userProfile ?? '',
        userProfileUrl: profile.userProfileUrl ?? '',
      });
      setProfilePicUrl(profile.userProfileUrl ?? '');
    }
  }, [profile]);

  const onSubmit = async (data: z.infer<typeof userSchema>) => {
    console.log('Submitting profile data:', data);
    setIsLoading(true);
    
    try {
      // Update basic account details in Appwrite
      await account.updateName(data.name);
      await updateUserData({
        email: data.email ?? '',
      });

      // Update profile via API
      await updateProfileMutation.mutateAsync({
        name: data.name,
        lastName: data.lastName,
        email: data.email,
        phone: data.phone,
        about: data.about,
        // typeOfTraveler: data.typeOfTraveler,
        gender: data.gender,
        genderPreference: data.genderPreference,
        openToAllGenders: data.openToAllGenders,
        bookingFor: data.bookingFor,
        typeOfTraveler: data.typeOfTraveler,
        languages: data.languages,
        userProfile: data.userProfile,
        userProfileUrl: data.userProfileUrl,
      });

      // Reload profile after update
      await refetch();
      console.log('Profile updated successfully');
    } catch (error) {
      console.error('Error saving user details:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onError = (errors: any) => {
    console.log(errors);
  };

  const PickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.All,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 0.5,
    });

    if (!result.canceled && result.assets.length > 0) {
      const image = result.assets[0];
      if (image) {
        try {
          // Delete old profile picture if exists
          const userData = await getUserData();
          if (userData?.userProfile) {
            await storage
              .deleteFile(Env.STORAGE_BUCKET_ID, userData.userProfile)
              .catch((err) => {
                console.log('error in deleting user profile', err);
              });
          }

          // Upload new profile picture
          const uploadedUserPic = await storage
            .createFile(
              Env.STORAGE_BUCKET_ID,
              ID.unique(),
              {
                uri: image.uri,
                type: image.mimeType ?? '',
                name: image.fileName ?? '',
                size: image.fileSize ?? 0,
              },
              ['read("any")']
            )
            // .catch((err) => {
            //   console.log('error in uploading user profile', err);
            //   throw err;
            // });

          if (uploadedUserPic) {
            // Update local user data
            // await updateUserData({
            //   userProfile: uploadedUserPic.$id,
            // });

            // Get the file URL
            const url = storage.getFileView(
              Env.STORAGE_BUCKET_ID,
              uploadedUserPic.$id
            );
            console.log('url is', url);

            // Update profile picture via API
            await updateProfilePictureMutation.mutateAsync({
              userProfile: uploadedUserPic.$id,
              userProfileUrl: url.href,
            });

            // Update local state
            setProfilePicUrl(url.href);
            setValue('userProfileUrl', url.href);

            // Refetch profile to get updated data
            await refetch();
          }
        } catch (error) {
          console.error('Error updating profile picture:', error);
        }
      }
    }
  };

 

  const [openModal, setOpenModal] = useState(false);
  const renderModal = () => {
    return (
      <Modal visible={openModal} animationType="slide" transparent={true}>
        <View className="flex-1  items-center justify-center px-5 dark:bg-bgmodal ">
          <View className="rounded-2xl  bg-white  px-5 py-6 dark:bg-black-950  ">
            <View className="gap-2.5">
              <Text className="font-PoppinsSemiBold text-xl  font-semibold text-black-950 dark:text-black-50">
                Change your profile photo
              </Text>

              <TouchableOpacity
                className="flex-row items-center gap-5 p-3"
                onPress={() => {
                  setOpenModal(false);
                  PickImage();
                }}
              >
                <Image
                  source={require('../../../assets/images/gallery.svg')}
                  contentFit="contain"
                  style={{ height: 20, width: 21.818 }}
                />
                <Text className="font-Poppins text-bold text-[16px] leading-[20px] text-black-0">
                  Choose photo
                </Text>
              </TouchableOpacity>

              <TouchableOpacity className="flex-row items-center gap-5 p-3">
                <Image
                  source={require('../../../assets/images/profile-camera.svg')}
                  contentFit="contain"
                  style={{ height: 20, width: 21.818 }}
                />
                <Text className="font-Poppins text-bold text-[16px] leading-[20px] text-black-0">
                  Take photo
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className="flex-row items-center gap-5 p-3"
                onPress={() => {
                  setOpenModal(false);
                }}
              >
                <Image
                  source={require('../../../assets/images/delete.svg')}
                  contentFit="contain"
                  style={{ height: 20, width: 21.818 }}
                />
                <Text className="font-Poppins text-bold text-[16px] leading-[20px] text-black-0">
                  Remove current photo
                </Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  return (
    <View className="w-full flex-1 justify-between gap-6 bg-primary-950">
      <View className="flex-1 gap-6 ">
        <View className="w-full gap-2.5 rounded-b-2xl px-5 pb-6 pt-2 dark:bg-blue">
          <BackButton
            text="Back"
            onPress={() => {
              router.back();
            }}
            text2="Edit Profile"
            text3="Save"
          />

          <TouchableOpacity
            className="relative items-center"
            onPress={() => {
              setOpenModal(true);
            }}
          >
            <View>
              <View
                style={{
                  width: 80,
                  height: 80,
                  borderRadius: 40,
                  overflow: 'hidden',
                }}
              >
                {profilePicUrl ? (
                  <Image
                    source={profilePicUrl}
                    contentFit="cover"
                    className="rounded-full"
                    style={{ width: 80, height: 80 }}
                  />
                ) : (
                  <View
                    style={{
                      width: 80,
                      height: 80,
                      borderRadius: 30,
                      backgroundColor: '#E5E7EB',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Ionicons name="person-circle" size={80} color="#9CA3AF" />
                  </View>
                )}
              </View>

              <TouchableOpacity onPress={PickImage}>
                <View className="absolute bottom-0 left-0 flex-row items-center gap-[3.47px] rounded-xl bg-black-950 px-[6.93px] py-[5.2px]">
                  <Image
                    source={require('../../../assets/images/camera.svg')}
                    contentFit="contain"
                    tintColor={'white'}
                    style={{ height: 12, width: 12 }}
                  />
                  <Text className="font-inter text-[9px] font-medium leading-3 dark:text-black-300">
                    {profilePicUrl ? 'Change Photo' : 'Add Photo'}
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        </View>

        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          <View className=" flex-1 gap-5 bg-primary-950 px-5">
            {/* name */}
            <InputLabelled label="Name">
              <Controller
                control={control}
                name="name"
                render={({ field: { onChange, value } }) => (
                  <InputText
                    onChangeText={onChange}
                    value={value}
                    placeholder="Enter your name"
                  />
                )}
              />
            </InputLabelled>
            {errors.name && (
              <Text className="text-red-500">
                {errors.name.message?.toString()}
              </Text>
            )}

            {/* phone number */}
            <InputLabelled label="Phone Number">
              <Controller
                control={control}
                name="phone"
                render={({ field: { value } }) => (
                  <InputText
                    className="border-transparent"
                    placeholder="Enter your phone number"
                    value={value}
                    editable={false}
                  />
                )}
              />
            </InputLabelled>
            {errors.phone && (
              <Text className="text-red-500">
                {errors.phone.message?.toString()}
              </Text>
            )}

            {/* email */}
            <InputLabelled label="Email">
              <Controller
                control={control}
                name="email"
                render={({ field: { onChange, value } }) => (
                  <InputText
                    placeholder="Enter your email"
                    autoCapitalize="none"
                    value={value}
                    onChangeText={onChange}
                  />
                )}
              />
            </InputLabelled>
            {errors.email && (
              <Text className="text-red-500">
                {errors.email.message?.toString()}
              </Text>
            )}

            {/* are you a traveller */}
            <InputLabelled label="Are you a ______ Traveler?">
              <Controller
                control={control}
                name="typeOfTraveler"
                render={({ field: { onChange, value } }) => (
                  <SelectionGroup
                      options={[
                        { value: 'SOLO', label: 'Solo' },
                        { value: 'FAMILY', label: 'Family' },
                        { value: 'GROUP', label: 'Group' },
                      ]}
                      value={value}
                      onChange={onChange}
                      error={errors.typeOfTraveler?.message}
                    />
                )}
              />
            </InputLabelled>
            {errors.typeOfTraveler && (
              <Text className="text-red-500">
                {errors.typeOfTraveler.message?.toString()}
              </Text>
            )}

            {/* your gender */}
            <InputLabelled label="Your Gender?">
              <Controller
                control={control}
                name="gender"
                render={({ field: { value, onChange } }) => (
                  <GenderSelector value={value} onChange={onChange} />
                )}
              />
            </InputLabelled>

            {/* gender preference */}
            <View>
              <View className="flex-row items-center justify-between">
                <InputLabelled
                  label="Gender Preference"
                  className="flex-row items-center gap-4"
                >
                  <Controller
                    control={control}
                    name="openToAllGenders"
                    render={({ field: { onChange, value } }) => (
                      <Switch
                        accessibilityLabel=""
                        label="Open for all genders"
                        className="text-black-300"
                        checked={value}
                        onChange={(checked) => {
                          onChange(checked);
                        }}
                      />
                    )}
                  />
                </InputLabelled>
              </View>
              {!watch('openToAllGenders') && (
                <Controller
                  control={control}
                  name="genderPreference"
                  render={({ field: { onChange, value } }) => (
                    <SelectionGroup
                      options={[
                        { value: 'MALE', label: 'Male' },
                        { value: 'FEMALE', label: 'Female' },
                        { value: 'OTHER', label: 'Other' },
                      ]}
                      value={value}
                      onChange={onChange}
                      error={errors.genderPreference?.message}
                    />
                  )}
                />
              )}
              {errors.genderPreference && (
                <Text className="text-red-500">
                  {errors.genderPreference.message?.toString()}
                </Text>
              )}
            </View>

            {/* language traveller speaks */}
            <InputLabelled label="Language Traveler Speaks">
              <Controller
                control={control}
                name="languages"
                render={({ field: { value, onChange } }) => (
                  <LanguagesSelector
                    languageIds={value ?? []}
                    setLanguageIds={(languageIds) => onChange(languageIds)}
                  />
                )}
              />
            </InputLabelled>
            {errors.languages && (
              <Text className="text-red-500">
                {errors.languages.message?.toString()}
              </Text>
            )}

            {/* write about you */}
            <InputLabelled label="Write about you">
              <Controller
                control={control}
                name="about"
                render={({ field: { onChange, value } }) => (
                  <InputText
                    placeholder="Write about yourself"
                    value={value}
                    onChangeText={onChange}
                  />
                )}
              />
            </InputLabelled>
            {errors.about && (
              <Text className="text-red-500">
                {errors.about.message?.toString()}
              </Text>
            )}
          </View>
        </ScrollView>
      </View>

      <View className="mb-8 px-5">
        <Button
          variant="secondary"
          label="Save Changes"
          loading={isLoading}
          onPress={handleSubmit(onSubmit, onError)}
        />
      </View>
      {renderModal()}
    </View>
  );
};

export default PersonalInfoPage;
