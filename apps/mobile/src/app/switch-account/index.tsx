
import { Image } from 'expo-image';
import { router } from 'expo-router';
import React from 'react';
import {
  Dimensions,
  ImageBackground,
  Pressable,
  Text,
  View,
} from 'react-native';

import { Button } from '@/components/ui';
import { UserType, userTypeStore } from '@/store/user-type-store';
const { height, width } = Dimensions.get('screen');

const SwitchAccountPage = () => {
  const changeUserType = userTypeStore((state) => state.changeUserType);

  const createTargetUserAccount = async () => {
    changeUserType(UserType.COMPANION);
    router.push('/form/ready-as-companion/companion-details?onboarding=true');
  };

  return (
    <View className="flex-1">
      <ImageBackground
        source={require('../../../assets/images/globalmap.png')}
        className="py-safe"
        style={{
          height: height * 1,
          width: width * 1,
        }}
      >
        <View className="flex-1 dark:bg-primary-950">
          {/* Section1 Image */}
          <View>
            <Pressable
              className="flex-row items-center gap-2"
              onPress={() => {
                router.back();
              }}
            >
              <Image
                source={require('../../../assets/images/back-icon.svg')}
                contentFit="contain"
                style={{ height: 12, width: 6 }}
              />
              <Text className="font-inter text-sm font-medium dark:text-black-50 ">
                Back
              </Text>
            </Pressable>
            <Image
              source={require('/assets/images/onboarding2.png')}
              contentFit="cover"
              style={{
                borderRadius: 32,
                width: width * 1,
                height: height * 0.532,
              }}
            />
          </View>
          {/* Section 2 */}
          <View className="gap-7 px-5 pb-9 pt-6">
            <Text className="font-PoppinsBold text-xl font-bold dark:text-black-200">
              Become a Companion on Thedal
            </Text>

            <View>
              <View className="mb-2 flex-row flex-wrap items-center">
                <Text className="font-PoppinsSemiBold text-base font-semibold dark:text-black-100">
                  Earn Extra Income:
                </Text>
                <Text className="font-inter text-base dark:text-black-300">
                  {' '}
                  Receive compensation for your time and companionship.
                </Text>
              </View>
              <View className="flex-row flex-wrap items-center">
                <Text className="font-PoppinsSemiBold text-base font-semibold dark:text-black-100">
                  Enhance Your Social Skills:
                </Text>
                <Text className="font-inter text-base dark:text-black-300">
                  {' '}
                  Develop your communication and interpersonal skills.
                </Text>
              </View>
            </View>

            <Button
              variant="secondary"
              label="Create account as Companion"
              onPress={() => createTargetUserAccount()}
            />
          </View>
        </View>
      </ImageBackground>
    </View>
  );
};

export default SwitchAccountPage;
