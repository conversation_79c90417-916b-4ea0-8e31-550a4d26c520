import { type BottomSheetModal } from '@gorhom/bottom-sheet';
import { useFocusEffect } from '@react-navigation/native';
import { FlashList } from '@shopify/flash-list';
import { format } from 'date-fns';
import { BlurView } from 'expo-blur';
import { router } from 'expo-router';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  Dimensions,
  Image,
  ImageBackground,
  Platform,
  Pressable,
  RefreshControl,
  StyleSheet,
  Text,
  TextInput,
  View,
} from 'react-native';
import { showMessage } from 'react-native-flash-message';

import BackButton from '@/components/back-button';
import { Modal, useModal } from '@/components/ui';
import { Button } from '@/components/ui/button';
import { useWalletPayment } from '@/hooks/use-wallet-payment';

const { height, width } = Dimensions.get('screen');

const WalletPage = () => {
  // Properly typing the ref with BottomSheetModal type
  const bottomSheetModalRef = useRef<BottomSheetModal>(null); // Type the ref
  const [loading, setLoading] = useState(false);
  // const { ref, present, dismiss } = useModal();
  const {
    ref: createCardRef,
    present: presentCreateCard,
    dismiss: dismissCreateCard,
  } = useModal();

  // Use the new tRPC-based wallet hook
  const { 
    walletBalance, 
    paymentHistory, 
    refetchBalance, 
    refetchHistory 
  } = useWalletPayment();

  // UseEffect equivalent with useFocusEffect to open the BottomSheetModal when screen is focused
  useFocusEffect(
    useCallback(() => {
      if (bottomSheetModalRef.current) {
        bottomSheetModalRef.current.present(); // This will open the BottomSheetModal when screen is focused
      }
    }, [])
  );

  const getWalletTransactions = async () => {
    setLoading(true);
    try {
      await Promise.all([refetchBalance(), refetchHistory()]);
    } catch (error: any) {
      showMessage({
        message: 'Error fetching transactions',
        description: error?.message ?? 'Error fetching wallet transactions',
        type: 'danger',
      });
      console.log('error', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    getWalletTransactions();
  }, []);

  // Function to open the modal
  const openCreateCardModal = () => {
    presentCreateCard();
  };

  // Function to close the modal
  const closeCreateCardModal = () => {
    dismissCreateCard();
  };

  const handleNavigate = () => {
    bottomSheetModalRef.current?.close();
    router.push('/wallet/add-money-2-wallet');
  };

  // Calculate total amount from wallet balance
  const totalAmount = walletBalance?.balance || 0;

  return (
    <View className="flex-1">
      <ImageBackground
        source={require('../../../assets/images/globalmap.png')}
        resizeMode="contain"
        className="my-5"
      >
        <View className="px-5">
          {/* Top heading */}
          <View className="flex-row items-center justify-between">
            <BackButton
              text="Back"
              text2="Wallet"
              text3="&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"
              onPress={() => {
                router.back();
              }}
            />

          </View>

          {/* Wallet amount */}
          {Platform.OS === 'ios' ? (
            <BlurView
              intensity={10}
              style={{ backgroundColor: '#1539AF66', borderRadius: 12 }}
              className="mt-6 flex-row items-center justify-between px-4 py-2.5"
            >
              <View>
                <Text className="font-PoppinsBold text-4xl font-bold text-black-0">
                  $ {(totalAmount / 100).toFixed(2)}
                </Text>
                <Text className="mt-1 font-PoppinsRegular text-sm font-normal text-black-100">
                  Wallet Balance
                </Text>
              </View>

              <Pressable
                className="rounded-xl border border-borderdark bg-[#FDFDFD1A] px-6 py-3.5"
                onPress={handleNavigate}
              >
                <Text className="font-inter text-base font-medium text-black-0">
                  Add money
                </Text>
              </Pressable>
            </BlurView>
          ) : Platform.OS === 'android' ? (
            <View
              style={{ backgroundColor: '#1539AF66', borderRadius: 12 }}
              className="mt-6 flex-row items-center justify-between px-4 py-2.5"
            >
              <View>
                <Text className="font-PoppinsBold text-4xl font-bold text-black-0">
                  $ {(totalAmount / 100).toFixed(2)}
                </Text>
                <Text className="mt-1 font-PoppinsRegular text-sm font-normal text-black-100">
                  Wallet Balance
                </Text>
              </View>

              <Pressable
                className="rounded-xl border border-borderdark bg-[#FDFDFD1A] px-6 py-3.5"
                onPress={handleNavigate}
              >
                <Text className="font-inter text-base font-medium text-black-0">
                  Add money
                </Text>
              </Pressable>
            </View>
          ) : null}
        </View>
      </ImageBackground>

      <View className="flex-1 px-5">
        <View className="mb-4 flex-row items-center justify-between">
          <Text className="font-PoppinsMedium text-xl font-medium text-black-100">
            Payment History
          </Text>
        </View>
        <FlashList
          data={paymentHistory}
          showsVerticalScrollIndicator={false}
          estimatedItemSize={50}
          refreshControl={
            <RefreshControl
              refreshing={loading}
              onRefresh={getWalletTransactions}
            />
          }
          renderItem={({ item }) => (
            <>
              <Pressable className="mb-3 flex-row items-center justify-between border-b-[0.55px] border-secondary-750 px-3 py-2">
                <View className="flex-col">
                  <Text className="font-inter text-sm text-black-300 flex-grow">
                    {item.description?.slice(0, 30) || 'No description'} {item.description?.length && item.description?.length > 30 ? '...' : ''}
                  </Text>
                  <Text className="font-inter text-xs text-black-300">
                    {format(new Date(item.createdAt), 'dd MMM yyyy hh:mm a')}
                  </Text>
                </View>
                {item.type === 'credit' ? (
                  <View className="flex-row items-center rounded-md bg-chatcardbg p-1">
                    <Image
                      source={require('../../../assets/images/arrowleft.png')}
                      className="mr-1 size-5"
                    />
                    <Text className="font-inter text-base font-semibold text-secondary-650">
                      $ {(item.amount / 100).toFixed(2)}
                    </Text>
                  </View>
                ) : (
                  <View className="flex-row items-center rounded-md bg-chatcardbg p-1">
                    <Image
                      source={require('../../../assets/images/arrowright.png')}
                      className="mr-1 size-5"
                    />
                    <Text className="font-inter text-base font-semibold text-red-500">
                      ${(Math.abs(item.amount) / 100).toFixed(2)}
                    </Text>
                  </View>
                )}
              </Pressable>
            </>
          )}
          ListEmptyComponent={() => (
            <View className="flex-1 items-center justify-center py-8">
              <Text className="font-inter text-base text-black-300">
                No payment history yet
              </Text>
            </View>
          )}
        />
      </View>

      {/* Add Card Modal */}
      <Modal
        ref={createCardRef}
        title="Add a new Card"
        snapPoints={['54%', '80%']}
        backgroundStyle={{ backgroundColor: '#030303' }}
      >
        <View className="flex-1">
          <View className="w-full rounded-t-2xl bg-primary-950 px-5 py-8">
            <View className="mb-5">
              <View>
                <Text className="font-PoppinsMedium text-base font-medium text-black-50">
                  Card Number
                </Text>
                <TextInput
                  placeholder="xxxxxxxxxxxxxxx"
                  keyboardType="numeric"
                  className="rounded-xl px-5 py-3.5"
                />
              </View>
            </View>

            <View className="mb-5 flex-row items-center justify-center">
              <View className="mr-5 flex-1">
                <Text className="font-PoppinsMedium text-base font-medium text-black-50">
                  Expiry Date
                </Text>
                <TextInput
                  placeholder="MM/YY"
                  keyboardType="numeric"
                  className="rounded-xl px-5 py-3.5"
                />
              </View>
              <View className="flex-1">
                <Text className="font-PoppinsMedium text-base font-medium text-black-50">
                  CVC/CVV
                </Text>
                <TextInput
                  placeholder="***"
                  keyboardType="numeric"
                  className="rounded-xl px-5 py-3.5"
                />
              </View>
            </View>

            <View className="mb-5">
              <View>
                <Text className="font-PoppinsMedium text-base font-medium text-black-50">
                  Card Holder Name
                </Text>
                <TextInput
                  placeholder="enter your name"
                  className="rounded-xl px-5 py-3.5"
                />
              </View>
            </View>

            <Button
              variant="darkblue"
              label="Add New Card"
              onPress={function (): void {
                throw new Error('Function not implemented.');
              }}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default WalletPage;

const styles = StyleSheet.create({
  gradientLayer: {
    flex: 1,
    borderRadius: 24,
    // width: width * 0.5581,
    // height: height * 0.1941,
  },
  cardContainer: {
    backgroundColor: '#00071D33',
    borderRadius: 24,
    width: width * 0.5581, // Adjust the width according to your design
    height: height * 0.1941, // Adjust the height according to your design
    // Shadow for iOS
    shadowColor: '#2A51CF', // Shadow color with transparency
    shadowOffset: { width: 0, height: 9 }, // Horizontal and vertical offset
    shadowOpacity: 1, // Full opacity for shadow
    shadowRadius: 49, // Blur radius
    // Elevation for Android (to show shadow)
    elevation: 12, // Elevation (shadow size on Android)
  },
  card2: {
    backgroundColor: '#FDFDFD', // background color
    borderRadius: 24, // border radius
    padding: 20, // padding inside the card
    shadowColor: '#5A5A5A33', // shadow for iOS
    shadowOffset: { width: 0, height: 3 }, // shadow offset
    shadowOpacity: 1, // shadow opacity
    shadowRadius: 9, // shadow blur radius
    elevation: 3, // shadow for Android
    width: width * 0.5581,
    // height: height * 0.1465,
  },
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end', // Position the modal content at the bottom of the screen
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Background dimming effect
  },
});

const Data = [
  {
    id: 1,
    details: 'Received on $500 / 24 July',
    money: '$40.23',
    type: 'received',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 2,
    details: 'Received on $500 / 24 July',
    money: '$43.23',
    type: 'send',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 3,
    details: 'Received on $500 / 24 July',
    money: '$84.23',
    type: 'received',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 4,
    details: 'Received on $500 / 24 July',
    money: '$74.23',
    type: 'send',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 5,
    details: 'Received on $500 / 24 July',
    money: '$65.23',
    type: 'received',
    source: require('../../../assets/images/av13.png'),
  },
  {
    id: 6,
    details: 'Received on $500 / 24 July',
    money: '$87.23',
    type: 'received',
    source: require('../../../assets/images/av13.png'),
    add: require('../../../assets/images/banner.png'),
  },
];
