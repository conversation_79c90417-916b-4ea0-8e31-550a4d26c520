import { Env } from '@env';
import {
  initPaymentSheet,
  presentPaymentSheet,
  StripeProvider,
} from '@stripe/stripe-react-native';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Text, View } from 'react-native';
import { showMessage } from 'react-native-flash-message';
import { TextInput } from 'react-native-gesture-handler';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';

import BackButton from '@/components/back-button';
import { Button } from '@/components/ui';
import { useWalletPayment } from '@/hooks/use-wallet-payment';

const AddMoney2WalletPage = () => {
  const [amount, setAmount] = useState('');
  const [loading, setLoading] = useState(false);
  const { processPayment } = useWalletPayment();

  const handlePayment = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      showMessage({
        message: 'Invalid Amount',
        description: 'Please enter a valid amount',
        type: 'danger',
      });
      return;
    }

    setLoading(true);
    try {
      await processPayment.mutateAsync(parseFloat(amount));
      
      showMessage({
        message: 'Payment Successful',
        description: 'Your wallet has been updated',
        type: 'success',
      });
      
      router.replace('/wallet/successful');
    } catch (error: any) {
      showMessage({
        message: 'Payment Failed',
        description: error?.message || 'Something went wrong',
        type: 'danger',
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <StripeProvider
      publishableKey={Env.STRIPE_PUBLISHABLE_KEY}
      // merchantIdentifier="merchant.identifier" // required for Apple Pay
      // urlScheme="your-url-scheme" // required for 3D Secure and bank redirects
    >
      <KeyboardAvoidingView
        // behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        className="flex flex-1 flex-col justify-between px-5"
      >
        <View>
          {/* Top heading */}
          <View className="flex-row items-center justify-between">
            <BackButton
              text="Back"
              text2="Add money to wallet"
              onPress={() => {
                router.back();
              }}
            />
            <Text />
          </View>

          {/* enter amount part */}
          <View className="mt-16 px-4">
            <View className="items-center justify-center border-b border-green px-5">
              <Text className="mb-5 font-PoppinsRegular text-lg font-normal text-black-300">
                Enter Amount
              </Text>
              <View className="mb-4 flex w-2/5 flex-row items-center justify-center ">
                <Text className="font-Poppins text-4xl text-black-200">$</Text>
                <TextInput
                  className="my-6 w-full rounded-xl px-5 font-PoppinsBold text-4xl font-bold leading-[40px] text-black-200"
                  keyboardType="numeric"
                  value={amount}
                  onChangeText={setAmount}
                  placeholder="0.00"
                />
              </View>
            </View>
          </View>
        </View>

        <View className="mb-8 w-full">
          <Button
            variant="darkblue"
            loading={loading || processPayment.isPending}
            label={loading || processPayment.isPending ? 'Processing...' : 'Proceed'}
            onPress={handlePayment}
            disabled={loading || processPayment.isPending || !amount}
          />
        </View>
      </KeyboardAvoidingView>
    </StripeProvider>
  );
};

export default AddMoney2WalletPage;
