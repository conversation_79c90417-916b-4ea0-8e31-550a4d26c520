import { router } from 'expo-router';
import * as WebBrowser from 'expo-web-browser';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Image, RefreshControl, ScrollView, Text, View } from 'react-native';
import { showMessage } from 'react-native-flash-message';

import BackButton from '@/components/back-button';
import { Button } from '@/components/ui';
import {
  useStripeConnect,
} from '@/hooks/use-stripe-connect';

const ConnectAccountPage = () => {
  const [accountLinkLoading, setAccountLinkLoading] = useState(false);
  const { accountDetails, accountDetailsLoading, refetchAccountDetails, createConnectAccountMutation, createAccountLinkMutation } =
    useStripeConnect();

  const handleGenerateAccountLink = async () => {
    if (!accountDetails?.accountId) return;

    setAccountLinkLoading(true);
    try {
      const { accountLink: url, success } =
        await createAccountLinkMutation.mutateAsync();
      console.log('Account link URL:', url);

      // Open the URL in the browser
      if (success) {
        await WebBrowser.openBrowserAsync(url);
      }
    } catch (error: any) {
      console.log('Error generating account link:', error);
      showMessage({
        message: 'Failed to Generate Link',
        description: error.message,
        type: 'danger',
      });
    } finally {
      setAccountLinkLoading(false);
    }
  };

  if (accountDetailsLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator />
      </View>
    );
  }

  if (!accountDetails) {
    return (
      <ScrollView refreshControl={<RefreshControl refreshing={accountDetailsLoading} onRefresh={refetchAccountDetails} />} className="flex-1 px-5">
        <View>
          {/* Top heading */}
          <View className="flex-row items-center justify-between">
            <BackButton
              text="Back"
              text2="Connect Stripe Account"
              onPress={() => router.back()}
            />
            <Text />
          </View>
        </View>
        <View className="flex-1 items-center justify-center px-5">
          <Text className="font-PoppinsRegular text-base font-normal text-black-100 mb-5">No account details found</Text>
          <Button
            variant="darkblue"
            label="Refresh Account Details"
            onPress={refetchAccountDetails}
          />
        </View>
      </ScrollView>
    );
  }

  return (
    <ScrollView refreshControl={<RefreshControl refreshing={accountDetailsLoading} onRefresh={refetchAccountDetails} />} className="flex-1 px-5">
      <View>
        {/* Top heading */}
        <View className="flex-row items-center justify-between">
          <BackButton
            text="Back"
            text2="Connect Stripe Account"
            onPress={() => router.back()}
          />
          <Text />
        </View>

        {/* Content */}
        <View className="mt-16 items-center justify-center px-4">
          <Image
            source={require('../../../assets/images/stripe-connect.png')}
            className="size-32"
            resizeMode="contain"
          />
          <Text className="mt-8 font-PoppinsBold text-2xl font-bold text-black-200">
            {accountDetails?.accountId
              ? 'Stripe Account Connected'
              : 'Connect Your Stripe Account'}
          </Text>
          
          <View className="mt-4">
            <Text className="text-center font-PoppinsRegular text-base font-normal text-black-100">
              Your Stripe account is connected with ID:{' '}
              {accountDetails.accountId.substring(0, 8)}...
            </Text>

            {accountDetails && (
              <View className="mt-4">
                {/* Show account status */}
                <Text className="text-center font-PoppinsRegular text-base font-semibold text-black-100">
                  Account Status
                </Text>

                <Text className="mt-1 text-center font-PoppinsRegular text-sm text-black-100">
                  Card Payments Enabled:{' '}
                  {accountDetails.capabilities?.card_payments === 'active'
                    ? '✅'
                    : '❌'}
                </Text>

                <Text className="mt-1 text-center font-PoppinsRegular text-sm text-black-100">
                  Transfers Enabled:{' '}
                  {accountDetails.capabilities?.transfers === 'active'
                    ? '✅'
                    : '❌'}
                </Text>

                {/* Show pending requirements if any */}
                {accountDetails.requirements &&
                  accountDetails.requirements.currently_due &&
                  accountDetails.requirements.currently_due.length > 0 && (
                    <View className="mt-4">
                      <Text className="text-center font-PoppinsRegular text-base font-semibold text-black-100">
                        Pending Requirements
                      </Text>

                      {accountDetails.requirements.currently_due.map(
                        (requirement, index) => (
                          <Text
                            key={index}
                            className="mt-1 text-center font-PoppinsRegular text-sm text-black-100"
                          >
                            • {requirement.replace(/_/g, ' ')}
                          </Text>
                        )
                      )}

                      <Text className="mt-2 text-center font-PoppinsRegular text-sm text-black-100">
                        Complete these steps to enable payments
                      </Text>
                    </View>
                  )}
              </View>
            )}
          </View>
        </View>
      </View>

      <View className="my-8 flex w-full flex-col gap-5 space-y-4">
        <Button
          variant="darkblue"
          loading={accountLinkLoading}
          label={
            accountLinkLoading
              ? 'Generating Link...'
              : 'Complete Account Setup'
          }
          onPress={handleGenerateAccountLink}
          disabled={accountLinkLoading}
        />

       {/*  {accountId &&
          accountDetails &&
          accountDetails.requirements &&
          accountDetails.requirements.currently_due &&
          accountDetails.requirements.currently_due.length > 0 && (
            <Button
              variant="outline"
              loading={accountLinkLoading}
              label={
                accountLinkLoading
                  ? 'Generating Link...'
                  : 'Complete Account Setup'
              }
              onPress={handleGenerateAccountLink}
              disabled={loading || accountLinkLoading}
            />
          )}*/}
      </View> 
    </ScrollView>
  );
};

export default ConnectAccountPage;
