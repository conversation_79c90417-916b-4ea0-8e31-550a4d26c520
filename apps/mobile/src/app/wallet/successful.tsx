import { router } from 'expo-router';
import React from 'react';
import { Image, Text, View } from 'react-native';

import BackButton from '@/components/back-button';
import { Button } from '@/components/ui';

const SuccessfulPage = () => {
  return (
    <View className="flex flex-1 flex-col justify-between px-5">
      <View>
        {/* Top heading */}
        <View className="flex-row items-center justify-between">
          <BackButton
            text="Back"
            text2="Payment Successful"
            onPress={() => {
              router.back();
            }}
          />
          <Text />
        </View>

        {/* Success message */}
        <View className="mt-16 items-center justify-center px-4">
          <Image
            source={require('../../../assets/images/green-tick.png')}
            className="size-32"
            resizeMode="contain"
          />
          <Text className="mt-8 font-PoppinsBold text-2xl font-bold text-black-200">
            Payment Successful!
          </Text>
          <Text className="mt-4 text-center font-PoppinsRegular text-base font-normal text-black-100">
            Your wallet has been credited successfully. You can now use this
            balance for your bookings.
          </Text>
        </View>
      </View>

      <View className="mb-8 w-full">
        <Button
          variant="darkblue"
          label="Done"
          onPress={() => {
            router.replace('/wallet');
          }}
        />
      </View>
    </View>
  );
};

export default SuccessfulPage;
