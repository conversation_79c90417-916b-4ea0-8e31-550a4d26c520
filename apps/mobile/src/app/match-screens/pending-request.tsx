import { FlashList } from '@shopify/flash-list';
import { router } from 'expo-router';
import React from 'react';
import {
  ActivityIndicator,
  Alert,
  Dimensions,
  Image,
  Pressable,
  RefreshControl,
  ScrollView,
  Text,
  View,
} from 'react-native';

import PendingRequestComponent from '@/components/pending-request-component';

import InviteModal from './Invite-modal';
import { trpc } from '@/lib/api';
import { useQuery } from '@tanstack/react-query';
import CompanionDetailCard from '@/components/companion-detail-card';
import TravelerDetailCard from '@/components/traveler-detail-card';
import { userTypeStore } from '@/store/user-type-store';
import { Button } from '@/components/ui';
const { height, width } = Dimensions.get('screen');

const EmptyList = () => {
  return (
    <View className="px-5">
      <View className="my-16 items-center justify-center">
        <Image
          source={require('../../../assets/images/pananoreq.png')}
          style={{ height: height * 0.183, width: width * 0.651 }}
        />
      </View>
      <View className="items-center justify-center">
        <Text className="mb-3 text-center font-inter text-base font-medium text-black-100">
          No Request sent or recevied
        </Text>
        <Text className="mb-6 text-center font-inter text-base font-medium text-black-100">
          Send request to companion to connect with enjoy the journey
          together without any hustle.
        </Text>
      </View>
      <Pressable
        className="self-center rounded-xl border border-charcoal-50 bg-primary-950 px-6 py-3.5"
        onPress={() => router.push('/home')}
      >
        <Text className="font-inter text-base font-medium text-charcoal-50">
          Send Request
        </Text>
      </Pressable>
    </View>
  );
};


const MatchedRequestTraveler = () => {
  const { data: matchedRequests, refetch, isLoading, isError } = useQuery(trpc.connectionRequests.getByUser.queryOptions({
    initiator: 'TRAVELER',
    status: 'PENDING',
  }));
  
  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator />
      </View>
    );
  }

  if (isError) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-red-500">Error fetching matched requests</Text>
        <Button
          variant="secondary"
          label="Retry"
          onPress={() => {
            refetch();
          }}
        />
      </View>
    );
  }

  if (!matchedRequests) {
    return <Text className="text-red-500">No matched requests</Text>;
  }

  return (
    <FlashList
      showsVerticalScrollIndicator={false}
      data={matchedRequests.requests}
      keyExtractor={(item) => item.id.toString()}
      estimatedItemSize={300}
      refreshControl={<RefreshControl refreshing={isLoading} onRefresh={() => refetch()} />}
      ListEmptyComponent={EmptyList}
      renderItem={({ item }) => {
        {/* TODO: fix this as ts-ignore is not correct will fix in future */}
        {/* @ts-ignore */}
        return <CompanionDetailCard bookingCompanion={item.bookingCompanion} bookingTraveler={item.bookingTraveler} />
      }}
    />
  );
};

const MatchedRequestCompanion = () => {
  const { data: matchedRequests, isLoading, isError, refetch } = useQuery(trpc.connectionRequests.getByUser.queryOptions({
    initiator: 'COMPANION',
    status: 'PENDING',
  }));

  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator />
      </View>
    );
  }

  if (isError || !matchedRequests) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-red-500">Error fetching matched requests</Text>
        <Button
          variant="secondary"
          label="Retry"
          onPress={() => {
            refetch();
          }}
        />
      </View>
    );
  }

  return (
    <FlashList
      showsVerticalScrollIndicator={false}
      data={matchedRequests.requests}
      keyExtractor={(item) => item.id.toString()}
      estimatedItemSize={300}
      refreshControl={<RefreshControl refreshing={isLoading} onRefresh={() => refetch()} />}
      ListEmptyComponent={EmptyList}
      renderItem={({ item }) => {
        {/* TODO: fix this as ts-ignore is not correct will fix in future */}
        {/* @ts-ignore */}
        return <TravelerDetailCard bookingTraveler={item.bookingTraveler} bookingCompanion={item.bookingCompanion} />
      }}
    />
  );
};


const PendingRequest = () => {
  const userType = userTypeStore((state) => state.userType);
  return (
    <View className="flex-1 pb-25 px-5">
      {userType === 'TRAVELLER' ? <MatchedRequestTraveler /> : <MatchedRequestCompanion />}
    </View>
  );
};

export default PendingRequest;
