import { FlashList } from '@shopify/flash-list';
import { router } from 'expo-router';
import React from 'react';
import { ActivityIndicator, ScrollView, View, Text, RefreshControl, Pressable } from 'react-native';

import { trpc } from '@/lib/api';
import { useQuery } from '@tanstack/react-query';
import CompanionDetailCard from '@/components/companion-detail-card';
import TravelerDetailCard from '@/components/traveler-detail-card';
import { userTypeStore } from '@/store/user-type-store';
import { Image } from 'expo-image';
import { Dimensions } from 'react-native';
import { Button } from '@/components/ui';

const { height, width } = Dimensions.get('screen');

// const MatchedRequestComponent: React.FC<{
//   bookingCompanion: IBookingCompanion;
//   bookingTraveler: IBookingTraveler;
//   onPressChat: () => void;
//   onPressProfile: () => void;
// }> = ({
//   onPressChat,
//   onPressProfile,
//   RatingNum,
//   title,
//   subtitle,
// }) => {
//   return (
//     <View className="px-5 py-5 rounded-[8px] bg-black-0 gap-3 flex-1 mt-4  dark:bg-darkcardbg">
//       <View className="flex-row items-center flex-1 gap-8">
//         <View className="flex-row  gap-2 flex-1 self-start ">
//           <Image
//             source={require('../../../assets/images/profile.png')}
//             resizeMode="contain"
//             style={{ width: 50, height: 50 }}
//           />
//           <View className="gap-1  ">
//             <Text className="text-green  font-bold leading-6 text-[16px] dark:text-green">
//               {title}
//             </Text>
//             <Text className="text-black-950 font-normal leading-5 text-[14px] dark:text-black-0 text-wrap">
//               {subtitle}
//             </Text>
//           </View>
//         </View>

//         <View className="flex-grow flex-1 gap-1 items-center">
//           <View className="flex-row gap-2 items-center self-end">
//             <Image
//               source={require('../../assets/images/single-star.png')}
//               resizeMode="contain"
//               style={{ height: 14, width: 14 }}
//             />
//             <Image
//               source={require('../../assets/images/single-star.png')}
//               resizeMode="contain"
//               style={{ height: 14, width: 14 }}
//             />
//             <Image
//               source={require('../../assets/images/single-star.png')}
//               resizeMode="contain"
//               style={{ height: 14, width: 14 }}
//             />

//             <Text className="text-black-950 dark:text-black-0">
//               {RatingNum}
//             </Text>
//           </View>

//           <View className="px-[10px] py-[6px]  bg-black-950 flex-row items-center gap-1 rounded-[8px] dark:bg-black-50 self-end">
//             <Image
//               source={require('../../assets/images/plane.png')}
//               resizeMode="contain"
//               style={{ height: 12, width: 12 }}
//               tintColor={'#071952'}
//             />
//             <Text className=" font-inter text-black-950 font-medium leading-[14px] text-[11px] dark:text-blue">
//               Solo Traveler
//             </Text>
//           </View>
//         </View>
//       </View>

//       <View className="flex-row items-center justify-between">
//         <Pressable
//           className="bg-black-950 rounded-[12px] items-center justify-center dark:bg-black-0"
//           onPress={onPressChat}
//           style={{ height: height * 0.0472, width: width * 0.393 }}
//         >
//           <Text className=" font-inter leading-5 font-medium text-[14px] text-black-0  dark:text-blue">
//             Start Chat
//           </Text>
//         </Pressable>

//         <Pressable
//           className="bg-black-950 rounded-[12px] items-center justify-center dark:bg-darkblackbutton"
//           style={{ height: height * 0.0472, width: width * 0.393 }}
//           onPress={onPressProfile}
//         >
//           <Text className="font-inter leading-5 font-medium text-[14px] text-black-0  dark:text-black-100">
//             View Profile
//           </Text>
//         </Pressable>
//       </View>
//     </View>
//   );
// };



// import InviteModal from './Invite-modal';

const EmptyList = () => {
  return (
    <View className="px-5">
      <View className="my-16 items-center justify-center">
        <Image
          source={require('../../../assets/images/pananoreq.png')}
          style={{ height: height * 0.183, width: width * 0.651 }}
        />
      </View>
      <View className="items-center justify-center">
        <Text className="mb-3 text-center font-inter text-base font-medium text-black-100">
          No Matched Requests
        </Text>
        <Text className="mb-6 text-center font-inter text-base font-medium text-black-100">
          Send request to companion to connect with enjoy the journey
          together without any hustle.
        </Text>
      </View>
      <Pressable
        className="self-center rounded-xl border border-charcoal-50 bg-primary-950 px-6 py-3.5"
        onPress={() => router.push('/home')}
      >
        <Text className="font-inter text-base font-medium text-charcoal-50">
          Send Request
        </Text>
      </Pressable>
    </View>
  );
};

const MatchedRequestTraveler = () => {
  const { data: matchedRequests, refetch, isLoading, isError } = useQuery(trpc.connectionRequests.getByUser.queryOptions({
    status: 'ACCEPTED',
  }));
  
  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator />
      </View>
    );
  }

  if (isError) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-red-500">Error fetching matched requests</Text>
        <Button
          variant="secondary"
          label="Retry"
          onPress={() => {
            refetch();
          }}
        />
      </View>
    );
  }

  if (!matchedRequests) {
    return <Text className="text-red-500">No matched requests</Text>;
  }

  return (
    <FlashList
      showsVerticalScrollIndicator={false}
      data={matchedRequests.requests}
      keyExtractor={(item) => item.id.toString()}
      estimatedItemSize={300}
      refreshControl={<RefreshControl refreshing={isLoading} onRefresh={() => refetch()} />}
      ListEmptyComponent={EmptyList}
      renderItem={({ item }) => {
        {/* TODO: fix this as ts-ignore is not correct will fix in future */}
        {/* @ts-ignore */}
        return <CompanionDetailCard bookingCompanion={item.bookingCompanion} bookingTraveler={item.bookingTraveler} />
      }}
    />
  );
};

const MatchedRequestCompanion = () => {
  const { data: matchedRequests, isLoading, isError, refetch } = useQuery(trpc.connectionRequests.getByUser.queryOptions({
    status: 'ACCEPTED',
  }));

  if (isLoading) {
    return (
      <View className="flex-1 items-center justify-center">
        <ActivityIndicator />
      </View>
    );
  }

  if (isError) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-red-500">Error fetching matched requests</Text>
        <Button
          variant="secondary"
          label="Retry"
          onPress={() => {
            refetch();
          }}
        />
      </View>
    );
  }

  if (!matchedRequests) {
    return <Text className="text-red-500">No matched requests</Text>;
  }

  return (
    <FlashList
      showsVerticalScrollIndicator={false}
      data={matchedRequests.requests}
      keyExtractor={(item) => item.id.toString()}
      estimatedItemSize={300}
      refreshControl={<RefreshControl refreshing={isLoading} onRefresh={() => refetch()} />}
      ListEmptyComponent={EmptyList}
      renderItem={({ item }) => {
        {/* TODO: fix this as ts-ignore is not correct will fix in future */}
        {/* @ts-ignore */}
        return <TravelerDetailCard bookingTraveler={item.bookingTraveler} bookingCompanion={item.bookingCompanion} />
      }}
    />
  );
};

const MatchedRequest = () => {
  const userType = userTypeStore((state) => state.userType);
  return (
    <View className="flex-1 pb-25 px-5">
        {/* <InviteModal /> */}

        {userType === 'TRAVELLER' ? <MatchedRequestTraveler /> : <MatchedRequestCompanion />}
    </View>
  );
};

export default MatchedRequest;