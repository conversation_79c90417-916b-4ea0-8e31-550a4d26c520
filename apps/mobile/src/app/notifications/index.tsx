import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Image,
  Pressable,
  ScrollView,
  Text,
  View,
} from 'react-native';

import BackButton from '@/components/back-button';
import NotificationCard from '@/components/notification-card';

const Notifications = () => {
  const [loading, setLoading] = useState(false);
  const [hasNotification, setHasNotification] = useState(false);

  useEffect(() => {
    setLoading(true);

    const timer = setTimeout(() => {
      setLoading(false);
      setHasNotification(true);
    }, 5000);

    return () => clearTimeout(timer);
  }, []);

  return (
    <View className="flex-1">
      <View className="bg-[#20202080] px-5 py-3">
        <BackButton
          text="Back"
          onPress={() => {
            router.back();
          }}
        />
      </View>

      {loading ? (
        <ScrollView className="flex-1">
          <View className="flex-1 items-center justify-center px-5">
            <ActivityIndicator size="small" color="#0000ff" className="mt-5" />
            <View className=" mt-4 gap-6 bg-primary-950">
              <Text className="font-PoppinsBold text-xl font-bold leading-[30px] text-black-100">
                Notification
              </Text>
              <View className="my-9 items-center">
                <Image
                  source={require('../../../assets/images/pana2.png')}
                  resizeMode="contain"
                  style={{ width: 209.676, height: 218 }}
                />
              </View>
              <View className="items-center gap-3">
                <Text className="font-inter text-[16px] font-semibold leading-6 dark:text-black-0">
                  No Notification Yet
                </Text>
                <Text className="font-inter text-[14px] font-medium leading-5 dark:text-black-300">
                  You haven't received any notifications yet. Keep checking back
                  as you match with companions or receive messages!
                </Text>
              </View>
              <View className="gap-5">
                <Image
                  source={require('../../../assets/images/banner1.png')}
                  resizeMode="contain"
                  style={{ width: '100%', height: 147 }}
                />
              </View>
            </View>
          </View>
        </ScrollView>
      ) : (
        <ScrollView className="">
          <View className="mt-1 bg-black-0 px-5 dark:bg-primary-950">
            {hasNotification ? (
              <View className="">
                <View className="my-4 flex-row items-center justify-between">
                  <Pressable>
                    <Text className="font-PoppinsBold text-xl font-bold leading-[30px] dark:text-black-100">
                      Notification
                    </Text>
                  </Pressable>
                  <Pressable onPress={() => {}}>
                    <Text className="font-inter text-sm font-medium leading-5 dark:text-black-100">
                      Mark all as read
                    </Text>
                  </Pressable>
                </View>
                <View className="mt-2">
                  <NotificationCard text={'New Match !'} />
                  <NotificationCard text={'We’ve Got You!'} />
                  <Text className="font-inter text-[14px] font-semibold leading-5 text-black-0 dark:text-black-200">
                    Yesterday
                  </Text>
                  <NotificationCard text={'Profile Updated'} />
                  <NotificationCard text={'Yash Messaged You'} />
                  <NotificationCard text={'New Message'} />
                  <NotificationCard text={'New Message'} />
                  <Text className="font-inter text-[14px] font-semibold leading-5 text-black-0 dark:text-black-200">
                    This week
                  </Text>
                  <NotificationCard text={'New Message'} />
                  <NotificationCard text={'New Message'} />
                  <NotificationCard text="New Message" />
                  <NotificationCard text={'New Message'} />
                  <NotificationCard text={'New Message'} />
                  <NotificationCard text="New Message" />
                </View>
                <View className="mb-8" />
              </View>
            ) : null}
          </View>
        </ScrollView>
      )}
    </View>
  );
};

export default Notifications;
