import { Image } from 'expo-image';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
  Pressable,
  StatusBar,
  Text,
  View,
} from 'react-native';
import { account, databases } from '@/lib/appwrite';
import { UserType, userTypeStore } from '@/store/user-type-store';
import TravellerHome from '@/components/home/<USER>';
import CompanionHome from '@/components/home/<USER>';

// type ITravelerFlightDates = {
//   FlightCompany: string;
//   FlightPNR: string;
//   FlightDate: string;
//   FlightTime: string;
// };

// const zodValidation = z.object({
//   FlightCompany: z
//     .string({ required_error: 'FlightCompany required' })
//     .min(1, 'Required')
//     .max(10, 'FlightCompany is too long'),
//   FlightPNR: z.string({ required_error: 'Required' }).min(1, 'Required'),
//   FlightDate: z.string({
//     required_error: 'FlightDate required',
//     invalid_type_error: 'Invalid date format',
//   }),
//   FlightTime: z
//     .string({ required_error: 'FlightTime required' })
//     .min(1, 'Required'),
// });

export default function HomePage() {
  // const { width } = useWindowDimensions();
  // const [date, setDate] = useState(new Date());
  // const [time, setTime] = useState(new Date());
  // const [showDatePicker, setShowDatePicker] = useState(false);
  // const [showTimePicker, setShowTimePicker] = useState(false);
  // const [swapped, setSwapped] = useState(false);
  const [name, setName] = useState<string | null>();
  const userType = userTypeStore((state) => state.userType);

  useEffect(() => {
    (async () => {
      const user = await account.get();
      setName(user.name);
    })();
  }, []);

  return (
    <View className="w-full flex-1">
      <StatusBar barStyle={'dark-content'} />
      {/* <FocusAwareStatusBar /> */}

      <View className="w-full bg-black-0 px-5 py-2.5">
        <View className="pt-safe flex-row items-center justify-between px-2.5 py-2">
          {name && (
            <View className=" flex-row items-center gap-2">
              <Text className="font-PoppinsRegular text-[16px] font-normal leading-6 dark:text-blue">
                Welcome,
              </Text>
              <Text className="font-PoppinsSemiBold text-[16px] font-semibold leading-6 dark:text-blue">
                {name}
              </Text>
              <Image
                // source={require('../../../assets/images/handgif.png')}
                contentFit="contain"
                source={require('../../../assets/images/hand.gif')}
                // contentFit="contain"
                style={{ height: 30, width: 30 }}
              />
            </View>
          )}
          <Pressable
            onPress={() => {
              router.push('/notifications');
            }}
            className="rounded-[6px] bg-green p-[6.86px]"
          >
            <Image
              source={require('../../../assets/images/bell.svg')}
              // contentFit="contain"
              style={{ width: 18.286, height: 18.286 }}
              tintColor={'#F2F2F2'}
            />
          </Pressable>
        </View>
      </View>
      {userType === UserType.TRAVELLER && <TravellerHome />}
      {userType === UserType.COMPANION && <CompanionHome />}
    </View>
  );
}
