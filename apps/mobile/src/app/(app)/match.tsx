import Entypo from '@expo/vector-icons/Entypo';
import React from 'react';
import { useState } from 'react';
import { Image, Modal, Pressable, SafeAreaView, StatusBar, Text, View } from 'react-native';

import InputText from '@/components/input-txt';

import Index from '../match-screens';

export default function MatchScreen() {
  // const [openModalOption, setOpenModalOption] = useState(false);

  // const renderModalOption = () => {
  //   return (
  //     <Modal visible={openModalOption} animationType="slide" transparent={true}>
  //       <View className="pt-safe flex-1 items-center justify-end  dark:bg-bgmodal">
  //         <View className="w-full rounded-t-lg bg-white px-5 dark:bg-primary-950">
  //           <View className="mb-4 py-10">
  //             <Pressable onPress={() => setOpenModalOption(false)}>
  //               <Image
  //                 source={require('../../../assets/images/cross.png')}
  //                 resizeMode="contain"
  //                 style={{ height: 24, width: 24 }}
  //               />
  //             </Pressable>
  //             <View className="my-6">
  //               <Pressable className="flex-row items-center justify-between py-3">
  //                 <View className=" flex-row items-center">
  //                   <Image
  //                     source={require('../../../assets/images/helpcenter.png')}
  //                     className="mr-5 size-6"
  //                   />
  //                   <Text className="font-PoppinsMedium text-base font-medium text-black-100">
  //                     Help Center
  //                   </Text>
  //                 </View>
  //                 <Entypo name="chevron-right" size={20} color="#FDFDFD" />
  //               </Pressable>
  //             </View>
  //           </View>
  //         </View>
  //       </View>
  //     </Modal>
  //   );
  // };

  return (
    <SafeAreaView className="flex-1 bg-black-0 dark:bg-primary-950">
      <StatusBar barStyle={'light-content'} />
      {/* {renderModalOption()} */}
      {/* <View className="pt-safe flex-row items-center justify-between px-5">
        <InputText
          placeholder="Search"
          placeholderTextColor={'#B3B3B3'}
          className="flex-1 dark:bg-primary-900 "
          iconSourceFirst={require('../../../assets/images/search-icon.png')}
          iconStyleFirst={{ height: 14, width: 14 }}
        />

        <Pressable onPress={() => setOpenModalOption(true)}>
          <Image
            source={require('../../../assets/images/dot.png')}
            resizeMode="contain"
            className="ml-3 mt-2 size-6"
          />
        </Pressable>
      </View> */}

      <Index />
    </SafeAreaView>
  );
}
