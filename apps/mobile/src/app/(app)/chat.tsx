import Entypo from '@expo/vector-icons/Entypo';
import { FlashList } from '@shopify/flash-list';
import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Image, Modal, Pressable, StatusBar, Text, View, TouchableOpacity, ActivityIndicator, RefreshControl } from 'react-native';
import { useQuery } from '@tanstack/react-query';
import { trpc } from '@/lib/api';
import { useGetUserChannels } from '@/hooks/use-stream-chat';
import InputText from '@/components/input-txt';

interface ChatItem {
  id: string;
  connectionRequestId: string;
  otherUserName?: string;
  otherUserImage?: string | null;
  // lastMessage?: string;
  // lastMessageTime?: string;
  // unreadCount?: number;
  flightRoute?: string;
}

export default function Chat() {
  const [openModal, setOpenModal] = useState(false);

  const renderModal = () => {
    return (
      <Modal visible={openModal} animationType="slide" transparent={true}>
        <View className="pt-safe flex-1 items-center justify-end  dark:bg-bgmodal">
          <View className="w-full rounded-t-lg bg-white px-5 dark:bg-primary-950">
            <View className="mb-4 py-10">
              <Pressable onPress={() => setOpenModal(false)}>
                <Image
                  source={require('../../../assets/images/cross.png')}
                  resizeMode="contain"
                  style={{ height: 24, width: 24 }}
                />
              </Pressable>
              <View className="my-6">
                <Pressable className="mb-5 flex-row items-center py-3">
                  <Image
                    source={require('../../../assets/images/select.png')}
                    className="mr-5 size-6"
                  />
                  <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                    Select All
                  </Text>
                </Pressable>
                <Pressable className="flex-row items-center justify-between py-3">
                  <View className=" flex-row items-center">
                    <Image
                      source={require('../../../assets/images/helpcenter.png')}
                      className="mr-5 size-6"
                    />
                    <Text className="font-PoppinsMedium text-base font-medium text-black-100">
                      Help Center
                    </Text>
                  </View>
                  <Entypo name="chevron-right" size={20} color="#FDFDFD" />
                </Pressable>
              </View>
            </View>
          </View>
        </View>
      </Modal>
    );
  };

  const [refreshing, setRefreshing] = useState(false);

  // Get accepted connection requests
  const { data: acceptedConnectionsData, isLoading: isLoadingConnections, refetch: refetchConnections } = useQuery(
    trpc.connectionRequests.getByUser.queryOptions({
      status: 'ACCEPTED',
    })
  );

  // Get user channels for chat data
  const { data: userChannels, isLoading: isLoadingChannels, refetch: refetchChannels } = useGetUserChannels();

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      refetchConnections(),
      refetchChannels(),
    ]);
    setRefreshing(false);
  };

  const acceptedConnections = acceptedConnectionsData?.requests;
  const userId = acceptedConnectionsData?.userId;

  return (
    <View className="flex-1 justify-between bg-black-0 px-5 dark:bg-primary-950">
      <StatusBar barStyle={'light-content'} />
      {renderModal()}
      <View className="pt-safe flex-row items-center justify-between">
        <InputText
          placeholder="Search"
          placeholderTextColor={'#B3B3B3'}
          className="flex-1 dark:bg-primary-900 "
          // iconSourceFirst={require("../../../../assets/images/search-icon.png")}
          iconSourceFirst={require('../../../assets/images/search-icon.png')}
          iconStyleFirst={{ height: 14, width: 14 }}
        />
        <Pressable onPress={() => setOpenModal(true)}>
          <Image
            source={require('../../../assets/images/dot.png')}
            resizeMode="contain"
            className="ml-3 mt-2 size-6"
          />
        </Pressable>
      </View>

      {(isLoadingConnections || isLoadingChannels) ? (
        <View className="flex-1 items-center justify-center">
          <ActivityIndicator size="large" color="#3B82F6" />
          <Text className="text-gray-500 mt-2">Loading conversations...</Text>
        </View>
      ) : (() => {
        // Transform data for chat list
        const chatItems: ChatItem[] = acceptedConnections?.map((connection) => {
          const channel = userChannels?.find(
            (ch) => ch.id === `connection-${connection.id}`
          );

          // Determine other user info based on current user's role
          const isTraveler = userId === connection?.bookingCompanion?.appwriteUserId;

          // Create flight route string
          const destinations = isTraveler 
            ? connection.bookingTraveler?.destinations 
            : connection.bookingCompanion?.destinations;
          
          const flightRoute = destinations?.map(d => d.airport?.shortCode).join(' → ') || '';

          return {
            id: connection.id,
            connectionRequestId: connection.id,
            otherUserName: isTraveler ? connection.bookingTraveler?.name : connection.bookingCompanion?.name,
            otherUserImage: isTraveler ? connection.bookingTraveler?.companionPhoto : connection.bookingCompanion?.travelersPhoto,
            // lastMessage: channel ? 'Last message placeholder' : undefined,
            // lastMessageTime: channel ? '2:30 PM' : undefined,
            // unreadCount: channel ? 0 : undefined,
            flightRoute,
          };
        }) || [];

        return chatItems.length === 0 ? (
          // Empty state
          <View className="justify-between">
            {/* Empty State Image */}
            <View className="my-[67px] items-center">
              <Image
                source={require('../../../assets/images/pana.png')}
                resizeMode="contain"
                style={{ width: 279, height: 170 }}
              />
            </View>

            {/* Empty State Text */}
            <View className="items-center gap-3">
              <Text className="font-inter text-[16px] font-bold leading-6 dark:text-black-0">
                No chat available
              </Text>
              <Text className="text-center font-inter text-[16px] font-normal leading-6 dark:text-black-300">
                Find a match and start a conversation with your travel companion
                using the chat below
              </Text>
            </View>

            {/* Start Chat Button */}
            <View className="mt-16 w-full flex-row items-center justify-center">
              <Pressable
                className="rounded-[12px] border border-black-200 bg-black-950 px-6 py-3.5 dark:bg-primary-50"
                onPress={() => router.push('/welcome/')}
              >
                <Text className="font-inter text-[16px] font-normal leading-6 text-primary-950 dark:text-primary-950">
                  Start Chat
                </Text>
              </Pressable>
            </View>

            {/* Advertisement Image */}
            <View className="flex-end mt-40 h-full">
              <Image
                source={require('../../../assets/images/ad.png')}
                className="h-24 w-full rounded-xl"
                resizeMode="cover"
              />
            </View>
          </View>
        ) : (
          // Chat list
          <View className="flex-1">
            <FlashList
              showsVerticalScrollIndicator={false}
              data={chatItems}
              keyExtractor={(item) => item.id}
              estimatedItemSize={200}
              refreshControl={
                <RefreshControl
                  refreshing={refreshing}
                  onRefresh={onRefresh}
                  colors={['#3B82F6']}
                />
              }
              renderItem={({ item }) => (
                <View className="flex-1">
                  <Pressable
                    onPress={() => router.push(`/chat-screens/${item.connectionRequestId}`)}
                    className="mb-2 flex-1 rounded-md bg-[#20202033] p-3"
                  >
                    <View className="flex-row items-center justify-between">
                      <View className="flex-row items-center">
                        {/* profile image */}
                        <View className="mr-3 size-14 rounded-full bg-gray-300 overflow-hidden">
                          {item.otherUserImage ? (
                            <Image
                              source={{ uri: item.otherUserImage }}
                              className="w-full h-full"
                              resizeMode="cover"
                            />
                          ) : (
                            <View className="w-full h-full bg-primary-500 items-center justify-center">
                              <Text className="text-white font-semibold text-lg">
                                {item.otherUserName?.charAt(0)?.toUpperCase()}
                              </Text>
                            </View>
                          )}
                        </View>
                        {/* center text part */}
                        <View>
                          <Text className="font-inter text-base font-medium text-black-50">
                            {item.otherUserName}
                          </Text>
                          {item.flightRoute && (
                            <View className="flex-row items-center">
                              <Text className="font-inter text-xs font-medium text-black-300">
                                {item.flightRoute}
                              </Text>
                            </View>
                          )}
                          {/* <Text className="mt-1 font-inter text-xs font-normal text-black-50">
                            {item.lastMessage || 'Start a conversation...'}
                          </Text> */}
                        </View>
                      </View>

                      {/* right icons and notification part */}
                      {/* <View>
                        <Text className="mb-3 font-inter text-xs font-medium text-black-500">
                          {item.lastMessageTime || ''}
                        </Text>

                        {item.unreadCount && item.unreadCount > 0 && (
                          <View className="self-end rounded-2xl bg-secondary-400 px-1">
                            <Text className="p-1 font-inter text-[10px] font-medium text-blue">
                              {item.unreadCount > 99 ? '99+' : item.unreadCount}
                            </Text>
                          </View>
                        )}
                      </View> */}
                    </View>
                  </Pressable>
                </View>
              )}
            />
            <View className="mb-20" />
          </View>
        );
      })()}
    </View>
  );
}
