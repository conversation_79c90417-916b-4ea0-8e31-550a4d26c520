import { Image } from 'expo-image';
import { Tabs } from 'expo-router';
import React from 'react';
import { useEffect } from 'react';
import {
  type GestureResponderEvent,
  Text,
  TouchableOpacity,
} from 'react-native';
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

import { cn } from '@/lib/utils';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface TabItem {
  route: string;
  label: string;
  activeImage: any;
  inActiveImage: any;
}

const TabArr: TabItem[] = [
  {
    route: 'home',
    label: 'Home',
    activeImage: require('../../../assets/images/home-active.svg'),
    inActiveImage: require('../../../assets/images/home.svg'),
  },
  {
    route: 'match',
    label: 'Match',
    activeImage: require('../../../assets/images/match-active.png'),
    inActiveImage: require('../../../assets/images/match.png'),
  },
  {
    route: 'chat',
    label: 'Chat',
    activeImage: require('../../../assets/images/chat-active.svg'),
    inActiveImage: require('../../../assets/images/text-active.png'),
  },

  {
    route: 'offers',
    label: 'Offers',
    activeImage: require('../../../assets/images/offers-in-active.svg'),
    inActiveImage: require('../../../assets/images/offersun-active.png'),
  },
  {
    route: 'profile',
    label: 'Profile',
    activeImage: require('../../../assets/images/profile-active.svg'),
    inActiveImage: require('../../../assets/images/user-active.svg'),
  },
];

const TabButton: React.FC<{
  item: TabItem;
  onPress: (event: GestureResponderEvent) => void;
  accessibilityState?: { selected?: boolean }; // Make `selected` optional
}> = ({ item, onPress, accessibilityState }) => {
  // Default `selected` to false if it's undefined
  const focused = accessibilityState?.selected ?? false;
  const insets = useSafeAreaInsets();

  // Animation setup
  const scale = useSharedValue(focused ? 1 : 0);

  useEffect(() => {
    scale.value = withSpring(focused ? 1 : 0, {
      damping: 15,
      stiffness: 100,
    });
  }, [focused]);

  const animatedIconStyle = useAnimatedStyle(() => {
    const scaleValue = interpolate(scale.value, [0, 1], [1, 1.2]);
    return {
      transform: [{ scale: scaleValue }],
    };
  });

  return (
    <TouchableOpacity
      onPress={onPress}
      style={{
        flex: 1,
      }}
    >
      <Animated.View
        style={[
          {
            flex: 1,
            justifyContent: 'center',
            alignItems: 'center',
            paddingVertical: 4,
          },
          animatedIconStyle,
        ]}
      >
        <Image
          contentFit="contain"
          source={focused ? item.activeImage : item.inActiveImage}
          style={{
            width: 24,
            height: 24,
            marginBottom: 2,
          }}
        />
        <Text
          className={cn(
            'font-PoppinsMedium text-xs font-medium leading-4',
            focused ? 'text-black-50' : 'text-black-600'
          )}
        >
          {item.label}
        </Text>
      </Animated.View>
    </TouchableOpacity>
  );
};

const HomeLayout = () => {
  const insets = useSafeAreaInsets();
  return (
    <Tabs
      // initialRouteName="index"
      screenOptions={{
        headerShown: false,
        tabBarStyle: {
          position: 'absolute',
          height: insets.bottom + 74,
          paddingHorizontal: 8,
          paddingTop: 15,
          bottom: 2,
          borderRadius: 12,
          backgroundColor: 'rgba(32, 32, 32, 0.99)',
          paddingBottom: insets.bottom,
        },
      }}
    >
    {TabArr.map((item, index) => {
      return (
        <Tabs.Screen
          key={index}
          name={item.route}
          options={{
            tabBarButton: (props) => (
              <TabButton
                {...props}
                item={item}
                onPress={
                  props.onPress as (event: GestureResponderEvent) => void
                } // Ensure onPress is typed correctly
              />
            ),
          }}
        />
      );
    })}
    </Tabs>
  );
};

export default HomeLayout;
