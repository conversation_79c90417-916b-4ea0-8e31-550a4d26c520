// Import global CSS file
import '../../global.css';

import { BottomSheetModalProvider } from '@gorhom/bottom-sheet';
import { ThemeProvider } from '@react-navigation/native';
import { Slot, Stack, useRouter, useSegments } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { StyleSheet } from 'react-native';
import FlashMessage from 'react-native-flash-message';
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { KeyboardProvider } from 'react-native-keyboard-controller';
import { QueryClientProvider } from '@tanstack/react-query';

import { queryClient } from '@/lib/api';
import { ConnectivityModal } from '@/components/connectivity-modal';
import { loadSelectedTheme, useAuth } from '@/lib';
import { useThemeConfig } from '@/lib/use-theme-config';
import { useAirportStore } from '@/store/airport-store';
import { StreamChatProvider } from '@/components/stream-chat-provider';
import { AuthProvider, useAuthContext } from '@/lib/auth/auth-context';

export { ErrorBoundary } from 'expo-router';

export const unstable_settings = {
  initialRouteName: 'index',
};

loadSelectedTheme();

// This is the main layout of the app
// It wraps your pages with the providers they need
export default function RootLayout() {
  return (
    <AuthProvider>
      <Providers>
        <RootLayoutNav />
      </Providers>
    </AuthProvider>
  );
}

function RootLayoutNav() {
  const { user, isLoading } = useAuthContext();

  // Show loading screen while checking authentication
  if (isLoading) {
    return (
      <Stack screenOptions={{ headerShown: false }}>
        <Stack.Screen name="loading" />
      </Stack>
    );
  }

  return (
    <Stack screenOptions={{ headerShown: false }}>
      <Stack.Protected guard={!user}>
        {/* Onboarding - first time users */}
        <Stack.Screen name="index" />
        {/* Auth group - unauthenticated users */}
        <Stack.Screen name="(auth)" />
        {/* Welcome - after onboarding */}
        <Stack.Screen name="welcome" />
      </Stack.Protected>
      
      <Stack.Protected guard={!!user}>
        {/* App group - authenticated users */}
        <Stack.Screen name="(app)" />
        
        {/* Other screens that can be accessed from anywhere */}
        <Stack.Screen name="form/companion-required" />
        <Stack.Screen name="form/ready-as-companion" />
        <Stack.Screen name="wallet" />
        <Stack.Screen name="payment" />
        <Stack.Screen name="bookings" />
        <Stack.Screen name="booking-details" />
        <Stack.Screen name="personal-info" />
        <Stack.Screen name="notifications" />
        <Stack.Screen name="feedback" />
        <Stack.Screen name="refer-to" />
        <Stack.Screen name="chat-screens" />
        <Stack.Screen name="match-screens" />
        <Stack.Screen name="policy" />
        <Stack.Screen name="transactions" />
        <Stack.Screen name="settings" />
        <Stack.Screen name="switch-account" />
        <Stack.Screen name="leader-board" />
        <Stack.Screen name="companion" />
      </Stack.Protected>
      <Stack.Screen name="loading" />

    </Stack>
  );
}

function Providers({ children }: { children: React.ReactNode }) {
  const theme = useThemeConfig();
  const auth = useAuth();
  const { fetchAirports } = useAirportStore();

  // Fetch airports when the app loads and user is authenticated
  useEffect(() => {
    if (auth.status === 'signIn') {
      fetchAirports();
    }
  }, [fetchAirports, auth.status]);

  return (
    <QueryClientProvider client={queryClient}>
      <GestureHandlerRootView
        style={styles.container}
        className={theme.dark ? `dark` : undefined}
      >
        <StreamChatProvider>
          <KeyboardProvider>
            <ThemeProvider value={theme}>
              <BottomSheetModalProvider>
                {children}
                <FlashMessage position="top" />
                <ConnectivityModal />
              </BottomSheetModalProvider>
            </ThemeProvider>
          </KeyboardProvider>
        </StreamChatProvider>
      </GestureHandlerRootView>
    </QueryClientProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
});
