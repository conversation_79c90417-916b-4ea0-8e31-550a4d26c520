import { AntDesign } from '@expo/vector-icons';
import { router } from 'expo-router';
import React from 'react';
import { Dimensions, Image, Pressable, Text, View } from 'react-native';
const { height, width } = Dimensions.get('screen');

const WelcomePage = () => {
  return (
    <>
      <View className="pt-safe relative w-full flex-1 bg-black-0 px-5  dark:bg-primary-950 ">
        {/*<Pressable*/}
        {/*  onPress={() => {*/}
        {/*    router.push('/home');*/}
        {/*  }}*/}
        {/*  className="z-10 mt-10 self-end rounded-xl px-5 py-3.5"*/}
        {/*>*/}
        {/*  <Text className="font-PoppinsRegular text-[16px] font-normal leading-6 dark:text-black-100 ">*/}
        {/*    Skip*/}
        {/*  </Text>*/}
        {/*</Pressable>*/}

        <View className="bottom-24 flex-1 items-center justify-center gap-9">
          <Image
            source={require('../../../assets/images/body.png')}
            resizeMode="contain"
            style={{ height: 85, width: 218 }}
          />

          {/* Card 1 */}
          <Pressable
            onPress={() => {
              router.push(
                '/form/companion-required/traveler-details?onboarding=true'
              );
            }}
            className="w-full items-center rounded-xl bg-black-0 px-6 py-3.5 dark:bg-primary-50 "
          >
            <View className="flex-row items-center gap-2">
              <Image
                source={require('../../../assets/images/search.png')}
                resizeMode="contain"
                className="size-24"
              />
              <Text className="mx-2 font-inter text-base font-medium leading-6 dark:text-blue">
                I Want a Companion
              </Text>
              <AntDesign name="right" size={20} color={'#071952'} />
            </View>
          </Pressable>

          {/* Card 2 */}
          <Pressable
            onPress={() => {
              router.push(
                '/form/ready-as-companion/companion-details?onboarding=true'
              );
              console.log('go to next screen');
            }}
            className="mt-2 w-full items-center rounded-xl bg-black-0  px-6 py-3.5 dark:bg-blue"
          >
            <View className="flex-row items-center gap-2">
              <Image
                source={require('../../../assets/images/companion.png')}
                resizeMode="contain"
                className="size-24"
              />
              <Text className="mx-2 text-center font-inter text-base font-medium leading-6 text-blue dark:text-black-0">
                Ready as a Companion
              </Text>
              <AntDesign name="right" size={20} color={'#FDFDFD'} />
            </View>
          </Pressable>
        </View>
      </View>
      <View
        className="absolute bottom-0 w-full items-center"
        pointerEvents="none"
      >
        <Image
          source={require('../../../assets/images/wave.png')}
          resizeMode="cover"
          style={{ width: width * 1, height: height * 0.342 }}
        />
      </View>
    </>
  );
};

export default WelcomePage;
