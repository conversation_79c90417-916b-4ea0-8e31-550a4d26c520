import { AntDesign, Ionicons } from '@expo/vector-icons';
import { useNavigation, useRouter } from 'expo-router';
import React, { useEffect, useRef, useState } from 'react';
// import { useIsFirstTime } from '@/lib/hooks';
import { Dimensions, ImageBackground, Pressable } from 'react-native';
import {
  Directions,
  Gesture,
  GestureDetector,
} from 'react-native-gesture-handler';
import Animated, {
  LightSpeedInRight,
  LightSpeedOutRight,
  runOnJS,
} from 'react-native-reanimated';

// import { Cover } from '@/components/cover';
import {
  Button,
  FocusAwareStatusBar,
  // SafeAreaView,
  Text,
  View,
} from '@/components/ui';
import { useSelectedTheme } from '@/lib/hooks';

const { height, width } = Dimensions.get('screen');

const slides = [
  {
    id: 1,
    title: 'Explore the World with Confidence ',
    Heading1: 'Find a travel buddy :',
    text1: 'Meet companions who share your trip.',
    Heading2: 'Safe & Secure :',
    text2: 'Connect with verified companions, privacy protected. ',
    backgroundImage: require('../../assets/images/onboarding-screen1.png'),
  },
  {
    id: 2,
    title: 'Simple & Hassle-Free Matching',
    Heading1: 'Quick Setup:',
    text1:
      'Briefly enter flight info( personal data stays private) and travel preferences.',
    Heading2: 'Perfect Match :',
    text2: 'Get paired with ideal companions.',
    backgroundImage: require('../../assets/images/onboarding-screen2.png'),
  },
  {
    id: 3,
    title: 'Connect & Explore Together',
    Heading1: 'Plan & Chat:',
    text1: 'Chat with your companion to plan your trip',
    Heading2: 'Get Compensated for your Companionship:',
    text2: 'Compensate with your companion based on your preferences.',
    backgroundImage: require('../../assets/images/onboarding-screen3.png'),
  },
];

export default function Onboarding() {
  const router = useRouter();
  const navigation = useNavigation();
  const [screenIndex, setScreenIndex] = useState(0);
  const nextStepRef = useRef(null);
  const [theme, setTheme] = useState('dark');
  const data = slides[screenIndex];
  const isLastScreen = screenIndex === slides.length - 1;
  const isFirstScreen = screenIndex === 0;
  // const [_, setIsFirstTime] = useIsFirstTime();
  // const router = useRouter();

  useEffect(() => {
    setTheme('dark');
  }, []);

  useEffect(() => {
    navigation.setOptions({ headerShown: false });
  }, [navigation]);

  const next = () => {
    if (!isLastScreen) {
      runOnJS(setScreenIndex)(screenIndex + 1);
    }
  };

  const back = () => {
    if (!isFirstScreen) {
      runOnJS(setScreenIndex)(screenIndex - 1);
    }
  };

  const swipes = Gesture.Simultaneous(
    Gesture.Fling()
      .direction(Directions.LEFT)
      .onEnd(next)
      .shouldCancelWhenOutside(false)
      .enabled(nextStepRef.current !== null),
    Gesture.Fling()
      .direction(Directions.RIGHT)
      .onEnd(back)
      .shouldCancelWhenOutside(false)
  );

  const { selectedTheme: _selectedTheme, setSelectedTheme } =
    useSelectedTheme();

  useEffect(() => {
    setSelectedTheme('dark');
  }, []);

  return (
    <GestureDetector gesture={swipes}>
      <Animated.View
        style={[{ flex: 1, height: height }]}
        entering={LightSpeedInRight}
        exiting={LightSpeedOutRight}
      >
        <FocusAwareStatusBar />

        <View className="relative flex-1 bg-white">
          <ImageBackground
            source={data.backgroundImage}
            style={[{ height: height, width: width }]}
            resizeMode="cover"
          />
          {screenIndex !== 0 && (
            <Pressable
              onPress={back}
              className="pt-safe absolute flex-row items-center px-5  "
            >
              <Ionicons name="chevron-back" size={20} color="white" />

              <Text className="font-inter text-[14px] font-medium leading-5 text-black-0 ">
                Back
              </Text>
            </Pressable>
          )}

          <View className="absolute bottom-0 h-[382px] w-full justify-between rounded-tl-[50px]  bg-black-0 px-5 py-6 dark:bg-primary-950">
            <View className="w-full gap-[28px]">
              <View className="flex-row items-center self-center ">
                {slides.map((item, index) => (
                  <View
                    key={item.id}
                    style={{
                      height: 8,
                      width: index === screenIndex ? 30 : 10,
                      borderRadius: index === screenIndex ? 8 : 6,
                      backgroundColor:
                        index === screenIndex ? 'white' : '#1B1F2E',
                      marginLeft: 8,
                    }}
                  />
                ))}
              </View>

              <View className="">
                <Text className=" font-PoppinsBold text-[20px] font-bold leading-[30px]    text-primary-750 dark:text-black-300  ">
                  {data.title}
                </Text>
              </View>

              <View className="w-full gap-2 ">
                <View className="w-full gap-[2px]">
                  <Text className="font-inter text-[16px] font-semibold leading-[24px] text-black-900 dark:text-black-0">
                    {data.Heading1}
                  </Text>
                  <Text className="font-inter text-[16px] font-light leading-6 text-black-300 dark:text-black-300 ">
                    {data.text1}
                  </Text>
                </View>

                <View className="gap-[2px]">
                  <Text className="font-inter text-[16px] font-semibold leading-[24px] text-black-900 dark:text-black-0">
                    {data.Heading2}
                  </Text>
                  <Text className="font-inter text-[16px] font-normal leading-6 text-black-0 dark:text-black-300">
                    {data.text2}
                  </Text>
                </View>
              </View>
            </View>

            <View className="bottom-[28px]">
              {screenIndex === 2 ? (
                <View>
                  <Button
                    onPress={() => {
                      router.push('/login');
                    }}
                    label="Get Started"
                    variant="secondary"
                    textClassName="text-black-0  font-medium font-inter leading-[24px] text-[16px] dark:text-blue"
                    className=" bg-black-0 dark:bg-primary-50"
                    // rightIcon={
                    //   <AntDesign name="right" size={16} color={'#071952'} />
                    // }
                  />
                </View>
              ) : (
                <View className="mt-[30px] flex-row items-center justify-between">
                  <Button
                    onPress={() => {
                      console.log('skip');
                      router.push('/login');
                    }}
                    label="Skip"
                    variant="secondary"
                    fullWidth={false}
                    textClassName="text-black-0  font-medium font-inter leading-[24px] text-[16px] dark:text-blue"
                    className=" bg-black-0 dark:bg-primary-50 w-40"
                  />

                  <Pressable ref={nextStepRef} onPress={() => next()}>
                    <AntDesign name="arrowright" size={30} color={'white'} />
                  </Pressable>
                </View>
              )}
            </View>
          </View>
        </View>
      </Animated.View>
    </GestureDetector>
  );
}
