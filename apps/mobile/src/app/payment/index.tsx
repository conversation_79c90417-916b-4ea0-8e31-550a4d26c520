import { Image } from 'expo-image';
import { router } from 'expo-router';
import React from 'react';
import { useState } from 'react';
import {
  Modal,
  Pressable,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  View,
} from 'react-native';

import BackButton from '@/components/back-button';
import BookingsCard from '@/components/bookings-card';
import { Button, Radio } from '@/components/ui';

const PaymentScreen = () => {
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false); // Modal visibility state
  const handleRadioChange = (option: string) => {
    setSelectedOption(option);
  };

  // Function to open the modal
  const openModal = () => {
    setIsModalVisible(true);
  };

  // Function to close the modal
  const closeModal = () => {
    setIsModalVisible(false);
  };

  return (
    <View className="flex-1 gap-8 bg-primary-950 px-5  pt-2.5">
      <BackButton
        text="Back"
        text2="Payment"
        onPress={() => {
          router.back();
        }}
      />
      <ScrollView showsVerticalScrollIndicator={false} className="h-full">
        {/* <BookingsCard
          date="23 April,2024"
          boarding="FGR"
          alighting="IND"
          boardingTiming="10:05 PM"
          travelTiming="2:05 AM"
          alightingTiming="20:2 PM"
          TravelerName="Kaushiki ojha"
          TravelerDetails="Male, 28yrs, Germany"
          text="Unpaid"
          className=""
          classNameText=""
          planeImage={require('../../../assets/images/plane.png')}
          profileImage={undefined}
        /> */}

        <View className="gap-4 border-b border-black-0 pb-6 ">
          <Text className="font-inter text-[16px] font-semibold leading-6 dark:text-black-50">
            Payments Details
          </Text>
          <View className="flex-row items-center justify-between">
            <View className="gap-2">
              <Text className="font-inter text-[14px] font-normal leading-5 dark:text-black-300 ">
                Subtotal
              </Text>
              <Text className="font-inter text-[14px] font-normal leading-6 dark:text-black-300 ">
                Taxes
              </Text>
              <Text className="font-inter text-[14px] font-semibold leading-5 dark:text-black-100 ">
                Total
              </Text>
            </View>
            <View className="gap-2">
              <Text className="font-inter text-[14px] font-normal leading-5 dark:text-black-300 ">
                $ 4500
              </Text>
              <Text className="font-inter text-[14px] font-normal leading-5 dark:text-black-300 ">
                $ 400.40
              </Text>
              <Text className="font-inter text-[14px] font-semibold leading-5 dark:text-black-100 ">
                $ 4900.40
              </Text>
            </View>
          </View>
        </View>

        <View className=" gap-3">
          <View className="flex-row items-center justify-between gap-2">
            <Text className="font-inter text-[16px] font-semibold leading-6 dark:text-black-50">
              Payments Details
            </Text>
            <Text className="font-inter text-[12px] font-normal leading-4 dark:text-black-300">
              Swipe left to edit cards details
            </Text>
          </View>

          <View className="flex-row items-start justify-between">
            <View className="flex-row gap-3">
              <Image
                source={require('../../../assets/images/back.svg')}
                contentFit="contain"
                style={{ width: 32, height: 22 }}
              />

              <View className="gap-1">
                <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-300">
                  ***8976
                </Text>

                <View className="flex-row items-center gap-2">
                  <Image
                    source={require('../../../assets/images/success.svg')}
                    contentFit="contain"
                    style={{ width: 14, height: 14 }}
                  />
                  <Text className="font-inter text-[12px] font-medium leading-4 dark:text-black-300">
                    Get 10 coins
                  </Text>
                </View>
              </View>
            </View>

            <Radio
              accessibilityLabel=""
              checked={selectedOption === 'option'}
              onChange={() => handleRadioChange('option')}
            />
          </View>

          <View className="border-t border-black-0  ">
            <View className="mt-4 flex-row justify-between">
              <View className="flex-row gap-3">
                <Image
                  source={require('../../../assets/images/back.svg')}
                  contentFit="contain"
                  style={{ width: 32, height: 22 }}
                />

                <View className="gap-1">
                  <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-300">
                    PayPal
                  </Text>

                  <View className="flex-row items-center gap-2">
                    <Image
                      source={require('../../../assets/images/success.svg')}
                      contentFit="contain"
                      style={{ width: 14, height: 14 }}
                    />
                    <Text className="font-inter text-[12px] font-medium leading-4 dark:text-black-300">
                      Get 10 coins
                    </Text>
                  </View>
                </View>
              </View>
              <Radio
                accessibilityLabel=""
                checked={selectedOption === 'option'}
                onChange={() => handleRadioChange('option')}
              />
            </View>
          </View>

          <View className="border-y border-black-0 pb-5 ">
            <View className="mt-4 flex-row justify-between">
              <View className="flex-row gap-3">
                <Image
                  source={require('../../../assets/images/back.svg')}
                  contentFit="contain"
                  style={{ width: 32, height: 22 }}
                />

                <View className="gap-1">
                  <View>
                    <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-300">
                      Credit Card
                    </Text>
                    <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-300">
                      Visa, Mastercard
                    </Text>
                  </View>

                  <View className="flex-row items-center gap-2">
                    <Image
                      source={require('../../../assets/images/success.svg')}
                      contentFit="contain"
                      style={{ width: 14, height: 14 }}
                    />
                    <Text className="font-inter text-[12px] font-medium leading-4 dark:text-black-300">
                      Get 10 coins
                    </Text>
                  </View>
                </View>
              </View>
              <Pressable onPress={openModal}>
                <Text className="font-inter text-[16px] font-normal leading-6 dark:text-green">
                  Add
                </Text>
              </Pressable>
            </View>
          </View>
        </View>
      </ScrollView>

      <View className="mb-5 flex-col justify-self-end">
        <View className="flex-row items-center justify-between">
          <View>
            <Pressable className="flex-row items-center ">
              <Text className="font-inter text-[12px] font-normal leading-4 text-black-0 dark:text-black-500">
                Subtotal
              </Text>
              <Image
                source={require('../../../assets/images/expandmore.png')}
                className="ml-0.5 size-5"
              />
            </Pressable>

            <Text className="font-inter text-[24px] font-medium leading-[32px] dark:text-black-200">
              $4900.40
            </Text>
          </View>

          <Pressable
            onPress={() => {
              router.push('/payment/succesful');
            }}
            className="w-3/5 items-center rounded-[8px] bg-primary-50  py-[14px] "
          >
            <Text className="font-inter text-[16px] font-normal leading-6 dark:text-blue">
              Pay Now
            </Text>
          </Pressable>
        </View>
      </View>

      {/* Add card Modal */}
      <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
        <Modal
          visible={isModalVisible}
          animationType="slide" // You can choose 'slide', 'fade', or 'none'
          transparent={true} // To make the background dimmed
          onRequestClose={closeModal} // Handles Android back button press
        >
          <View style={styles.modalOverlay}>
            <View className="h-[54%] w-full rounded-t-2xl bg-primary-950 px-5 py-8">
              <Pressable onPress={closeModal}>
                <Image
                  source={require('../../../assets/images/cancel.png')}
                  className="size-6"
                />
              </Pressable>

              <View className="mb-5">
                <Text className="my-5 font-inter text-xl font-semibold text-black-50">
                  Add a new Card
                </Text>
                <View>
                  <Text className="font-PoppinsMedium text-base font-medium text-black-50">
                    Card Number
                  </Text>
                  <TextInput
                    placeholder="xxxxxxxxxxxxxxx"
                    keyboardType="numeric"
                    className="rounded-xl px-5 py-3.5"
                  />
                </View>
              </View>

              <View className="mb-5 flex-row items-center justify-center">
                <View className="mr-5 flex-1">
                  <Text className="font-PoppinsMedium text-base font-medium text-black-50">
                    Expiry Date
                  </Text>
                  <TextInput
                    placeholder="MM/YY"
                    keyboardType="numeric"
                    className="rounded-xl px-5 py-3.5"
                  />
                </View>
                <View className="flex-1">
                  <Text className="font-PoppinsMedium text-base font-medium text-black-50">
                    CVC/CVV
                  </Text>
                  <TextInput
                    placeholder="***"
                    keyboardType="numeric"
                    className="rounded-xl px-5 py-3.5"
                  />
                </View>
              </View>

              <View className="mb-5">
                <View>
                  <Text className="font-PoppinsMedium text-base font-medium text-black-50">
                    Card Holder Name
                  </Text>
                  <TextInput
                    placeholder="enter your name"
                    className="rounded-xl px-5 py-3.5"
                  />
                </View>
              </View>

              <Button
                variant="darkblue"
                label="Add New Card"
                onPress={function (): void {
                  throw new Error('Function not implemented.');
                }}
              />
            </View>
          </View>
        </Modal>
      </ScrollView>
    </View>
  );
};

export default PaymentScreen;

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    justifyContent: 'flex-end', // Position the modal content at the bottom of the screen
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Background dimming effect
  },
});
