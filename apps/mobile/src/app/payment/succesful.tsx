import { Image, ImageBackground } from 'expo-image';
import { router } from 'expo-router';
import React, { useState } from 'react';
import { Alert, Pressable, Text, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

const SuccessfulPayment = () => {
  const [selectedStep, setSelectedStep] = useState(0);
  return (
    <ImageBackground
      source={require('../../../assets/images/globalmap.png')}
      style={{ flex: 1 }}
    >
      <SafeAreaView>
        <View className=" top-[130] mx-5 rounded-2xl bg-black-0 p-6">
          <Pressable
            onPress={() => {
              router.back();
            }}
          >
            <Image
              source={require('../../../assets/images/cross.png')}
              contentFit="contain"
              tintColor={'black'}
              className="size-6 self-end"
            />
          </Pressable>

          <View className="mt-6">
            <Image
              source={require('../../../assets/images/paymentplane.svg')}
              style={{ width: 91, height: 91 }}
              className="absolute bottom-0 self-center"
              contentFit="contain"
            />
          </View>

          <View className="gap-10">
            <View className="items-center">
              <Image
                source={require('../../../assets/images/green-tick.png')}
                style={{ height: 162, width: 162 }}
              />
            </View>

            <View className="gap-[30px]">
              <View className="items-center gap-[10px]">
                <Text className="font-inter text-[24px] font-bold leading-[34px] dark:text-black-900">
                  Money Added to Wallet
                </Text>
                <Text className="text-center font-inter text-[12px] font-medium leading-4 dark:text-black-600">
                  Your money has been successfully added to your wallet , use it
                  for future traveling
                </Text>
              </View>
              <View className="flex-row items-center justify-between">
                <Text className="font-inter text-[12px] font-normal leading-4 text-black-800">
                  Transaction Id:
                </Text>
                <Text className="font-inter text-[12px] font-normal leading-4 text-black-600">
                  43545dsr65469203858394834
                </Text>
              </View>
              <View className="border border-dashed border-black-200" />
              <View className="items-center gap-[10px] ">
                <Text className="font-inter text-[12px] font-normal leading-4 text-black-600">
                  Amount
                </Text>
                <Text className="font-inter text-[20px] font-normal leading-[30px] dark:text-blue">
                  $ 450.00
                </Text>
              </View>
              <Pressable
                className=" w-full items-center justify-center rounded-[12px] bg-primary-950 px-6 py-[14px]"
                onPress={() => Alert.alert('No Function Implemented yet')}
              >
                <Text className="font-inter text-[16px] font-medium leading-6 dark:text-black-50">
                  View Details
                </Text>
              </Pressable>
            </View>
          </View>
        </View>
      </SafeAreaView>
    </ImageBackground>
  );
};

export default SuccessfulPayment;
