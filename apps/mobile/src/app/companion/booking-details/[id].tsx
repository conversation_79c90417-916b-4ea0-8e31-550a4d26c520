import { router } from 'expo-router';
import React, { useState } from 'react';
import { ActivityIndicator, Alert, Text, View } from 'react-native';

import BackButton from '@/components/back-button';
import { Button, FocusAwareStatusBar } from '@/components/ui';
import { useLocalSearchParams } from 'expo-router';
import * as MailComposer from 'expo-mail-composer';
import { showMessage } from 'react-native-flash-message';
import { trpc } from '@/lib/api';
import { useQuery } from '@tanstack/react-query';
import CompanionBookingsCard from '@/components/companion-booking-card';
import { BookingTraveler } from '@/types';
import TravelerDetailCard from '@/components/traveler-detail-card';

function BookingDetailsTraveler({ bookingTraveler }: { bookingTraveler: BookingTraveler }) {
  const { data, isLoading, isRefetching, isError, refetch } = useQuery(trpc.bookingTravelers.travelersAvailableForBooking.queryOptions({ bookingTravelerId: bookingTraveler.id }));

  if (isLoading) {
    return <ActivityIndicator />;
  }

  if (isError || !data) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-red-500">Error fetching companions</Text>
        <Button
          variant="secondary"
          label="Retry"
          onPress={() => {
            refetch();
          }}
        />
      </View>
    );
  }

  return (
    <View className="flex-1 gap-2.5 px-5 dark:bg-[#20202080]">
      <View className="flex-row items-center justify-between">
        <Text className="leading-5 font-inter font-normal  text-black-50">Total Travelers: {data.total}</Text>
        <Button variant="secondary" label="Refresh" loading={isRefetching} fullWidth={false} onPress={refetch} />
      </View>
      {data.travelers.map((bookingCompanion) => (
        <TravelerDetailCard key={bookingCompanion.id} bookingCompanion={bookingCompanion} bookingTraveler={bookingTraveler} />
      ))}
    </View>
  );
}

export default function BookingDetails() {
  const { id } = useLocalSearchParams();
  const { data: bookingTraveler, isLoading, isError, refetch } = useQuery(trpc.bookingTravelers.getById.queryOptions({ id: id as string }));

  if (isLoading) {
    return <ActivityIndicator />;
  }

  if (isError) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-red-500">Error fetching booking details</Text>
        <Button
          variant="secondary"
          label="Retry"
          onPress={() => {
            refetch();
          }}
        />
      </View>
    );
  }

  if (!bookingTraveler) {
    return <Text>No booking details found</Text>;
  }

  return (
    <View className="flex-1 dark:bg-primary-950">
      <FocusAwareStatusBar />
      <View className="gap-2.5 px-5 py-3 dark:bg-[#20202080]">
        <BackButton
          text="Back"
          text2="Booking Details"
          text3="Help"
          onPress={() => {
            router.back();
          }}
          onPress3={() => {
            MailComposer.composeAsync({
              recipients: ['<EMAIL>'],
              subject: `Booking #${bookingTraveler.id}`,
              body: ``,
            }).catch((err) => {
              showMessage({
                message: 'Error in sending email',
                type: 'danger',
              });
              console.log('error in sending email', err);
            });
          }}
        />
        <CompanionBookingsCard bookingId={bookingTraveler.id} />
        {bookingTraveler.isEditable && <Button variant="secondary" label="Edit Booking" onPress={() => {
          router.push(`/form/ready-as-companion/companion-details?id=${bookingTraveler.id}`);
        }} />}
      </View>
      <BookingDetailsTraveler bookingTraveler={bookingTraveler} />
      {/* <View className="flex-1 gap-8 px-5 ">
        <View className="flex-row items-center justify-between bg-red-500 h-20">
          {booking && <BookingDetailsCompanion booking={booking} />}
        </View>
      </View> */}
      {/* <View className="flex-1 gap-8 px-5 ">
        <View className="mt-8 gap-6 rounded-xl border border-primary-900  p-5 dark:bg-primary-950">
          {/* Row First *\/}
          <View className="gap-3.5   border-b border-primary-900 pb-4">
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                {' '}
                Payment on
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                {' '}
                9 Decemeber, 2023 | 10:00 AM
              </Text>
            </View>
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                {' '}
                Booked Date
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                {' '}
                19 December, 2023
              </Text>
            </View>
          </View>
          {/* Row Second *\/}
          <View className="gap-8  border-b border-primary-900 pb-4">
            <View className="gap-3.5">
              <View className="flex-row items-center justify-between">
                <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                  Amount
                </Text>
                <Text className="font-inter text-sm font-semibold dark:text-black-50">
                  {' '}
                  $90.50
                </Text>
              </View>
              <View className="flex-row items-center justify-between">
                <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                  {' '}
                  Tax
                </Text>
                <Text className="font-inter text-sm font-semibold dark:text-black-50">
                  {' '}
                  $5.00
                </Text>
              </View>
            </View>

            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                Total
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                $95.50
              </Text>
            </View>
          </View>
          {/* Row Thired *\/}
          <View className="gap-3.5 ">
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                Name
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                {' '}
                Raj Kumar
              </Text>
            </View>
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                Phone Number
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                +91 8949952520
              </Text>
            </View>
            <View className="flex-row items-center justify-between">
              <Text className="font-PoppinsRegular text-sm font-normal dark:text-black-300">
                Transaction ID
              </Text>
              <Text className="font-inter text-sm font-semibold dark:text-black-50">
                #IRM878748G548
              </Text>
            </View>
          </View>
        </View>
        <Button
          variant="secondary"
          label="Download Invoice"
          onPress={() => {}}
        />
      </View> */}
    </View>
  );
}
