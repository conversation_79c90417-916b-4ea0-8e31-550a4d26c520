import { View, Text, ActivityIndicator } from 'react-native';
import React from 'react';
import { useThemeConfig } from '@/lib/use-theme-config';
import { useAuthContext } from '@/lib/auth/auth-context';
import { Redirect } from 'expo-router';

export default function LoadingScreen() {
  const theme = useThemeConfig();
  const { user } = useAuthContext()

  if (user) {
    return <Redirect href="/home" />
  }

  return (
    <View 
      style={{ 
        flex: 1, 
        justifyContent: 'center', 
        alignItems: 'center',
        backgroundColor: theme.dark ? '#000' : '#fff'
      }}
    >
      <ActivityIndicator size="large" color={theme.dark ? '#fff' : '#000'} />
      <Text 
        style={{ 
          marginTop: 16, 
          fontSize: 16,
          color: theme.dark ? '#fff' : '#000'
        }}
      >
        Loading...
      </Text>
    </View>
  );
} 