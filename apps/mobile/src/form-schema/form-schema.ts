import { z } from 'zod';

const phoneRegex = new RegExp(
  /^([+]?[\s0-9]+)?(\d{3}|[(]?[0-9]+[)])?([-]?[\s]?[0-9])+$/
);
const travelerType = ['SOLO', 'FAMILY', 'GROUP'] as const;
const gender = ['MALE', 'FEMALE', 'OTHERS'] as const;
const status = [
  'DRAFT',
  'ACTIVE',
  'ASSIGNED',
  'CANCELLED',
  'COMPLETED',
] as const;
const typeOfUser = ['COMPANION', 'TRAVELLER'] as const;
export const userSchema = z.object({
  name: z
    .string({ required_error: 'First Name is required' })
    .min(1, { message: 'First Name is required' })
    .min(3, { message: 'First Name must be at least 3 char long' }),
  lastName: z.string().optional(),
  email: z
    .string()
    .email({ message: 'Invalid email address' })
    .optional(),
  phone: z.string().optional(),
  bookingFor: z.enum(["SELF", "FATHER", "MOTHER", "RELATIVE"]).default("SELF"),
  typeOfTraveler: z.enum(travelerType),
  gender: z.enum(gender),
  genderPreference: z
    .enum(gender, {
      message: 'Gender Preference can be MALE | FEMALE | OTHERS',
    })
    .optional(),
  openToAllGenders: z.boolean().default(false),
  languages: z
    .array(z.string(), { message: 'At least one language is required' })
    .optional(),
  about: z
    .string({ required_error: 'About yourself is required' })
    .min(1, { message: 'About yourself is required' })
    .min(5, { message: 'Write about yourself should contain atleast 5 chars' }),
  userProfile: z.string().optional(),
  userProfileUrl: z.string().optional(),
});

export const flightSchema = z.object({
  flightPNR: z.string().min(1, { message: 'Flight number is required.' }),
  flightDate: z.string().min(1, { message: 'Flight Date is required.' }),
  flightTime: z.string().min(1, { message: 'Flight Time is required.' }),
  flightEndDate: z.string().min(1, { message: 'Flight End Date is required.' }),
  flightEndTime: z.string().min(1, { message: 'Flight End Time is required.' }),
});

export const bookingSchema = z.object({
  travelersPhoto: z
    .string()
    .min(1, { message: 'Traveler photo is required.' }),
  passportPhoto: z.string().min(1, { message: 'Passport photo is required.' }),
  compensationValue: z
    .string({
      invalid_type_error: 'Enter the correct value for compensation value.',
    })
    .min(1, { message: 'Compensation value is required.' }),
});

export const companionSchema = z.object({
  name: z
    .string({ required_error: 'Name is required' })
    .min(1, { message: 'Name is required' })
    .min(3, { message: 'Name must be at least 3 char long' }),
  lastName: z.string({ required_error: 'Last Name is required' }),
  typeOfTraveler: z.enum(travelerType),
  gender: z.enum(gender),
  genderPreference: z.enum(gender, {
    message: 'Gender Preference can be MALE | FEMALE | OTHERS',
  }),
  openToAllGender: z.boolean(),
  languages: z
    .array(z.string(), { message: 'At least one language is required' })
    .optional(),
  about: z
    .string({ required_error: 'About yourself is required' })
    .min(1, { message: 'About yourself is required' })
    .min(5, { message: 'Write about yourself should contain atleast 5 chars' }),
});

export const companionPhotoSchema = z.object({
  companionPhoto: z
    .string()
    .min(1, { message: 'Companion photo is required.' }),
  passportPhoto: z.string().min(1, { message: 'Passport photo is required.' }),
  compensationValue: z
    .string({
      invalid_type_error: 'Enter the correct value for compensation value.',
    })
    .min(1, { message: 'Compensation value is required.' }),
});

export const travelerSchema = z.object({
  name: z
    .string({ required_error: 'Name is required' })
    .min(1, { message: 'Name is required' })
    .min(3, { message: 'Name must be at least 3 char long' }),
  lastName: z.string({ required_error: 'Last Name is required' }).optional(),
  typeOfTraveller: z.enum(['SOLO', 'FAMILY', 'GROUP', 'BUSINESS']),
  bookingFor: z.enum(['YOURSELF', 'SOMEONE_ELSE']),
  gender: z.enum(gender),
  genderPreference: z.enum(gender, {
    message: 'Gender Preference can be MALE | FEMALE | OTHERS',
  }),
  openToAllGenders: z.boolean(),
  languages: z
    .array(z.string(), { message: 'At least one language is required' })
    .optional(),
  about: z
    .string({ required_error: 'About yourself is required' })
    .min(1, { message: 'About yourself is required' })
    .min(5, { message: 'Write about yourself should contain atleast 5 chars' }),
});

export const travelerPhotoSchema = z.object({
  companionPhoto: z
    .string()
    .min(1, { message: 'Traveler photo is required.' }),
  passportPhoto: z.string().optional(),
  compensationValue: z
    .string({
      invalid_type_error: 'Enter the correct value for compensation value.',
    })
    .min(1, { message: 'Compensation value is required.' }),
});

export const airportSchema = z.object({
  name: z
    .string({ required_error: 'Airport name is required' })
    .min(1, { message: 'Airport name is required' })
    .min(2, { message: 'Airport name must be at least 2 characters long' }),
  shortCode: z
    .string({ required_error: 'Airport code is required' })
    .min(1, { message: 'Airport code is required' })
    .max(3, { message: 'Airport code must be 3 characters or less' })
    .toUpperCase(),
  city: z
    .string({ required_error: 'City is required' })
    .min(1, { message: 'City is required' })
    .min(2, { message: 'City must be at least 2 characters long' }),
  state: z
    .string({ required_error: 'State/Province is required' })
    .min(1, { message: 'State/Province is required' }),
  country: z
    .string({ required_error: 'Country is required' })
    .min(1, { message: 'Country is required' }),
  icao: z.string().optional(),
  type: z.string().optional(),
  timezone: z.string().optional(),
  lat: z.number().optional(),
  lon: z.number().optional(),
});
