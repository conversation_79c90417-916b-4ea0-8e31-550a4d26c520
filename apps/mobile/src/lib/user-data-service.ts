import { Env } from '@env';
import { type Models } from 'react-native-appwrite';

import { account, databases } from './appwrite';

/**
 * Service to handle user data storage and retrieval using COLLECTION_USER database
 * instead of user preferences
 */

// Define the structure of user data
export interface UserData extends Models.Document {
  // Basic user preferences
  notification?: boolean;
  muteChat?: boolean;
  openMicroPhone?: boolean;
  email?: string;

  // Profile related
  userProfile?: string;
  userProfileUrl?: string;
  travellerProfile?: boolean;
  companionProfile?: boolean;

  // Stripe related
  stripeCustomerId: string;
  stripeConnectAccountId?: string;
}

/**
 * Get user data from COLLECTION_USER database
 * Falls back to preferences if data doesn't exist in database
 */
export async function getUserData(): Promise<UserData | null> {
  try {
    // Get current user
    const user = await account.get();

    try {
      // Try to get user data from database
      const userData = await databases.getDocument<UserData>(
        Env.COLLECTION_ID,
        Env.COLLECTION_USER,
        user.$id
      );

      return userData as UserData;
    } catch (dbError) {
      // If document doesn't exist in database, check preferences
      console.log(
        'User data not found in database, checking preferences',
        dbError
      );

      // Get preferences as fallback
      const prefs = await account.getPrefs();

      // If we have preferences, migrate them to the database
      if (Object.keys(prefs).length > 0) {
        await migratePrefsToDatabase(prefs);
        return prefs as UserData;
      }

      // If no preferences exist, return empty object
      return null;
    }
  } catch (error) {
    console.error('Error getting user data:', error);
    return null;
  }
}

/**
 * Update user data in COLLECTION_USER database
 * @param data Partial user data to update
 */
export async function updateUserData(data: Partial<UserData>): Promise<void> {
  try {
    // Get current user
    const user = await account.get();

    try {
      // Try to get existing document to check if it exists
      await databases.getDocument(
        Env.COLLECTION_ID,
        Env.COLLECTION_USER,
        user.$id
      );

      // If document exists, update it
      await databases.updateDocument(
        Env.COLLECTION_ID,
        Env.COLLECTION_USER,
        user.$id,
        data
      );
    } catch (dbError) {
      // If document doesn't exist, create it
      await databases.createDocument(
        Env.COLLECTION_ID,
        Env.COLLECTION_USER,
        user.$id,
        data
      );
    }
  } catch (error) {
    console.error('Error updating user data:', error);
    throw error;
  }
}

/**
 * Migrate existing preferences to the database
 * @param prefs Preferences to migrate
 */
async function migratePrefsToDatabase(
  prefs: Record<string, any>
): Promise<void> {
  try {
    await updateUserData(prefs);
  } catch (error) {
    console.error('Error migrating preferences to database:', error);
  }
}

/**
 * Get a specific user data field
 * @param key The key to get
 * @param defaultValue Default value if key doesn't exist
 */
export async function getUserDataField<T>(
  key: string,
  defaultValue?: T
): Promise<T | undefined> {
  const userData = await getUserData();
  return (userData?.[key] as T) ?? defaultValue;
}

/**
 * Update a specific user data field
 * @param key The key to update
 * @param value The value to set
 */
export async function updateUserDataField<T>(
  key: string,
  value: T
): Promise<void> {
  await updateUserData({ [key]: value });
}
