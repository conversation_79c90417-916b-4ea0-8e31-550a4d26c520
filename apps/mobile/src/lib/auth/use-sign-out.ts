import { useRouter } from 'expo-router';
import { useAuthContext } from './auth-context';
import { signOut as signOutAuth } from './index';

export function useSignOut() {
  const router = useRouter();
  const { signOut: signOutContext } = useAuthContext();

  const handleSignOut = async () => {
    try {
      // Sign out from Appwrite
      await signOutContext();
      
      // Sign out from local auth store
      signOutAuth();
      
      // Navigate to auth screen
      router.replace('/(auth)');
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  return { signOut: handleSignOut };
} 