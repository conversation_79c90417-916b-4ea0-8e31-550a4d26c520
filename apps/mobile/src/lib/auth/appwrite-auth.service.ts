import { account } from '@/lib/appwrite';
import { Models } from 'react-native-appwrite';
import { ID } from 'react-native-appwrite';

export interface SignUpData {
  email: string;
  password: string;
  name: string;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface AuthResponse {
  success: boolean;
  user?: Models.User<Models.Preferences>;
  error?: string;
}

export class AppwriteAuthService {
  static async signUp(data: SignUpData): Promise<AuthResponse> {
    try {
      const user = await account.create(
        ID.unique(),
        data.email,
        data.password,
        data.name
      );
      
      // Auto sign in after sign up
      await this.signIn({ email: data.email, password: data.password });
      
      return { success: true, user };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'Sign up failed' 
      };
    }
  }

  static async signIn(data: SignInData): Promise<AuthResponse> {
    try {
      const session = await account.createSession(data.email, data.password);
      const user = await account.get();
      
      return { success: true, user };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'Sign in failed' 
      };
    }
  }

  static async signOut(): Promise<AuthResponse> {
    try {
      await account.deleteSession('current');
      return { success: true };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'Sign out failed' 
      };
    }
  }

  static async getCurrentUser(): Promise<AuthResponse> {
    try {
      const user = await account.get();
      return { success: true, user };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'Failed to get user' 
      };
    }
  }

  static async sendVerificationEmail(email: string): Promise<AuthResponse> {
    try {
      await account.createVerification('http://localhost:3000/verify');
      return { success: true };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'Failed to send verification email' 
      };
    }
  }

  static async verifyEmail(userId: string, secret: string): Promise<AuthResponse> {
    try {
      await account.updateVerification(userId, secret);
      return { success: true };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'Email verification failed' 
      };
    }
  }

  static async sendPasswordResetEmail(email: string): Promise<AuthResponse> {
    try {
      await account.createRecovery(email, 'http://localhost:3000/reset-password');
      return { success: true };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'Failed to send password reset email' 
      };
    }
  }

  static async resetPassword(userId: string, secret: string, password: string): Promise<AuthResponse> {
    try {
      await account.updateRecovery(userId, secret, password);
      return { success: true };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'Password reset failed' 
      };
    }
  }

  static async updateProfile(name: string): Promise<AuthResponse> {
    try {
      const user = await account.updateName(name);
      return { success: true, user };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'Profile update failed' 
      };
    }
  }

  static async updateEmail(email: string, password: string): Promise<AuthResponse> {
    try {
      await account.updateEmail(email, password);
      return { success: true };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'Email update failed' 
      };
    }
  }

  static async updatePassword(password: string, oldPassword: string): Promise<AuthResponse> {
    try {
      await account.updatePassword(password, oldPassword);
      return { success: true };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.message || 'Password update failed' 
      };
    }
  }
} 