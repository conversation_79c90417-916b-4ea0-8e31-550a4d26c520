import { QueryClient } from "@tanstack/react-query";
import { createTR<PERSON><PERSON>lient, httpBatchLink, loggerLink } from "@trpc/client";
import { createTRPCOptionsProxy } from "@trpc/tanstack-react-query";
import superjson from "superjson";

import type { AppRouter } from "@repo/customer-api";

import { getBaseUrl } from "./base-url";
import { account } from "./appwrite";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1 * 60 * 1000, // 1 minutes
      // ...
    },
  },
});

/**
 * A set of typesafe hooks for consuming your API.
 */
export const trpc = createTRPCOptionsProxy<AppRouter>({
  client: createTRPCClient({
    links: [
      loggerLink({
        enabled: (opts) =>
          process.env.NODE_ENV === "development" ||
          (opts.direction === "down" && opts.result instanceof Error),
        colorMode: "ansi",
      }),
      httpBatchLink({
        transformer: superjson,
        url: `${getBaseUrl()}/api/trpc`,
        headers: async () => {
          const headers = new Map<string, string>();
          headers.set("x-trpc-source", "expo-react");
          try {
            const jwtResult = await account.createJWT();
            console.log("jwt", jwtResult);
            headers.set("Authorization", "Bearer " + jwtResult.jwt);

            return headers;
          } catch (error) {
            console.error(error);
          }
          return headers;
        },
      }),
    ],
  }),
  queryClient,
});

export { type RouterInputs, type RouterOutputs } from "@repo/customer-api";
