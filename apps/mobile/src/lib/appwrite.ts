import { Account, Client, Databases, Storage } from 'react-native-appwrite';

export const client = new Client()
  .setProject('6711297700083cd76ad6') //project ID
  .setPlatform('com.nextflytech.thedal'); //app platform identifier

export const databases = new Databases(client);

export const account = new Account(client); //account instance to interact with the authentication system

export const storage = new Storage(client);
