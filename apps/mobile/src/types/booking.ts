// Booking related type definitions

export type BookingGender = 'MALE' | 'FEMALE' | 'OTHERS';

export type BookingStatus =
  | 'DRAFT'
  | 'ACTIVE'
  | 'ASSIGNED'
  | 'CONFIRMED'
  | 'COMPLETED'
  | 'CANCELLED';

export type TravelerType = 'SOLO' | 'FAMILY' | 'GROUP';

export type BookingFor = 'SELF' | 'FATHER' | 'MOTHER' | 'RELATIVE';

export interface Language {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Airport {
  id: string;
  name: string;
  locationCity: string;
  shortCode: string;
  lat: string | null;
  lon: string | null;
  city: string | null;
  state: string | null;
  country: string | null;
  timezone: string | null;
  type: string | null;
  icao: string | null;
  runwayLength: string | null;
  elevation: string | null;
  directFlights: string | null;
  carriers: string | null;
  phone: string | null;
  email: string | null;
  url: string | null;
  woeid: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface BookingDestination {
  order: number;
  airports: Airport;
  bookingCompanion: any | null;
}

export interface BookingCompanionLanguage {
  id: string;
  bookingCompanionId: string;
  languageId: string | null;
  createdAt: Date;
  language: Language;
}

export interface BookingCompanionDestination {
  id: string;
  bookingCompanionId: string | null;
  airportId: string | null;
  order: number;
  createdAt: Date;
  updatedAt: Date;
  airport: Airport | null;
}

export interface BookingTravelerLanguage {
  id: string;
  bookingTravelerId: string | null;
  languageId: string | null;
  createdAt: Date;
  language: Language;
}

export interface BookingTravelerDestination {
  id: string;
  bookingTravelerId: string | null;
  airportId: string | null;
  order: number;
  createdAt: Date;
  updatedAt: Date;
  airport: Airport | null;
}

export interface BookingCompanion {
  id: string;
  appwriteUserId: string | null;
  name: string | null;
  lastName?: string | null;
  about: string | null;
  gender: BookingGender | null;
  genderPreference: BookingGender | null;
  typeOfTraveler: TravelerType | null;
  openToAllGenders: boolean | null;
  flightPNR: string | null;
  flightTime: Date | null;
  flightEndTime: Date | null;
  timezone: string | null;
  searchField: string | null;
  travelersPhoto: string | null;
  passportPhoto?: string | null;
  compensationValue: number | null;
  status: BookingStatus;
  createdAt?: Date;
  updatedAt?: Date;
  languages: BookingCompanionLanguage[];
  destinations: BookingCompanionDestination[];
  connectionRequestsAsTraveler?: ConnectionRequest[];
}

export interface BookingTraveler {
  id: string;
  name: string | null;
  lastName?: string | null;
  gender: BookingGender | null;
  genderPreference: BookingGender | null;
  openToAllGenders: boolean | null;
  about: string | null;
  status: BookingStatus;
  typeOfTraveler: TravelerType | null;
  flightPNR: string | null;
  flightTime: Date | null;
  flightEndTime: Date | null;
  searchField: string | null;
  companionPhoto: string | null;
  passportPhoto?: string | null;
  compensationValue: number | null;
  createdAt?: Date;
  updatedAt?: Date;
  languages: BookingTravelerLanguage[];
  destinations: BookingTravelerDestination[];
  connectionRequestsAsCompanion?: ConnectionRequest[];
}

export interface ConnectionRequest {
  id: string;
  bookingCompanionId: string;
  bookingTravelerId: string;
  status: 'PENDING' | 'ACCEPTED' | 'REJECTED';
}

// Utility types for creating/updating bookings (without system fields)
export type CreateBookingInput = Omit<
  BookingCompanion,
  | '$id'
  | '$createdAt'
  | '$updatedAt'
  | '$databaseId'
  | '$collectionId'
  | 'languages'
  | 'bookingDestinations'
> & {
  languages?: string[]; // Language IDs for creation
  bookingDestinations?: Omit<
    BookingDestination,
    '$id' | '$createdAt' | '$updatedAt' | '$databaseId' | '$collectionId'
  >[];
};

export type UpdateBookingInput = Partial<CreateBookingInput>;

// Response types for API calls
export type BookingCompanionResponse = BookingCompanion;
export type BookingsListResponse = BookingCompanion[];
