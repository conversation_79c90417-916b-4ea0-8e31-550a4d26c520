import { View, Text, Pressable } from 'react-native';
import React from 'react';
import { Image } from 'expo-image';
import { RadioButton } from 'react-native-paper';

type StateType = {
  checked: string;
  setChecked: React.Dispatch<React.SetStateAction<string>>;
};

const Reviews: React.FC<StateType> = ({ checked, setChecked }) => {
  return (
    <View>
      {/* Review 5 stars */}
      <Pressable
        className="flex-row items-center justify-between mb-"
        onPress={() => setChecked('five')}
      >
        <View className="flex-row items-center gap-2.5">
          {[...Array(5)].map((_, index) => (
            <Image
              key={index}
              source={require('../../assets/images/single-star.png')}
              contentFit="contain"
              style={{ width: 17.067, height: 16 }}
            />
          ))}
          <Text className="text-black-0 font-medium text-xs leading-5">
            4.5 and above
          </Text>
        </View>
        <RadioButton
          value="five"
          status={checked === 'five' ? 'checked' : 'unchecked'}
          onPress={() => setChecked('five')}
          color="#FDFDFD"
          
        />
      </Pressable>

      {/* Review 4 stars */}
      <Pressable
        className="flex-row items-center justify-between"
        onPress={() => setChecked('four')}
      >
        <View className="flex-row items-center gap-2.5">
          {[...Array(4)].map((_, index) => (
            <Image
              key={index}
              source={require('../../assets/images/single-star.png')}
              contentFit="contain"
              style={{ width: 17.067, height: 16 }}
            />
          ))}
          <Image
            source={require('../../assets/images/single-star-blank.png')}
            contentFit="contain"
            style={{ width: 17.067, height: 16 }}
          />
          <Text className="text-black-0 font-medium text-xs leading-5">
            4.0-4.5
          </Text>
        </View>
        <RadioButton
          value="four"
          status={checked === 'four' ? 'checked' : 'unchecked'}
          onPress={() => setChecked('four')}
          color="#FDFDFD"
        />
      </Pressable>

      {/* Review 3 stars */}
      <Pressable
        className="flex-row items-center justify-between"
        onPress={() => setChecked('three')}
      >
        <View className="flex-row items-center gap-2.5">
          {[...Array(3)].map((_, index) => (
            <Image
              key={index}
              source={require('../../assets/images/single-star.png')}
              contentFit="contain"
              style={{ width: 17.067, height: 16 }}
            />
          ))}
          {[...Array(2)].map((_, index) => (
            <Image
              key={index}
              source={require('../../assets/images/single-star-blank.png')}
              contentFit="contain"
              style={{ width: 17.067, height: 16 }}
            />
          ))}
          <Text className="text-black-0 font-medium text-xs leading-5">
            3.0-4.0
          </Text>
        </View>
        <RadioButton
          value="three"
          status={checked === 'three' ? 'checked' : 'unchecked'}
          onPress={() => setChecked('three')}
          color="#FDFDFD"
        />
      </Pressable>

      {/* Review 2 stars */}
      <Pressable
        className="flex-row items-center justify-between"
        onPress={() => setChecked('two')}
      >
        <View className="flex-row items-center gap-2.5">
          {[...Array(2)].map((_, index) => (
            <Image
              key={index}
              source={require('../../assets/images/single-star.png')}
              contentFit="contain"
              style={{ width: 17.067, height: 16 }}
            />
          ))}
          {[...Array(3)].map((_, index) => (
            <Image
              key={index}
              source={require('../../assets/images/single-star-blank.png')}
              contentFit="contain"
              style={{ width: 17.067, height: 16 }}
            />
          ))}
          <Text className="text-black-0 font-medium text-xs leading-5">
            2.5-3.0
          </Text>
        </View>
        <RadioButton
          value="two"
          status={checked === 'two' ? 'checked' : 'unchecked'}
          onPress={() => setChecked('two')}
          color="#FDFDFD"
        />
      </Pressable>

      {/* Review 1 star */}
      <Pressable
        className="flex-row items-center justify-between"
        onPress={() => setChecked('one')}
      >
        <View className="flex-row items-center gap-2.5">
          <Image
            source={require('../../assets/images/single-star.png')}
            contentFit="contain"
            style={{ width: 17.067, height: 16 }}
          />
          {[...Array(4)].map((_, index) => (
            <Image
              key={index}
              source={require('../../assets/images/single-star-blank.png')}
              contentFit="contain"
              style={{ width: 17.067, height: 16 }}
            />
          ))}
          <Text className="text-black-0 font-medium text-xs leading-5">
            2.5 and below
          </Text>
        </View>
        <RadioButton
          value="one"
          status={checked === 'one' ? 'checked' : 'unchecked'}
          onPress={() => setChecked('one')}
          color="#FDFDFD"
        />
      </Pressable>
    </View>
  );
};

export default Reviews;
