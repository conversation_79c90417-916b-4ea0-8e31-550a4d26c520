/* eslint-disable max-lines-per-function */
import { Image } from 'expo-image';
import React from 'react';
import { Pressable, Text, View } from 'react-native';
import { TouchableOpacity } from 'react-native-gesture-handler';

import { cn } from '@/lib/utils';
type IStepper = {
  selectedStep: number;
  onPress: () => void;
  onPress2: () => void;
};
const Stepper: React.FC<IStepper> = ({ selectedStep, onPress, onPress2 }) => {
  return (
    <View className="mt-2.5 flex flex-row items-center justify-between ">
      <Pressable className="flex-row items-center gap-2" onPress={onPress}>
        <Image
          source={require('../../assets/images/back-icon.svg')}
          style={{ height: 14, width: 6 }}
          contentFit="contain"
          tintColor={'white'}
        />

        <Text className="font-inter text-sm font-medium leading-5 text-black-0">
          Back
        </Text>
      </Pressable>

      <View className="flex flex-row items-center gap-2 ">
        <View
          className={cn(
            'h-[40px] w-[40px] rounded-[20px] bg-primary-950 items-center justify-center border border-black-0 ',
            selectedStep > 0 ? 'border-black-0' : 'border-black-400',
          )}
        >
          <View
            className={cn(
              'h-[30px] w-[30px] rounded-[15px]  items-center justify-center',
              selectedStep > 0 ? 'bg-black-0 ' : 'bg-primary-950 ',
            )}
          >
            <Image
              source={require('../../assets/images/user-stepper.svg')}
              style={{ height: 22, width: 22 }}
              tintColor={'#999999'}
            />
          </View>
        </View>
        <View
          className={cn(
            'border  w-[40px]',
            selectedStep > 1 ? 'border-black-0' : 'border-black-400',
          )}
        />

        <View
          className={cn(
            'h-[40px] w-[40px] rounded-[20px] bg-primary-950 items-center justify-center border border-black-0',
            selectedStep > 1 ? 'border-black-0' : 'border-black-400',
          )}
        >
          <View
            className={cn(
              'h-[30px] w-[30px] rounded-[15px] bg-primary-50 items-center justify-center',
              selectedStep > 1 ? 'bg-black-0 ' : 'bg-primary-950 ',
            )}
          >
            <Image
              source={require('../../assets/images/stepper-plane.svg')}
              style={{ height: 22, width: 22 }}
              tintColor={'#999999'}
            />
          </View>
        </View>
        <View
          className={cn(
            'border  w-[40px]',
            selectedStep > 2 ? 'border-black-0' : 'border-black-400',
          )}
        />

        <View
          className={cn(
            'h-[40px] w-[40px] rounded-[20px] bg-primary-950 items-center justify-center border border-black-0',
            selectedStep > 2 ? 'border-black-0' : 'border-black-400',
          )}
        >
          <View
            className={cn(
              'h-[30px] w-[30px] rounded-[15px] bg-primary-50 items-center justify-center',
              selectedStep > 2 ? 'bg-black-0 ' : 'bg-primary-950 ',
            )}
          >
            <Image
              source={require('../../assets/images/stepper-image.svg')}
              style={{ height: 22, width: 22 }}
              tintColor={'#999999'}
            />
          </View>
        </View>
      </View>
      <TouchableOpacity onPress={onPress2}>
        <Text className="font-inter text-sm font-medium leading-5 text-primary-750 dark:text-black-200">
          Cancel
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default Stepper;
