import { router } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Image, Modal, Text, View } from 'react-native';

import { useNetworkConnectivity } from '@/lib/hooks';

export const ConnectivityModal = () => {
  const { isConnected } = useNetworkConnectivity();
  const [showModal, setShowModal] = useState(false);

  // For testing purposes - you can temporarily set this to true to test the modal
  const TEST_MODE = false;

  useEffect(() => {
    if (TEST_MODE || !isConnected) {
      setShowModal(true);
    }

    // When connectivity is restored, navigate back to root
    setShowModal(false);
    // router.replace('/');
  }, [isConnected]);

  return (
    <Modal
      visible={showModal}
      transparent
      animationType="fade"
      statusBarTranslucent
    >
      <View className="flex-1 bg-black/50 justify-center items-center px-6">
        <View className="bg-white dark:bg-gray-800 rounded-2xl p-8 items-center max-w-sm w-full">
          {/* No Connectivity Icon */}
          <View className="w-20 h-20 bg-red-100 dark:bg-red-900/20 rounded-full items-center justify-center mb-6">
            <Image
              source={require('../../assets/images/cross.png')}
              className="w-10 h-10"
              resizeMode="contain"
            />
          </View>

          {/* Title */}
          <Text className="text-2xl font-bold text-gray-900 dark:text-white text-center mb-3">
            No Internet Connection
          </Text>

          {/* Description */}
          <Text className="text-base text-gray-600 dark:text-gray-300 text-center mb-6 leading-6">
            Please check your internet connection and try again. This screen
            will automatically disappear when you're back online.
          </Text>

          {/* Loading indicator */}
          <View className="flex-row items-center">
            <View className="w-2 h-2 bg-blue-500 rounded-full mr-2 animate-pulse" />
            <Text className="text-sm text-gray-500 dark:text-gray-400">
              Checking connection...
            </Text>
          </View>
        </View>
      </View>
    </Modal>
  );
};
