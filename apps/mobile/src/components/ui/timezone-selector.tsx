import { TIMEZONES, ITimezone } from '@/lib/time-zones';

import React from 'react';
import { FlatList, Pressable, Text, View } from 'react-native';
import { useDebouncedCallback } from 'use-debounce';

import InputText from '@/components/input-txt';
import { Button } from '@/components/ui/button';
import { Modal, useModal } from '@/components/ui/modal';
import { cn } from '@/lib/utils';
import { BottomSheetFlashList } from '@gorhom/bottom-sheet';

interface TimezoneSelectionInputProps {
  timezone?: string;
  setTimezone: (timezone: string) => void;
  placeholder?: string;
}

const MODAL_SNAP_POINTS = ['95%'];
const SEARCH_PLACEHOLDER = 'Search By Timezone Name / City';
const ICON_DIMENSIONS = { height: 16, width: 16 };

const TimezoneSelectionInput = ({
  timezone,
  setTimezone,
  placeholder,
}: TimezoneSelectionInputProps) => {
  const { ref, present, dismiss } = useModal();
  const [filterTimezone, setFilterTimezone] = React.useState('');

  const debouncedSetFilter = useDebouncedCallback(
    (value: string) => setFilterTimezone(value),
    300
  );

  // Add memoization for filtered timezones
  const filteredTimezones = React.useMemo(
    () =>
      filterTimezone
        ? TIMEZONES.filter(
            (t) =>
              t.name.toLowerCase().includes(filterTimezone.toLowerCase()) ||
              t.tzCode.toLowerCase().includes(filterTimezone.toLowerCase())
          )
        : TIMEZONES,
    [filterTimezone]
  );

  const handleTimezoneSelect = (item: ITimezone) => {
    setTimezone(item.tzCode);
    dismiss();
  };

  // Memoize the timezone item component
  const TimezoneItem = React.memo(
    ({
      item,
      onSelect,
    }: {
      item: ITimezone;
      onSelect: (item: ITimezone) => void;
    }) => (
      <Pressable onPress={() => onSelect(item)}>
        <Text className="p-3 text-white">{item.name}</Text>
      </Pressable>
    )
  );

  const selectedTimezone = TIMEZONES.find((t) => t.tzCode === timezone);

  return (
    <View
      className={cn(
        'rounded-[12px] mt-2 flex-row items-center dark:bg-bgtextInput border border-customborder px-2'
      )}
    >
      <Pressable
        onPress={() => {
          present();
        }}
        className="flex-1"
      >
        <View key={timezone} className="w-full flex-1 flex-row py-3">
          <View className="flex-1 flex-row items-center gap-2">
            <Text className="flex-1 text-[14px] font-normal leading-5 text-black-300">
              {selectedTimezone?.name ?? placeholder ?? 'Select Timezone'}
            </Text>
          </View>
        </View>
      </Pressable>
      <Modal
        ref={ref}
        snapPoints={MODAL_SNAP_POINTS}
        onDismiss={dismiss}
        backgroundStyle={{ backgroundColor: '#020717' }}
      >
        <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
          <View className="flex-1 justify-between">
            <View className="mb-3 mt-6 flex-row items-center">
              <InputText
                onPress={() => {}}
                placeholder={SEARCH_PLACEHOLDER}
                className="flex-1 dark:bg-blue"
                iconSourceFirst={require('../../../assets/images/search-icon.png')}
                iconStyleFirst={ICON_DIMENSIONS}
                onChangeText={debouncedSetFilter}
              />
            </View>
              <BottomSheetFlashList
                estimatedItemSize={40}
              data={filteredTimezones}
              renderItem={({ item }) => (
                <TimezoneItem item={item} onSelect={handleTimezoneSelect} />
              )}
              keyExtractor={(item) => item.tzCode}
            />
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default TimezoneSelectionInput;
