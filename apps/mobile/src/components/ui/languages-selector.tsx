import { Env } from '@env';
import { Image } from 'expo-image';
import React, { useEffect, useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { KeyboardAvoidingView, KeyboardAwareScrollView } from 'react-native-keyboard-controller';

import InputText from '@/components/input-txt';
import { Button } from '@/components/ui/button';
import { Modal, useModal } from '@/components/ui/modal';
import { databases } from '@/lib/appwrite';
import { trpc } from '@/lib/api';
import { useQuery } from '@tanstack/react-query';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { ScrollView } from 'react-native-gesture-handler';

const LanguagesSelector = ({
  languageIds,
  setLanguageIds,
}: {
  languageIds: string[];
  setLanguageIds: (languageIds: string[]) => void;
}) => {
  const insets = useSafeAreaInsets();
  const { ref, present, dismiss } = useModal();
  const [languageThatTravellerSpeaks, setLanguageThatTravellerSpeaks] =
    useState<{ id: string; name: string }[]>([]);
  // const [availableLanguages, setAvailableLanguages] = useState<
  //   { name: string; id: string }[]
  // >([]);

  const { data: availableLanguages } = useQuery(trpc.languages.getAll.queryOptions());

  console.log("availableLanguages", availableLanguages);

  // const fetchLanguages = async () => {
  //   const info = await databases.listDocuments(
  //     Env.COLLECTION_ID,
  //     Env.COLLECTION_LANGUAGES
  //   );
  //   const document = info.documents.map((document) => ({
  //     name: document.name,
  //     id: document.$id,
  //   }));
  //   setAvailableLanguages(document);
  // };

  //getting the languages from database
  // useEffect(() => {
  //   fetchLanguages();
  // }, []);

  useEffect(() => {
    setLanguageThatTravellerSpeaks(
      availableLanguages?.filter((i) => languageIds.includes(i.id)) ?? []
    );
  }, [languageIds, availableLanguages]);

  // const [showMore, setShowMore] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>('');

  const saveLanguages = () => {
    setLanguageIds(languageThatTravellerSpeaks.map((i) => i.id));
    dismiss();
  };

  const filteredLanguages = availableLanguages?.filter(
    (language) =>
      !languageThatTravellerSpeaks
        .map((spoken) => spoken.id)
        .includes(language.id) && language.name.includes(searchQuery)
  ) ?? [];

  const languages = availableLanguages?.filter((language) =>
    languageIds.includes(language.id)
  ) ?? [];

  const addOrRemoveLanguage = (language: { id: string; name: string }) => {
    const languageExist = languageThatTravellerSpeaks.find(
      (i) => i.id === language.id
    );
    if (languageExist) {
      setLanguageThatTravellerSpeaks(
        languageThatTravellerSpeaks.filter((i) => i.id !== language.id)
      );
    } else {
      setLanguageThatTravellerSpeaks([
        ...languageThatTravellerSpeaks,
        language,
      ]);
    }
  };

  return (
    <>
      <Pressable
        onPress={() => {
          present();
        }}
      >
        <View className="mt-2.5 flex-row flex-wrap items-center gap-2 rounded-[12px] border border-borderdark p-5 dark:bg-bgtextInput">
          {languages.length > 0 ? (
            <>
              {languages.map((language) => (
                <Text
                  key={language.id}
                  className="font-inter text-[14px] text-black-0 dark:text-black-100"
                >
                  {language.name}
                </Text>
              ))}
            </>
          ) : (
            <Text className="text-[14px] text-black-300">Select Language</Text>
          )}
        </View>
      </Pressable>
      <Modal
        ref={ref}
        snapPoints={['100%']}
        onDismiss={dismiss}
        // containerStyle={{ backgroundColor: '#000000B2' }}
        backgroundStyle={{ backgroundColor: '#020717' }}
      >
        <KeyboardAvoidingView className="w-full flex-1 bg-bgmodal px-5" style={{ paddingBottom: insets.bottom }}>
          <View className="mt-5 gap-2">
            <Text className="font-PoppinsBold text-xl font-bold leading-[30px] text-black-50">
              Choose Language Traveler Speaks
            </Text>
            <Text className="font-inter text-[14px] font-medium text-black-950 dark:text-black-300">
              Add the language that traveler speaks, add at least 3 languages
              that you speak.
            </Text>
          </View>
          <View className="mt-6 w-full flex-1 justify-between">
            <InputText
              value={searchQuery}
              onChangeText={setSearchQuery}
              onPress={() => {}}
              placeholder="Language you spoke the most?"
              placeholderTextColor={'#B3B3B3'}
              className="border-0 dark:bg-primary-900 mb-4"
              iconSourceFirst={require('../../../assets/images/search-icon.png')}
              iconStyleFirst={{ height: 14, width: 14 }}
            />
            <ScrollView>
              <View className="gap-8 pb-4">
              <View className="w-full flex-row flex-wrap items-center gap-4">
                {languageThatTravellerSpeaks.map((language) => (
                  <Pressable
                    onPress={() => addOrRemoveLanguage(language)}
                    key={language.id}
                  >
                    <View
                      className="rounded-[16px] border px-2.5 py-[6px] dark:border-bgtextInput "
                      key={language.id}
                    >
                      <View className="flex-row items-center gap-1">
                        <Text className="font-inter font-semibold leading-5 dark:text-black-100 ">
                          {language.name}
                        </Text>
                        <Image
                          source={require('../../../assets/images/circlecross.png')}
                          contentFit="contain"
                          style={{ height: 12, width: 12 }}
                        />
                      </View>
                    </View>
                  </Pressable>
                ))}
              </View>

              <View className="flex-row flex-wrap items-center gap-4">
                {filteredLanguages.map((language) => (
                  <Pressable
                    onPress={() => addOrRemoveLanguage(language)}
                    key={language.id}
                  >
                    <View
                      className="rounded-[16px] border border-primary-50 bg-customborder px-2.5 py-[6px] "
                      key={language.id}
                    >
                      <View className="flex-row items-center gap-1">
                        <Text className="font-inter font-semibold leading-5 text-primary-50 ">
                          {language.name}
                        </Text>
                        <Image
                          source={require('../../../assets/images/plus.png')}
                          contentFit="contain"
                          style={{ height: 12, width: 12 }}
                        />
                      </View>
                    </View>
                  </Pressable>
                ))}
              </View>
              </View>
            </ScrollView>

            <View className="mt-4">
              <Button
                variant="secondary"
                label="Save"
                className="mb-4"
                onPress={saveLanguages}
              />
            </View>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </>
  );
};

export default LanguagesSelector;
