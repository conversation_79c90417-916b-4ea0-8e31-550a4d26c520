import * as React from 'react';
import type { SvgProps } from 'react-native-svg';
import Svg, { ClipP<PERSON>, Defs, G, Path } from 'react-native-svg';

import colors from '../colors';

export const Rate = ({ color = colors.black[500], ...props }: SvgProps) => (
  <Svg width={24} height={24} fill="none" viewBox="0 0 24 24" {...props}>
    <G clipPath="url(#a)">
      <Path
        d="m12.412 17.878 4.725 3c.61.384 1.36-.187 1.181-.89l-1.369-5.382a.816.816 0 0 1 .272-.825l4.238-3.534c.553-.46.271-1.388-.45-1.434l-5.532-.357a.778.778 0 0 1-.684-.506L12.73 2.756a.778.778 0 0 0-1.462 0L9.206 7.95a.778.778 0 0 1-.685.506l-5.531.357c-.722.046-1.003.975-.45 1.434l4.237 3.534a.815.815 0 0 1 .272.825l-1.265 4.988c-.216.843.684 1.528 1.406 1.069l4.397-2.785a.769.769 0 0 1 .825 0v0Z"
        stroke={color}
        strokeWidth={1.219}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </G>
    <Defs>
      <ClipPath id="a">
        <Path fill="#fff" d="M0 0h24v24H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
