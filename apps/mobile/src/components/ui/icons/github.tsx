import * as React from 'react';
import type { SvgProps } from 'react-native-svg';
import Svg, { <PERSON><PERSON><PERSON><PERSON>, Defs, G, Path } from 'react-native-svg';

import colors from '../colors';

export const Github = ({ color = colors.black[500], ...props }: SvgProps) => (
  <Svg width={24} height={24} fill="none" viewBox="0 0 24 24" {...props}>
    <G
      clipPath="url(#github)"
      stroke={color}
      strokeWidth={1.219}
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <Path d="M7.876 22.5a2.242 2.242 0 0 0 2.25-2.25v-4.5M16.125 22.5a2.242 2.242 0 0 1-2.25-2.25v-4.5M14.25 15.75h1.5A2.24 2.24 0 0 1 18 18v.75A2.24 2.24 0 0 0 20.25 21M9.752 15.75h-1.5A2.24 2.24 0 0 0 6.002 18v.75A2.24 2.24 0 0 1 3.752 21" />
      <Path d="M10.485 6a4.875 4.875 0 0 0-4.107-2.25 4.875 4.875 0 0 0-.328 4.19 4.622 4.622 0 0 0-.797 2.56v.75a4.5 4.5 0 0 0 4.5 4.5h4.5a4.5 4.5 0 0 0 4.5-4.5v-.75a4.622 4.622 0 0 0-.797-2.56 4.874 4.874 0 0 0-.328-4.19A4.875 4.875 0 0 0 13.522 6h-3.037Z" />
    </G>
    <Defs>
      <ClipPath id="github">
        <Path fill="#fff" d="M0 0h24v24H0z" />
      </ClipPath>
    </Defs>
  </Svg>
);
