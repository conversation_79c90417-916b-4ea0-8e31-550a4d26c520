import React from 'react';
import type { SvgProps } from 'react-native-svg';
import Svg, { G, Path, Text, TSpan } from 'react-native-svg';

export const Language = ({ ...props }: SvgProps) => (
  <Svg width={100} height={71} {...props}>
    <G fill="none" fillRule="evenodd">
      <Path
        d="M55.684 0H3.319A3.319 3.319 0 0 0 0 3.324V39.4a3.319 3.319 0 0 0 3.32 3.324h10.207V57l14.048-14.276h28.109A3.32 3.32 0 0 0 59 39.4V3.324A3.32 3.32 0 0 0 55.684 0"
        fill="#FC6276"
      />
      <Path
        d="M32.103 23.62l-3.242-11.567-3.394 11.567h6.636zM25.856 6.23a26.079 26.079 0 0 1 1.562-.172A19.683 19.683 0 0 1 29.058 6c.521 0 1.026.016 1.52.058.494.04 1.041.094 1.64.172L41 33.769a21.499 21.499 0 0 1-3.122.231c-.992 0-1.95-.075-2.887-.231l-1.56-5.512h-9.29l-1.6 5.512c-.936.156-1.837.231-2.693.231-.962 0-1.914-.075-2.848-.231l8.856-27.54z"
        fill="#FFF"
      />
      <Path
        d="M96.682 14H44.316A3.32 3.32 0 0 0 41 17.318v36.08a3.321 3.321 0 0 0 3.316 3.323h28.11L86.473 71V56.721h10.208A3.32 3.32 0 0 0 100 53.398v-36.08A3.317 3.317 0 0 0 96.682 14"
        fill="#192332"
      />
      <Text
        // fontFamily="HelveticaNeueLTArabic-Bold, Helvetica Neue LT Arabic"
        fontSize={30}
        fontWeight="bold"
        fill="#FFF"
        transform="translate(0 -4)"
      >
        <TSpan x={59} y={42}>
          ي
        </TSpan>
      </Text>
    </G>
  </Svg>
);
