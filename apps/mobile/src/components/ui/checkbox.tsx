import React, { useCallback } from 'react';
import {
  I18nManager,
  Pressable,
  type PressableProps,
  View,
} from 'react-native';
import Svg, { Path } from 'react-native-svg';
import Animated, {
  useAnimatedStyle,
  withTiming,
  interpolateColor,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

import colors from '@/components/ui/colors';

import { Text } from './text';

const SIZE = 20;
const WIDTH = 50;
const HEIGHT = 28;
const THUMB_HEIGHT = 22;
const THUMB_WIDTH = 22;
const THUMB_OFFSET = 4;

export interface RootProps extends Omit<PressableProps, 'onPress'> {
  onChange: (checked: boolean) => void;
  checked?: boolean;
  className?: string;
  accessibilityLabel: string;
}

export type IconProps = {
  checked: boolean;
};

export const Root = ({
  checked = false,
  children,
  onChange,
  disabled,
  className = '',
  ...props
}: RootProps) => {
  const handleChange = useCallback(() => {
    onChange(!checked);
  }, [onChange, checked]);

  return (
    <Pressable
      onPress={handleChange}
      className={`flex-row items-center ${className} ${
        disabled ? 'opacity-50' : ''
      }`}
      accessibilityState={{ checked }}
      disabled={disabled}
      {...props}
    >
      {children}
    </Pressable>
  );
};

type LabelProps = {
  text: string;
  className?: string;
  testID?: string;
};

const Label = ({ text, testID, className = '' }: LabelProps) => {
  return (
    <Text testID={testID} className={` ${className} pl-2`}>
      {text}
    </Text>
  );
};

export const CheckboxIcon = ({ checked = false }: IconProps) => {
  const animatedValue = useSharedValue(checked ? 1 : 0);

  React.useEffect(() => {
    animatedValue.value = withTiming(checked ? 1 : 0, { duration: 100 });
  }, [checked, animatedValue]);

  const animatedStyle = useAnimatedStyle(() => {
    const backgroundColor = interpolateColor(
      animatedValue.value,
      [0, 1],
      ['transparent', colors.primary[300]]
    );
    const borderColor = interpolateColor(
      animatedValue.value,
      [0, 1],
      [colors.charcoal[400], colors.primary[300]]
    );

    return {
      backgroundColor,
      borderColor,
    };
  });

  const checkmarkStyle = useAnimatedStyle(() => {
    return {
      opacity: animatedValue.value,
    };
  });

  return (
    <Animated.View
      style={[
        {
          height: SIZE,
          width: SIZE,
        },
        animatedStyle,
      ]}
      className="items-center justify-center rounded-[5px] border-2"
    >
      <Animated.View style={checkmarkStyle}>
        <Svg width="24" height="24" viewBox="0 0 24 24" fill="none">
          <Path
            d="m16.726 7-.64.633c-2.207 2.212-3.878 4.047-5.955 6.158l-2.28-1.928-.69-.584L6 12.66l.683.577 2.928 2.477.633.535.591-.584c2.421-2.426 4.148-4.367 6.532-6.756l.633-.64L16.726 7Z"
            fill="#fff"
          />
        </Svg>
      </Animated.View>
    </Animated.View>
  );
};

const CheckboxRoot = ({ checked = false, children, ...props }: RootProps) => {
  return (
    <Root checked={checked} accessibilityRole="checkbox" {...props}>
      {children}
    </Root>
  );
};

const CheckboxBase = ({
  checked = false,
  testID,
  label,

  ...props
}: RootProps & { label?: string }) => {
  return (
    <CheckboxRoot checked={checked} testID={testID} {...props}>
      <CheckboxIcon checked={checked} />
      {label ? (
        <Label
          text={label}
          testID={testID ? `${testID}-label` : undefined}
          className="pr-2"
        />
      ) : null}
    </CheckboxRoot>
  );
};

export const Checkbox = Object.assign(CheckboxBase, {
  Icon: CheckboxIcon,
  Root: CheckboxRoot,
  Label,
});

export const RadioIcon = ({ checked = false }: IconProps) => {
  const animatedValue = useSharedValue(checked ? 1 : 0);

  React.useEffect(() => {
    animatedValue.value = withTiming(checked ? 1 : 0, { duration: 100 });
  }, [checked, animatedValue]);

  const animatedStyle = useAnimatedStyle(() => {
    const borderColor = interpolateColor(
      animatedValue.value,
      [0, 1],
      [colors.charcoal[400], colors.primary[300]]
    );

    return {
      borderColor,
    };
  });

  const dotStyle = useAnimatedStyle(() => {
    return {
      opacity: animatedValue.value,
    };
  });

  return (
    <Animated.View
      style={[
        {
          height: SIZE,
          width: SIZE,
        },
        animatedStyle,
      ]}
      className="items-center justify-center rounded-[20px] border-2 bg-transparent"
    >
      <Animated.View
        style={[
          {
            width: 10,
            height: 10,
            borderRadius: 5,
            backgroundColor: colors.primary[300],
          },
          dotStyle,
        ]}
      />
    </Animated.View>
  );
};

const RadioRoot = ({ checked = false, children, ...props }: RootProps) => {
  return (
    <Root checked={checked} accessibilityRole="radio" {...props}>
      {children}
    </Root>
  );
};

const RadioBase = ({
  checked = false,
  testID,
  label,
  ...props
}: RootProps & { label?: string }) => {
  return (
    <RadioRoot checked={checked} testID={testID} {...props}>
      <RadioIcon checked={checked} />
      {label ? (
        <Label text={label} testID={testID ? `${testID}-label` : undefined} />
      ) : null}
    </RadioRoot>
  );
};

export const Radio = Object.assign(RadioBase, {
  Icon: RadioIcon,
  Root: RadioRoot,
  Label,
});

export const SwitchIcon = ({ checked = false }: IconProps) => {
  const translateX = checked
    ? THUMB_OFFSET
    : WIDTH - THUMB_WIDTH - THUMB_OFFSET;

  const backgroundColor = checked ? colors.primary[300] : colors.charcoal[400];
  const animatedTranslateX = useSharedValue(
    I18nManager.isRTL ? translateX : -translateX
  );

  React.useEffect(() => {
    const newTranslateX = checked
      ? THUMB_OFFSET
      : WIDTH - THUMB_WIDTH - THUMB_OFFSET;
    animatedTranslateX.value = withSpring(
      I18nManager.isRTL ? newTranslateX : -newTranslateX,
      { overshootClamping: true }
    );
  }, [checked, animatedTranslateX]);

  const thumbStyle = useAnimatedStyle(() => {
    return {
      transform: [{ translateX: animatedTranslateX.value }],
    };
  });

  return (
    <View className="w-[50px] justify-center">
      <View className="overflow-hidden rounded-full">
        <View
          style={{
            width: WIDTH,
            height: HEIGHT,
            backgroundColor,
          }}
        />
      </View>
      <Animated.View
        style={[
          {
            height: THUMB_HEIGHT,
            width: THUMB_WIDTH,
            position: 'absolute',
            backgroundColor: 'white',
            borderRadius: 13,
            right: 0,
          },
          thumbStyle,
        ]}
      />
    </View>
  );
};

const SwitchRoot = ({ checked = false, children, ...props }: RootProps) => {
  return (
    <Root checked={checked} accessibilityRole="switch" {...props}>
      {children}
    </Root>
  );
};

const SwitchBase = ({
  checked = false,
  testID,
  label,
  ...props
}: RootProps & { label?: string }) => {
  return (
    <SwitchRoot checked={checked} testID={testID} {...props}>
      <SwitchIcon checked={checked} />
      {label ? (
        <Label text={label} testID={testID ? `${testID}-label` : undefined} />
      ) : null}
    </SwitchRoot>
  );
};

export const Switch = Object.assign(SwitchBase, {
  Icon: SwitchIcon,
  Root: SwitchRoot,
  Label,
});
