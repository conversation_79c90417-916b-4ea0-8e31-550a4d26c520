import React from 'react';
import { Pressable, Text, View } from 'react-native';
import type { VariantProps } from 'tailwind-variants';
import { tv } from 'tailwind-variants';

import { cn } from '@/lib/utils';

const selectionGroup = tv({
  slots: {
    container:
      'mt-2.5 rounded-[12px] border border-borderdark px-5 py-[14px] dark:bg-bgtextInput',
    optionContainer: 'px-3 py-2',
    optionText: 'text-inter leading-5 text-[14px] font-normal',
    selectedOption: 'bg-stromegray rounded-xl border border-borderdark',
    selectedText: 'dark:text-black-0',
    unselectedText: 'text-black-300',
  },
  variants: {
    layout: {
      horizontal: {
        container: 'flex-row justify-between',
      },
      vertical: {
        container: 'flex-col gap-2',
      },
    },
  },
  defaultVariants: {
    layout: 'horizontal',
  },
});

type SelectionGroupVariants = VariantProps<typeof selectionGroup>;

export interface SelectionOption {
  value: string;
  label: string;
  disabled?: boolean;
}

interface SelectionGroupProps extends SelectionGroupVariants {
  options: SelectionOption[];
  value?: string | string[];
  onChange: (value: string | string[]) => void;
  multiple?: boolean;
  className?: string;
  error?: string;
}

export const SelectionGroup = React.forwardRef<View, SelectionGroupProps>(
  (
    {
      options,
      value,
      onChange,
      multiple = false,
      layout = 'horizontal',
      className = '',
      error,
    },
    ref
  ) => {
    const styles = React.useMemo(() => selectionGroup({ layout }), [layout]);

    const handleOptionPress = (optionValue: string) => {
      if (multiple) {
        const currentValues = Array.isArray(value) ? value : [];
        const newValues = currentValues.includes(optionValue)
          ? currentValues.filter((v) => v !== optionValue)
          : [...currentValues, optionValue];
        onChange(newValues);
      } else {
        onChange(optionValue);
      }
    };

    const isSelected = (optionValue: string) => {
      if (multiple) {
        return Array.isArray(value) && value.includes(optionValue);
      }
      return value === optionValue;
    };

    return (
      <View ref={ref}>
        <View className={styles.container({ className })}>
          {options.map((option) => (
            <Pressable
              key={option.value}
              onPress={() =>
                !option.disabled && handleOptionPress(option.value)
              }
              disabled={option.disabled}
            >
              <View
                className={cn(
                  styles.optionContainer(),
                  isSelected(option.value) && styles.selectedOption()
                )}
              >
                <Text
                  className={cn(
                    styles.optionText(),
                    isSelected(option.value)
                      ? styles.selectedText()
                      : styles.unselectedText(),
                    option.disabled && 'opacity-50'
                  )}
                >
                  {option.label}
                </Text>
              </View>
            </Pressable>
          ))}
        </View>
        {error && <Text className="mt-1 text-sm text-red-500">{error}</Text>}
      </View>
    );
  }
);

SelectionGroup.displayName = 'SelectionGroup';
