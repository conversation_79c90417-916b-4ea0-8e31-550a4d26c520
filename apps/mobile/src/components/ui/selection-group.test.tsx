import React from 'react';
import { render, fireEvent } from '@testing-library/react-native';

import { SelectionGroup } from './selection-group';

describe('SelectionGroup', () => {
  const mockOptions = [
    { value: 'option1', label: 'Option 1' },
    { value: 'option2', label: 'Option 2' },
    { value: 'option3', label: 'Option 3' },
  ];

  it('renders all options correctly', () => {
    const { getByText } = render(
      <SelectionGroup
        options={mockOptions}
        value="option1"
        onChange={() => {}}
      />
    );

    expect(getByText('Option 1')).toBeTruthy();
    expect(getByText('Option 2')).toBeTruthy();
    expect(getByText('Option 3')).toBeTruthy();
  });

  it('calls onChange when an option is pressed', () => {
    const mockOnChange = jest.fn();
    const { getByText } = render(
      <SelectionGroup
        options={mockOptions}
        value="option1"
        onChange={mockOnChange}
      />
    );

    fireEvent.press(getByText('Option 2'));
    expect(mockOnChange).toHaveBeenCalledWith('option2');
  });

  it('handles multiple selection correctly', () => {
    const mockOnChange = jest.fn();
    const { getByText } = render(
      <SelectionGroup
        options={mockOptions}
        value={['option1']}
        onChange={mockOnChange}
        multiple={true}
      />
    );

    fireEvent.press(getByText('Option 2'));
    expect(mockOnChange).toHaveBeenCalledWith(['option1', 'option2']);

    fireEvent.press(getByText('Option 1'));
    expect(mockOnChange).toHaveBeenCalledWith([]);
  });

  it('displays error message when provided', () => {
    const errorMessage = 'This field is required';
    const { getByText } = render(
      <SelectionGroup
        options={mockOptions}
        value="option1"
        onChange={() => {}}
        error={errorMessage}
      />
    );

    expect(getByText(errorMessage)).toBeTruthy();
  });

  it('handles disabled options correctly', () => {
    const mockOnChange = jest.fn();
    const optionsWithDisabled = [
      { value: 'option1', label: 'Option 1' },
      { value: 'option2', label: 'Option 2', disabled: true },
      { value: 'option3', label: 'Option 3' },
    ];

    const { getByText } = render(
      <SelectionGroup
        options={optionsWithDisabled}
        value="option1"
        onChange={mockOnChange}
      />
    );

    fireEvent.press(getByText('Option 2'));
    expect(mockOnChange).not.toHaveBeenCalled();
  });

  it('applies correct layout styles', () => {
    const { getByText } = render(
      <SelectionGroup
        options={mockOptions}
        value="option1"
        onChange={() => {}}
        layout="vertical"
      />
    );

    expect(getByText('Option 1')).toBeTruthy();
    expect(getByText('Option 2')).toBeTruthy();
    expect(getByText('Option 3')).toBeTruthy();
  });
});
