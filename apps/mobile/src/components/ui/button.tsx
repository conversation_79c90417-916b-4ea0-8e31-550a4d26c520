import React, { useState } from 'react';
import type { PressableProps, View } from 'react-native';
import {
  ActivityIndicator,
  Pressable,
  Text,
  View as RNView,
} from 'react-native';
import type { VariantProps } from 'tailwind-variants';
import { tv } from 'tailwind-variants';

const button = tv({
  slots: {
    container:
      'flex flex-row items-center justify-center rounded-xl px-10 py-3.5 ',
    label: 'font-inter  ',
    indicator: 'text-black-0',
    icon: 'mr-2',
  },

  variants: {
    variant: {
      default: {
        container: 'bg-black dark:bg-white',
        label: 'dark:text-black text-black-0',
        indicator: 'dark:text-black text-white',
      },

      darkblue: {
        container: 'bg-black dark:bg-blue',
        label: 'text-black-0 dark:text-black-50',
        indicator: 'text-white dark:text-black-50',
      },

      secondary: {
        container: 'bg-blue dark:bg-primary-50 ',
        label: 'text-sm font-bold text-black-0 dark:text-blue',
        indicator: 'text-white',
      },

      outline: {
        container: 'border border-neutral-400',
        label: 'text-black-950 dark:text-black-0',
        indicator: 'text-black dark:text-neutral-100',
      },

      destructive: {
        container: 'bg-red-600',
        label: 'text-white',
        indicator: 'text-white',
      },

      ghost: {
        container: 'bg-transparent',
        label: 'text-black underline dark:text-white',
        indicator: 'text-black dark:text-white',
      },

      link: {
        container: 'bg-transparent',
        label: 'text-black',
        indicator: 'text-black',
      },
    },
    size: {
      default: {
        container: 'py-[14px]',
        label: 'text-base',
      },
      lg: {
        container: 'h-12 px-8',
        label: 'text-xl',
      },
      sm: {
        container: 'h-8 px-3',
        label: 'text-sm',
        indicator: 'h-2',
      },
      icon: { container: 'size-9' },
    },
    disabled: {
      true: {
        container: 'bg-neutral-300 dark:bg-neutral-300',
        label: 'text-neutral-600 dark:text-neutral-600',
        indicator: 'text-neutral-400 dark:text-neutral-400',
      },
    },
    fullWidth: {
      true: {
        container: 'w-full',
      },
      false: {
        container: 'self-center',
      },
    },
  },

  defaultVariants: {
    variant: 'default',
    disabled: false,
    fullWidth: true,
    size: 'default',
  },
});

type ButtonVariants = VariantProps<typeof button>;
interface Props extends ButtonVariants, Omit<PressableProps, 'disabled'> {
  label?: string;
  loading?: boolean;
  className?: string;
  textClassName?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onPress: () => void;
}

export const Button = React.forwardRef<View, Props>(
  (
    {
      label: text,
      loading = false,
      fullWidth = true,
      variant = 'default',
      disabled = false,
      size = 'default',
      className = '',
      textClassName = '',
      onPress,
      leftIcon,
      rightIcon,
      ...props
    },
    ref
  ) => {
    const styles = React.useMemo(
      () => button({ variant, disabled, size, fullWidth }),
      [variant, disabled, size, fullWidth]
    );

    const [isPressed, setIsPressed] = useState(false);

    const handlePressIn = () => {
      if (!disabled && !loading) {
        setIsPressed(true);
      }
    };

    const handlePressOut = () => {
      setIsPressed(false);
    };

    const containerStyle = isPressed || loading ? { opacity: 0.6 } : {};

    return (
      <Pressable
        onPress={onPress}
        disabled={disabled || loading}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        className={styles.container({ className })}
        style={containerStyle}
        {...props}
        ref={ref}
      >
        <RNView className="flex-row items-center gap-2">
          {loading && (
            <ActivityIndicator
              size="small"
              className={`${variant === 'secondary' ? 'text-[#0000FF]' : 'text-[#FFFFFF]'}`}
            />
          )}
          {leftIcon && <RNView className={styles.icon()}>{leftIcon}</RNView>}
          <Text className={styles.label({ className: textClassName })}>
            {text}
          </Text>
          {!loading && rightIcon && (
            <RNView className={styles.icon()}>{rightIcon}</RNView>
          )}
        </RNView>
      </Pressable>
    );
  }
);
