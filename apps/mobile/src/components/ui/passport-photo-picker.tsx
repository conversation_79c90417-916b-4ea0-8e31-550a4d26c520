import { Env } from '@env';
import * as ImagePicker from 'expo-image-picker';
import React from 'react';
import { ID } from 'react-native-appwrite';
import { showMessage } from 'react-native-flash-message';

import InputText from '@/components/input-txt';
import { storage } from '@/lib/appwrite';
import { tryCatch } from '@/lib/try-catch';

interface PassportPhotoPickerProps {
  value: string | null;
  onImageSelected: (url: string) => void;
  onImageRemoved: () => void;
  loading?: boolean;
  onLoadingChange?: (loading: boolean) => void;
  documentId?: string;
  disabled?: boolean;
}

const PassportPhotoPicker: React.FC<PassportPhotoPickerProps> = ({
  value,
  onImageSelected,
  onImageRemoved,
  loading = false,
  onLoadingChange,
  documentId = 'default',
  disabled = false,
}) => {
  const pickPassportImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (result.canceled) {
      return;
    }

    const imageAsset = result.assets[0];
    onLoadingChange?.(true);

    try {
      const file = {
        name: `passport-${documentId}.jpg`,
        type: imageAsset.mimeType ?? 'image/jpeg',
        size: imageAsset.fileSize ?? 0,
        uri: imageAsset.uri,
      };

      const uploadedFile = await storage.createFile(
        Env.STORAGE_BUCKET_ID,
        ID.unique(),
        file
      );

      const url = storage.getFileView(Env.STORAGE_BUCKET_ID, uploadedFile.$id);
      onImageSelected(url.href);
    } catch (err) {
      console.log('error in uploading passport image', err);
      showMessage({
        message: 'Error in uploading passport image',
        type: 'danger',
      });
    } finally {
      onLoadingChange?.(false);
    }
  };

  const handleRemoveImage = () => {
    if (value) {
      try {
        const results = value.split('/');
        const deleteFileId = value.split('/')[results.length - 2];
        tryCatch(storage.deleteFile(Env.STORAGE_BUCKET_ID, deleteFileId));
      } catch (err) {
        console.log('Error deleting file:', err);
      }
    }
    onImageRemoved();
  };

  return (
    <InputText
      onPress={pickPassportImage}
      placeholder={value ? value : 'Choose a Picture'}
      iconSourceFirst={require('../../../assets/images/upload.png')}
      iconStyleFirst={{ height: 13.923, width: 15 }}
      iconStyleSecond={{ height: 18, width: 15 }}
      disabled={disabled || loading}
    />
  );
};

export default PassportPhotoPicker; 