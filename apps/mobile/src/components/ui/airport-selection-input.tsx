import React from 'react';
import { FlatList, Image, Pressable, Text, View } from 'react-native';
import { useDebouncedCallback } from 'use-debounce';
import { Controller, useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import FontAwesome from '@expo/vector-icons/FontAwesome';


import InputText from '@/components/input-txt';
import InputLabelled from '@/components/input-labelled';
import InputErrorMsg from '@/components/input-error-msg';
import { Button } from '@/components/ui/button';
import { Modal, useModal } from '@/components/ui/modal';
import { cn } from '@/lib/utils';
import {
  type Airport,
  useAirportStore,
  type CreateAirportData,
} from '@/store/airport-store';
import { airportSchema } from '@/form-schema/form-schema';
import { showMessage } from 'react-native-flash-message';
import { BottomSheetFlashList } from '@gorhom/bottom-sheet';
import { useQuery } from '@tanstack/react-query';
import { trpc } from '@/lib/api';

// Consider creating proper interfaces
interface AirportSelectionInputProps {
  index: number;
  airport?: Flight;
  setAirport: (flight: Flight) => void;
  removeAirport: (index: number) => void;
  totalFlights: number;
  placeholder?: string;
}

interface Flight {
  index: number;
  id: string;
  name: string;
}

const MODAL_SNAP_POINTS = ['95%'];
const SEARCH_PLACEHOLDER = 'Search By Airport Name / IATA Code / City';
const ICON_DIMENSIONS = { height: 16, width: 16 };

const AirportSelectionInput = ({
  index,
  airport,
  setAirport,
  removeAirport,
  totalFlights,
  placeholder,
}: AirportSelectionInputProps) => {
  const { ref, present, dismiss } = useModal();
  const {
    ref: createModalRef,
    present: presentCreate,
    dismiss: dismissCreate,
  } = useModal();
  const { data: airports, refetch: refetchAirports, isLoading } = useQuery(trpc.airports.getAll.queryOptions());
  const [filterAirport, setFilterAirport] = React.useState('');

  const {
    control: createControl,
    handleSubmit: handleCreateSubmit,
    reset: resetCreateForm,
    formState: { errors: createErrors },
  } = useForm<z.infer<typeof airportSchema>>({
    resolver: zodResolver(airportSchema),
    defaultValues: {
      name: '',
      shortCode: '',
      city: '',
      state: '',
      country: '',
      icao: '',
      type: 'airport',
      timezone: 'UTC',
    },
  });

  const debouncedSetFilter = useDebouncedCallback((value: string) => {
    setFilterAirport(value);
  }, 300);

  // Add memoization for filtered airports
  const filteredAirports = React.useMemo(
    () =>
      filterAirport
        ? airports?.filter(
            (a) =>
              a.name.toLowerCase().includes(filterAirport.toLowerCase()) ||
              a.shortCode.toLowerCase().includes(filterAirport.toLowerCase()) ||
              a.airportLocation?.toLowerCase().includes(filterAirport.toLowerCase())
          )
        : airports,
    [airports, filterAirport]
  );

  const showCreateOption = filteredAirports?.length === 0;

  const handleAirportSelect = (item: Airport) => {
    const newSelectedAirport = {
      index: index,
      id: item.id,
      name: item.name,
    };

    setAirport(newSelectedAirport);
    dismiss();
  };

  // const handleCreateAirport = handleCreateSubmit(
  //   async (data: z.infer<typeof airportSchema>) => {
  //     try {
  //       const newAirport = await createAirport(data as CreateAirportData);
  //       if (newAirport) {
  //         const newSelectedAirport = {
  //           index: index,
  //           id: newAirport.id,
  //           name: newAirport.name,
  //         };
  //         setAirport(newSelectedAirport);
  //         dismissCreate();
  //         dismiss();
  //         resetCreateForm();
  //         showMessage({
  //           message: 'Airport created successfully',
  //           type: 'success',
  //         });
  //       }
  //     } catch (error) {
  //       showMessage({
  //         message: 'Failed to create airport',
  //         type: 'danger',
  //       });
  //     }
  //   }
  // );

  // Memoize the airport item component
  const AirportItem = React.memo(
    ({
      item,
      onSelect,
    }: {
      item: Airport;
      onSelect: (item: Airport) => void;
    }) => (
      <Pressable onPress={() => onSelect(item)}>
        <Text className="p-3 text-white">
          {item.name} ({item.shortCode}) - {item.airportLocation}
        </Text>
      </Pressable>
    )
  );

  return (
    <View
      className={cn(
        'flex-1 flex-row items-center justify-between border bg-bgtextInput px-5  border-customborder border-b-0',
        index === 0 && 'rounded-t-xl',
        index === totalFlights - 1 && 'rounded-b-xl border-b-1'
      )}
    >
      <Pressable
        onPress={() => {
          present();
        }}
        className="flex-1"
      >
        <View
          key={airport?.id ?? index}
          // style={{ backgroundColor: 'red' }}
          className="w-full flex-1 flex-row py-[14px]"
        >
          <View className="flex-1 flex-row items-center gap-2">
            <Image
              source={require('../../../assets/images/location.png')}
              resizeMode="contain"
              style={{ width: 12, height: 15 }}
            />
            <Text className="flex-1 text-[14px] font-normal leading-5 text-black-300">
              {airport?.name ?? placeholder ?? 'Select Airport'}
            </Text>
          </View>
          {index !== 0 && (index !== totalFlights - 1 || totalFlights > 2) && (
            <Pressable
              onPress={() => {
                removeAirport(airport?.index ?? index);
              }}
              className="absolute right-0 top-3"
            >
              <View className="">
                <Image
                  source={require('../../../assets/images/circlecross.png')}
                  resizeMode="contain"
                  style={{ width: 20, height: 20 }}
                />
              </View>
            </Pressable>
          )}
        </View>
      </Pressable>

      {/* Airport Selection Modal */}
      <Modal
        ref={ref}
        snapPoints={MODAL_SNAP_POINTS}
        onDismiss={dismiss}
        backgroundStyle={{ backgroundColor: '#020717' }}
      >
        <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
          <View className="flex-1 justify-between">
            <View className="mb-3 mt-6 flex-row items-center">
              <InputText
                onPress={() => {}}
                placeholder={SEARCH_PLACEHOLDER}
                className="flex-1 dark:bg-blue"
                iconSourceFirst={require('../../../assets/images/search-icon.png')}
                iconStyleFirst={ICON_DIMENSIONS}
                iconStyleSecond={ICON_DIMENSIONS}
                onChangeText={debouncedSetFilter}
              />
              <Pressable onPress={() => refetchAirports()}>
                <View className="flex-row items-center justify-center ml-4">
                  <FontAwesome name="refresh" size={24} color="white" />
                </View>
              </Pressable>
            </View>
            {isLoading ? (
              <View className="flex-1 items-center justify-center">
                <Text className="text-white">Loading airports...</Text>
              </View>
            ) : (
              <>
                <BottomSheetFlashList
                estimatedItemSize={40}
                  data={filteredAirports}
                  renderItem={({ item }) => (
                    <AirportItem item={item} onSelect={handleAirportSelect} />
                  )}
                  keyExtractor={(item) => item.id}
                />
                {/* {showCreateOption && (
                  <Pressable
                    onPress={() => {
                      dismiss();
                      presentCreate();
                    }}
                    className="mt-4 rounded-lg border border-blue bg-blue p-4 dark:bg-blue"
                  >
                    <Text className="text-center text-blue-600 dark:text-blue-300">
                      + Add "{filterAirport}" as new airport
                    </Text>
                  </Pressable>
                )} */}
              </>
            )}
            <View className="mb-4">
              <Button variant="secondary" label="Cancel" onPress={dismiss} />
            </View>
          </View>
        </View>
      </Modal>

      {/* Create Airport Modal */}
      <Modal
        ref={createModalRef}
        snapPoints={['90%']}
        onDismiss={dismissCreate}
        backgroundStyle={{ backgroundColor: '#020717' }}
      >
        <View className="flex-1 bg-black-0 px-5 dark:bg-primary-950">
          <View className="mt-6 mb-4">
            <Text className="font-PoppinsBold text-[20px] font-bold leading-[30px] text-black-950 dark:text-black-0">
              Add New Airport
            </Text>
            <Text className="font-inter text-[14px] font-medium leading-5 text-black-950 dark:text-black-300">
              Add airport details to create a new airport
            </Text>
          </View>

          <View className="flex-1 gap-4">
            <InputLabelled label="Airport Name">
              <Controller
                name="name"
                control={createControl}
                render={({ field: { value, onChange }, fieldState }) => (
                  <>
                    <InputText
                      placeholder="Enter airport name"
                      value={value}
                      onChangeText={onChange}
                    />
                    {fieldState.error?.message && (
                      <InputErrorMsg message={fieldState.error.message} />
                    )}
                  </>
                )}
              />
            </InputLabelled>

            <InputLabelled label="Airport Code (IATA)">
              <Controller
                name="shortCode"
                control={createControl}
                render={({ field: { value, onChange }, fieldState }) => (
                  <>
                    <InputText
                      placeholder="e.g., JFK, LAX"
                      value={value}
                      onChangeText={(text) => onChange(text.toUpperCase())}
                    />
                    {fieldState.error?.message && (
                      <InputErrorMsg message={fieldState.error.message} />
                    )}
                  </>
                )}
              />
            </InputLabelled>

            <InputLabelled label="City">
              <Controller
                name="city"
                control={createControl}
                render={({ field: { value, onChange }, fieldState }) => (
                  <>
                    <InputText
                      placeholder="Enter city name"
                      value={value}
                      onChangeText={onChange}
                    />
                    {fieldState.error?.message && (
                      <InputErrorMsg message={fieldState.error.message} />
                    )}
                  </>
                )}
              />
            </InputLabelled>

            <InputLabelled label="State/Province">
              <Controller
                name="state"
                control={createControl}
                render={({ field: { value, onChange }, fieldState }) => (
                  <>
                    <InputText
                      placeholder="Enter state or province"
                      value={value}
                      onChangeText={onChange}
                    />
                    {fieldState.error?.message && (
                      <InputErrorMsg message={fieldState.error.message} />
                    )}
                  </>
                )}
              />
            </InputLabelled>

            <InputLabelled label="Country">
              <Controller
                name="country"
                control={createControl}
                render={({ field: { value, onChange }, fieldState }) => (
                  <>
                    <InputText
                      placeholder="Enter country name"
                      value={value}
                      onChangeText={onChange}
                    />
                    {fieldState.error?.message && (
                      <InputErrorMsg message={fieldState.error.message} />
                    )}
                  </>
                )}
              />
            </InputLabelled>

            <InputLabelled label="ICAO Code (Optional)">
              <Controller
                name="icao"
                control={createControl}
                render={({ field: { value, onChange }, fieldState }) => (
                  <InputText
                    placeholder="e.g., KJFK, KLAX"
                    value={value}
                    onChangeText={(text) => onChange(text.toUpperCase())}
                  />
                )}
              />
            </InputLabelled>
          </View>

          <View className="mb-5 flex-row items-center justify-between">
            <Button
              variant="outline"
              label="Cancel"
              onPress={() => {
                dismissCreate();
                resetCreateForm();
              }}
            />
            {/* <Button
              variant="secondary"
              label="Create Airport"
              onPress={handleCreateAirport}
            /> */}
          </View>
        </View>
      </Modal>
    </View>
  );
};

export default AirportSelectionInput;
