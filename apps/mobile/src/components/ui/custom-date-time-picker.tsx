import React, { useState } from 'react';
import { Controller } from 'react-hook-form';
import { Platform, Pressable } from 'react-native';
import InputErrorMsg from '../input-error-msg';
import InputText from '../input-txt';
import DateTimePicker, {
  type DateTimePickerEvent,
} from '@react-native-community/datetimepicker';
import { format } from 'date-fns';

const CustomDateTimePicker = ({
  value,
  mode,
  onChange,
}: {
  value: Date;
  mode: 'date' | 'time';
  onChange: (date: DateTimePickerEvent, selectedTime: Date | undefined) => void;
}) => {
  const [showPicker, setShowPicker] = useState(false);

  const togglePicker = () => {
    setShowPicker(!showPicker);
  };

  return (
    <>
      {Platform.OS === 'ios' ? (
        <Pressable
          onPress={() => setShowPicker(true)}
          className="mt-2 items-start rounded-xl border border-customborder dark:bg-bgtextInput "
        >
          <DateTimePicker
            value={value}
            mode={mode}
            display="default"
            onChange={onChange}
            minimumDate={mode === 'date' ? new Date() : undefined}
          />
        </Pressable>
      ) : (
        <>
          <InputText
            editable={false}
            onPress={togglePicker}
            value={
              mode === 'date'
                ? format(value, 'dd/MM/yyyy')
                : format(value, 'hh:mm a')
            }
            // onChangeText={(value) => {
            //   onChange(value);
            // }}
            placeholder={mode === 'date' ? 'Select Date' : 'Select Time'}
            iconSourceFirst={
              mode === 'date'
                ? require('../../../assets/images/calender.png')
                : require('../../../assets/images/watch.png')
            }
            iconStyleFirst={{ height: 15, width: 15 }}
            className="min-w-40"
          />
          {showPicker && (
            <DateTimePicker
              value={value}
              mode={mode}
              display="default"
              onChange={(event, selectedDate) => {
                onChange(event, selectedDate);
                setShowPicker(false);
              }}
              minimumDate={mode === 'date' ? new Date() : undefined}
            />
          )}
        </>
      )}
    </>
  );
};

export default CustomDateTimePicker;
