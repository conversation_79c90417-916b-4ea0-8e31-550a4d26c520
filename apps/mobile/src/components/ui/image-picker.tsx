import { Env } from '@env';
import { Image } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import React, { useState } from 'react';
import { Dimensions, Pressable, Text, View } from 'react-native';
import { ID, Permission, Role } from 'react-native-appwrite';
import { showMessage } from 'react-native-flash-message';

import { storage } from '@/lib/appwrite';
import { tryCatch } from '@/lib/try-catch';

const { height, width } = Dimensions.get('screen');

interface ImagePickerProps {
  image: string | null;
  onImageSelected: (url: string) => void;
  onImageRemoved: () => void;
  loading?: boolean;
  onLoadingChange?: (loading: boolean) => void;
  filePrefix?: string;
  documentId?: string;
}

const ImagePickerComponent: React.FC<ImagePickerProps> = ({
  image,
  onImageSelected,
  onImageRemoved,
  loading = false,
  onLoadingChange,
  filePrefix = 'image',
  documentId = 'default',
}) => {
  const pickImage = async () => {
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (result.canceled) {
      return;
    }

    const imageAsset = result.assets[0];
    onLoadingChange?.(true);

    try {
      const file = {
        name: `${filePrefix}-${documentId}.jpg`,
        type: imageAsset.mimeType ?? 'image/jpeg',
        size: imageAsset.fileSize ?? 0,
        uri: imageAsset.uri,
      };

      const uploadedFile = await storage.createFile(
        Env.STORAGE_BUCKET_ID,
        ID.unique(),
        file,
        [
          Permission.read(Role.any()),
        ]
      );

      const url = storage.getFileView(Env.STORAGE_BUCKET_ID, uploadedFile.$id);
      onImageSelected(url.href);
    } catch (err) {
      console.log(`error in uploading ${filePrefix} image`, err);
      showMessage({
        message: `Error in uploading ${filePrefix} image`,
        type: 'danger',
      });
    } finally {
      onLoadingChange?.(false);
    }
  };

  const handleRemoveImage = () => {
    if (image) {
      try {
        const results = image.split('/');
        const deleteFileId = image.split('/')[results.length - 2];
        tryCatch(storage.deleteFile(Env.STORAGE_BUCKET_ID, deleteFileId));
      } catch (err) {
        console.log('Error deleting file:', err);
      }
    }
    onImageRemoved();
  };

  return (
    <View className="flex-row items-center">
      {!image && (
        <Pressable
          onPress={pickImage}
          disabled={loading}
          className="mr-8 flex-row items-center justify-center rounded-xl border-2 border-borderdark"
          style={{ height: height * 0.085, width: width * 0.1836 }}
        >
          <Text className="text-xl text-primary-200">+</Text>
          <Image
            source={require('../../../assets/images/Profilee.png')}
            contentFit="contain"
            className="size-6 p-1"
          />
        </Pressable>
      )}

      {image && (
        <View className="flex-row items-center">
          <Image
            source={{ uri: image }}
            style={{ height: 80, width: 80, borderRadius: 12 }}
          />
          <Pressable
            onPress={handleRemoveImage}
            disabled={loading}
            className="absolute right-0 top-0 rounded-full bg-white p-2"
          >
            <Image
              source={require('../../../assets/images/delete.png')}
              className="size-4"
            />
          </Pressable>
        </View>
      )}
    </View>
  );
};

export default ImagePickerComponent; 