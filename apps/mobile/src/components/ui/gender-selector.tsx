import React from 'react';
import { Pressable, Text, View } from 'react-native';

import { cn } from '@/lib';
import { SelectionGroup } from './selection-group';

const GenderSelector = ({
  value,
  onChange,
}: {
  value: string;
  onChange: (value: string) => void;
}) => {
  return (
    <SelectionGroup
      options={[
        { value: 'MALE', label: 'Male' },
        { value: 'FEMALE', label: 'Female' },
        { value: 'OTHERS', label: 'Other' },
      ]}
      value={value}
      onChange={(value) => {
        if (typeof value === 'string') {
          onChange(value);
        } else {
          value[0] && onChange(value[0]);
        }
      }}
    />
  );
};

export default GenderSelector;
