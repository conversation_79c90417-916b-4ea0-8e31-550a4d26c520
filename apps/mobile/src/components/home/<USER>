import { FlashList } from '@shopify/flash-list';
import { useCallback, useState } from 'react';
import { View, ScrollView, RefreshControl, ActivityIndicator, Text } from 'react-native';
import { Image } from 'expo-image';
import { Button } from '../ui/button';
import { router, useFocusEffect } from 'expo-router';
import { Query } from 'react-native-appwrite';
import { databases } from '@/lib/appwrite';
import { Env } from '@env';
import BookingsCardNoCompanion from '../bookings-card-no-companion';
import CompanionBookingsCard from '../companion-booking-card';
import { useQuery } from '@tanstack/react-query';
import { trpc } from '@/lib/api';

const images = [
  require('../../../assets/images/banner1.png'),
  require('../../../assets/images/banner2.png'),
  require('../../../assets/images/banner3.png'),
  require('../../../assets/images/banner1.png'),
  require('../../../assets/images/banner3.png'),
];

export default function CompanionHome() {
  const [currentPage, setCurrentPage] = useState(0);
  const { data: bookings, isLoading: isLoadingBookings, refetch: refetchBookings } = useQuery(trpc.bookingTravelers.getAllByUser.queryOptions());
  const handleScroll = (event: {
    nativeEvent: { contentOffset: { x: any } };
  }) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const pageWidth = 303 + 8; // Image width + marginHorizontal
    const newPage = Math.floor((contentOffsetX + pageWidth / 2) / pageWidth);
    setCurrentPage(newPage);
  };

  const renderItem = ({ item }: any) => (
    <View className='w-full mb-5'>
      <Image
        source={item}
        style={{ aspectRatio: 303/114.289 }}
        contentFit="contain"
        className='m-auto'
      />
    </View>
  );

  useFocusEffect(
    useCallback(() => {
      refetchBookings();
    }, [])
  );

  if (isLoadingBookings) {
    return <ActivityIndicator />;
  }

  if (!bookings) {
    return <Text>No bookings found</Text>;
  }

  console.log('bookings', bookings);


  return (
    <ScrollView
      className="mb-20 flex-1 bg-black-0"
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={isLoadingBookings}
          onRefresh={refetchBookings}
        />
      }
    >
      <View className="flex-1 bg-black-0">
        <View className="flex-1 gap-6 rounded-t-[24px] bg-primary-950 px-5 py-6">
          <View className="flex flex-col gap-2">
            {bookings.map((booking) => (
              <CompanionBookingsCard
                bookingId={booking.id}
                key={booking.id}
                detailed={false}
                onPress={() => {
                  router.push(`/companion/booking-details/${booking.id}`);
                }}
              />
            ))}
          </View>
          <View>
            <Button
              variant="secondary"
              label="Find Traveller"
              onPress={() => {
                router.push('/form/ready-as-companion/companion-details');
              }}
            />
          </View>
          <View className="gap-2.5">
            <FlashList
              data={images}
              estimatedItemSize={284}
              keyExtractor={(item, index) => index.toString()}
              showsHorizontalScrollIndicator={false}
              pagingEnabled
              onScroll={handleScroll}
              renderItem={renderItem}
            />
          </View>
        </View>
      </View>
    </ScrollView>
  );
}
