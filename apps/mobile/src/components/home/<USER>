import { useCallback, useState } from 'react';
import {
  RefreshControl,
  ScrollView,
  View,
  Text,
  ActivityIndicator,
} from 'react-native';
import { Image } from 'expo-image';
import { router, useFocusEffect } from 'expo-router';
import BookingsCardNoCompanion from '../bookings-card-no-companion';
import { Button } from '../ui/button';
import { FlashList } from '@shopify/flash-list';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { trpc } from '@/lib/api';
import { useQuery } from '@tanstack/react-query';

const images = [
  require('../../../assets/images/banner1.png'),
  require('../../../assets/images/banner2.png'),
  require('../../../assets/images/banner3.png'),
  require('../../../assets/images/banner1.png'),
  require('../../../assets/images/banner3.png'),
];

const zodValidation = z.object({
  flightNumber: z.string({ required_error: 'Required' }).min(1, 'Required'),
  flightDate: z.string({
    required_error: 'FlightDate required',
    invalid_type_error: 'Invalid date format',
  }),
  flightTime: z
    .string({ required_error: 'FlightTime required' })
    .min(1, 'Required'),
});

export default function TravellerHome() {
  const [currentPage, setCurrentPage] = useState(0);
  const [loading, setLoading] = useState(false);
  // const [date, setDate] = useState(new Date());
  // const [time, setTime] = useState(new Date());
  // const [showDatePicker, setShowDatePicker] = useState(false);
  // const [showTimePicker, setShowTimePicker] = useState(false);

  const { control } = useForm<z.infer<typeof zodValidation>>({
    resolver: zodResolver(zodValidation),
    defaultValues: {
      flightNumber: '',
      flightDate: '',
      flightTime: '',
    },
  });

  const handleScroll = (event: {
    nativeEvent: { contentOffset: { x: any } };
  }) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const pageWidth = 303 + 8; // Image width + marginHorizontal
    const newPage = Math.floor((contentOffsetX + pageWidth / 2) / pageWidth);
    setCurrentPage(newPage);
  };

  const renderItem = ({ item }: any) => (
    <View className='w-full mb-5'>
      <Image
        source={item}
        style={{ aspectRatio: 303/114.289 }}
        contentFit="contain"
        className='m-auto'
      />
    </View>
  );

  const { data: bookings, refetch, isLoading, isError } = useQuery(trpc.bookingCompanions.getAllByUser.queryOptions());

  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [])
  );

  if (isLoading) {
    return <ActivityIndicator />;
  }

  if (isError || !bookings) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-red-500">Error fetching bookings</Text>
        <Button
          variant="secondary"
          label="Retry"
          onPress={() => {
            refetch();
          }}
        />
      </View>
    );
  }

  return (
    <ScrollView
      className="mb-20 flex-1 bg-black-0"
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl
          refreshing={loading}
          onRefresh={refetch}
        />
      }
    >
      <View className="flex-1 bg-black-0">
        <View className="flex-1 gap-6 rounded-t-[24px] bg-primary-950 px-5 py-6">
          <View className="flex flex-col gap-2">
            {bookings?.map((booking) => (
              <BookingsCardNoCompanion
                bookingId={booking.id}
                key={booking.id}
                onPress={() => {
                  router.push(`/booking-details/${booking.id}`);
                }}
              />
            ))}
          </View>
          <View>
            <Button
              variant="secondary"
              label="Find Companion"
              onPress={() => {
                router.push('/form/companion-required/traveler-details');
              }}
            />
          </View>

          <View className="gap-2.5">
            <FlashList
              data={images}
              keyExtractor={(item, index) => index.toString()}
              estimatedItemSize={284}
              showsHorizontalScrollIndicator={false}
              pagingEnabled
              onScroll={handleScroll}
              renderItem={renderItem}
            />
          </View>
        </View>
      </View>
    </ScrollView>
  );
}
