import { Text, type TextProps } from 'react-native';

import { cn } from '@/lib/utils';

export interface IInputErrorMsg extends TextProps {
  message: string;
}

const InputErrorMsg = ({ message, className }: IInputErrorMsg) => {
  return (
    <Text
      className={cn(
        ' text-black-0 font-inter font-medium text-sm dark:text-danger-50',
        className
      )}
    >
      {message}
    </Text>
  );
};

InputErrorMsg.displayName = 'InputTextarea';

export default InputErrorMsg;
