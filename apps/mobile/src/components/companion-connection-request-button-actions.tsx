import { trpc } from "@/lib/api";
import { Text, View, Alert } from "react-native";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Button } from "./ui/button";
import { BookingCompanion, BookingTraveler } from "@/types";
import { showMessage } from "react-native-flash-message";
import { router } from "expo-router";
import { useState } from "react";
import { PriceNegotiationModal } from "./price-negotiation-modal";

interface CompanionConnectionRequestButtonActionsProps {
    bookingCompanion: BookingCompanion;
    bookingTraveler: BookingTraveler;
}

export const CompanionConnectionRequestButtonActions = ({ bookingCompanion, bookingTraveler }: CompanionConnectionRequestButtonActionsProps) => {
    const [showPriceNegotiation, setShowPriceNegotiation] = useState(false);
    
    const { data: connectionRequest, isFetching, isError: isErrorConnectionRequest, refetch: refetchConnectionRequest } = useQuery(trpc.connectionRequests.getByCompanionAndTraveler.queryOptions({
      bookingCompanionId: bookingCompanion.id,
      bookingTravelerId: bookingTraveler.id,
    }, {
      staleTime: 10000, // 10 seconds
    }));
  
    const sendConnectionRequestMutation = useMutation(trpc.connectionRequests.create.mutationOptions());
    const removeConnectionRequestMutation = useMutation(trpc.connectionRequests.delete.mutationOptions());
    const acceptPriceMutation = useMutation(trpc.connectionRequests.acceptNegotiatedPrice.mutationOptions());
    const updateStatusConnectionRequestMutation = useMutation(trpc.connectionRequests.updateStatus.mutationOptions());
    const cancelConnectionRequestMutation = useMutation(trpc.connectionRequests.cancel.mutationOptions());

    const handleAcceptPrice = () => {
      if (!connectionRequest?.id || !connectionRequest.proposedPrice) {
        return;
      }
      Alert.alert(
        'Confirm Price',
        `Are you sure you want to accept the proposed price of $${(connectionRequest.proposedPrice / 100).toFixed(2)}?`,
        [
          { text: 'Cancel', style: 'cancel' },
          {
            text: 'Accept',
            style: 'default',
            onPress: () => {
              acceptPriceMutation.mutate({
                connectionRequestId: connectionRequest.id,
                confirmPrice: connectionRequest.proposedPrice || 0,
              }, {
                onSuccess: () => {
                  showMessage({
                    message: 'Price accepted! You can now chat with your companion.',
                    type: 'success',
                  });
                },
                onError: (err) => {
                  showMessage({
                    message: err.message || 'Failed to accept price',
                    type: 'danger',
                  });
                },
                onSettled: () => {
                  refetchConnectionRequest();
                },
              });
            },
          },
        ]
      );
    }

    const cancelConnectionRequest = async () => {
      if (!connectionRequest?.id) {
        showMessage({
          message: 'Error cancelling request',
          type: 'danger',
        }); 
        return;
      }
      Alert.alert(
        'Cancel Connection Request',
        'Are you sure you want to cancel this connection request?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Cancel', style: 'destructive', onPress: () => {
            cancelConnectionRequestMutation.mutate({
              id: connectionRequest.id,
            }, {
              onSuccess: (data) => {
                showMessage({
                  message: data.message || 'Request cancelled',
                  type: 'success',
                });
                router.back();
              },
              onError: (err) => {
                showMessage({
                  message: err.message || 'Error cancelling request',
                  type: 'danger',
                });
              },
              onSettled: () => {
                refetchConnectionRequest();
              },
            });
          }}  
        ]
      );
    }
  
    const sendConnectionRequest = async () => {
      sendConnectionRequestMutation.mutate({
        initiator: 'TRAVELER',
        bookingCompanionId: bookingCompanion.id,
        bookingTravelerId: bookingTraveler.id,
      }, {
        onSuccess: (data) => {
          if (data.requiresNegotiation) {
            setShowPriceNegotiation(true);
          } else {
            showMessage({
              message: 'Request sent',
              type: 'success',
            });
          }
        },
        onError: (error) => {
          showMessage({
            message: error.message || 'Error sending request',
            type: 'danger',
          });
        },
        onSettled: () => {
          refetchConnectionRequest();
        },
      });
    }
  
    const removeConnectionRequest = async () => {
      if (!connectionRequest?.id) {
        return;
      }
      removeConnectionRequestMutation.mutate({
        id: connectionRequest.id,
      }, {
        onSuccess: () => {
          showMessage({
            message: 'Request removed',
            type: 'success',
          });
        },
        onError: (err) => {
          showMessage({
            message: err.message || 'Error removing request',
            type: 'danger',
          });
        },
        onSettled: () => {
          refetchConnectionRequest();
        },
      });
    }

    const acceptConnectionRequest = async () => {
      if (!connectionRequest?.id) {
        return;
      }

      // Show confirmation alert for negotiated price
      if (connectionRequest.status === 'PENDING' && connectionRequest.proposedPrice) {
        Alert.alert(
          'Confirm Connection Request',
          `Are you sure you want to accept this connection request with the negotiated price of $${(connectionRequest.proposedPrice / 100).toFixed(2)}?`,
          [
            { text: 'Cancel', style: 'cancel' },
            {
              text: 'Accept',
              style: 'default',
              onPress: () => {
                updateStatusConnectionRequestMutation.mutate({
                  id: connectionRequest.id,
                  status: 'ACCEPTED',
                }, {
                  onSuccess: () => {
                    showMessage({
                      message: 'Request accepted',
                      type: 'success',
                    });
                    router.dismissTo('/home');
                  },
                  onError: (err) => {
                    showMessage({
                      message: err.message || 'Error accepting request',
                      type: 'danger',
                    });
                  },
                  onSettled: () => {
                    refetchConnectionRequest();
                  },
                });
              },
            },
          ]
        );
      } else {
        // Normal acceptance flow
        updateStatusConnectionRequestMutation.mutate({
          id: connectionRequest.id,
          status: 'ACCEPTED',
        }, {
          onSuccess: () => {
            showMessage({
              message: 'Request accepted',
              type: 'success',
            });
            router.dismissTo('/home');
          },
          onError: () => {
            showMessage({
              message: 'Error accepting request',
              type: 'danger',
            });
          },
          onSettled: () => {
            refetchConnectionRequest();
          },
        });
      }
    }
    
    const rejectConnectionRequest = async () => {
      if (!connectionRequest?.id) {
        return;
      }
      updateStatusConnectionRequestMutation.mutate({
        id: connectionRequest.id,
        status: 'REJECTED',
      }, {
        onSettled: () => {
          refetchConnectionRequest();
        },
      });
    }
  
    if (isFetching) {
      return (
        <View className="flex-row gap-3 mt-3">
          <Button
            label="Loading..."
            variant="darkblue"
            loading={true}
            onPress={() => {}}
          />
        </View>
      )
    }
  
    if (isErrorConnectionRequest) {
      return (
        <View className="flex-row gap-3 mt-3">
          <Text>Error</Text>
        </View>
      )
    }
  
    if (!connectionRequest) {
      return (
        <View className="flex-row gap-3 mt-3">
         <Button
            label="Send Request"
            variant="darkblue"
            loading={sendConnectionRequestMutation.isPending || isFetching}
            onPress={sendConnectionRequest}
          />
        </View>
      )
    }

    if (connectionRequest?.initiator !== 'TRAVELER' && connectionRequest?.status === 'PENDING') {
      return (
        <View className="flex-row gap-3 mt-3">
          <Button
            label="Accept"
            variant="darkblue"
            fullWidth={false}
            className="flex-grow"
            loading={updateStatusConnectionRequestMutation.isPending || isFetching}
            onPress={acceptConnectionRequest}
          />
          <Button
            label="Reject"
            variant="destructive"
            fullWidth={false}
            className="flex-grow"
            loading={updateStatusConnectionRequestMutation.isPending || isFetching}
            onPress={rejectConnectionRequest}
          />
        </View>
      );
    }

    if (connectionRequest?.status === 'PENDING_NEGOTIATION') {
      return (
        <>
          <PriceNegotiationModal
            isVisible={showPriceNegotiation}
            onClose={() => setShowPriceNegotiation(false)}
            connectionRequestId={connectionRequest.id}
            originalTravelerPrice={connectionRequest.originalTravelerPrice || 0}
            originalCompanionPrice={connectionRequest.originalCompanionPrice || 0}
            currentProposedPrice={connectionRequest.proposedPrice || 0}
            onPriceAccepted={() => {
              refetchConnectionRequest();
            }}
          />
          <View className="flex-row gap-3 mt-3 w-full">
            <Button
              label="Negotiate Price"
              variant="darkblue"
              fullWidth={false}
              className="flex-grow"
              onPress={() => setShowPriceNegotiation(true)}
            />
            {!connectionRequest.lastPriceUpdatedByUser && (<Button
              label={`Accept $${((connectionRequest.proposedPrice || 0) / 100).toFixed(2)}`}
              variant="darkblue"
              fullWidth={false}
              className="flex-grow"
              loading={acceptPriceMutation.isPending || isFetching}
              onPress={handleAcceptPrice}
            />)}
          </View>
        </>
      );
    }

    if (connectionRequest?.status === 'ACCEPTED') {
      return <View className="flex-row gap-3 mt-3">
        <Button
          label="Chat"
          variant="darkblue"
          fullWidth={false}
          className="flex-grow"
          onPress={() => {
            router.push(`/chat-screens/${connectionRequest.id}`);
          }}
        />
        <Button
          label="Cancel"
          variant="destructive"
          fullWidth={false}
          className="flex-grow"
          loading={cancelConnectionRequestMutation.isPending || isFetching}
          onPress={cancelConnectionRequest}
        />
      </View>
    }

    return (
        <View className="flex-row gap-3 mt-3">
          {connectionRequest?.status === 'PENDING' && connectionRequest?.initiator === 'TRAVELER'  && (
            <Button
              label="Remove Request"
              variant="destructive"
              loading={removeConnectionRequestMutation.isPending || isFetching}
              onPress={removeConnectionRequest}
            />
          )}
        </View>
    )
  } 