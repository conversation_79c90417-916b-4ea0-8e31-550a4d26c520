import React, { useState } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  Text,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useSendMessage } from '@/hooks/use-stream-chat';

interface ChatInputProps {
  connectionRequestId: string;
}

export default function ChatInput({ connectionRequestId }: ChatInputProps) {
  const [message, setMessage] = useState('');
  const sendMessageMutation = useSendMessage();

  const handleSendMessage = async () => {
    if (!message.trim()) return;

    try {
      await sendMessageMutation.mutateAsync({
        connectionRequestId,
        message: message.trim(),
      });
      setMessage('');
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  const handleKeyPress = (e: any) => {
    if (e.nativeEvent.key === 'Enter' && !e.nativeEvent.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      className="border-t border-gray-200 bg-white"
    >
      <View className="flex-row items-center p-4">
        <TextInput
          className="flex-1 border border-gray-300 rounded-full px-4 py-2 mr-3 text-gray-900"
          placeholder="Type a message..."
          placeholderTextColor="#9CA3AF"
          value={message}
          onChangeText={setMessage}
          onKeyPress={handleKeyPress}
          multiline
          maxLength={1000}
        />
        <TouchableOpacity
          className={`px-4 py-2 rounded-full ${
            message.trim() ? 'bg-primary-500' : 'bg-gray-300'
          }`}
          onPress={handleSendMessage}
          disabled={!message.trim() || sendMessageMutation.isPending}
        >
          <Text
            className={`font-semibold ${
              message.trim() ? 'text-white' : 'text-gray-500'
            }`}
          >
            {sendMessageMutation.isPending ? 'Sending...' : 'Send'}
          </Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
} 