import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { StreamChat } from 'stream-chat';
import { useAuth } from '@/lib';
import { useGetStreamToken } from '@/hooks/use-stream-chat';
import { Env } from '@env';
import { account } from '@/lib/appwrite';
import { Models } from 'react-native-appwrite';
import { Chat, OverlayProvider } from 'stream-chat-react-native';
import { useAuthContext } from '@/lib/auth/auth-context';

type StreamChatContextType = {
  client: StreamChat | null;
  isConnected: boolean;
  disconnect: () => Promise<void>;
};

const StreamChatContext = createContext<StreamChatContextType>({
  client: null,
  isConnected: false,
  disconnect: async () => {},
});

export const useStreamChatContext = () => useContext(StreamChatContext);

export const StreamChatProvider = ({ children }: { children: React.ReactNode }) => {
  const { user, isLoading } = useAuthContext();
  const { data: tokenData, isSuccess } = useGetStreamToken({ enabled: !!user });
  const [client, setClient] = useState<StreamChat | null>(null);
  const [isConnected, setIsConnected] = useState(false);

  // Connect user to StreamChat when authenticated and token is available
  useEffect(() => {
    let isMounted = true;
    const connect = async () => {
      if (!user || !isSuccess || !tokenData?.token) return;

      const streamClient = StreamChat.getInstance(Env.STREAM_API_KEY);
      try {
        await streamClient.connectUser(
          {
            id: user.$id,
            name: user.name,
          //   image: user.avatar,
          },
          tokenData.token
        );
        if (isMounted) {
          setClient(streamClient);
          setIsConnected(true);
        }
        console.log('connect to StreamChat:', isConnected);
      } catch (error) {
        console.error('Failed to connect to StreamChat:', error);
        if (isMounted) {
          setClient(null);
          setIsConnected(false);
        }
      }
    };

    connect();

    return () => {
      isMounted = false;
      if (client) {
        client.disconnectUser();
        setClient(null);
        setIsConnected(false);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSuccess, user, tokenData?.token]);

  // Disconnect function
  const disconnect = useCallback(async () => {
    if (client) {
      await client.disconnectUser();
      setClient(null);
      setIsConnected(false);
    }
  }, [client]);

  console.log('isConnected', isConnected);

//   if (!user) return null;

  if (!client) return children;

  return (
    <StreamChatContext.Provider value={{ client, isConnected, disconnect }}>
        <OverlayProvider>
            <Chat client={client!}>
                {children}
            </Chat>
        </OverlayProvider>
    </StreamChatContext.Provider>
  );
};
