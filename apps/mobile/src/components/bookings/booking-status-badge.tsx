import React from 'react';
import { Text, View } from 'react-native';
import { getStatusColor, getStatusLabel } from '@/utils/booking-utils';
import type { BookingStatus } from '@/types';

interface BookingStatusBadgeProps {
  status: BookingStatus;
  size?: 'sm' | 'md' | 'lg';
}

const BookingStatusBadge: React.FC<BookingStatusBadgeProps> = ({ 
  status, 
  size = 'md' 
}) => {
  const statusColor = getStatusColor(status);
  const statusLabel = getStatusLabel(status);
  
  const sizeClasses = {
    sm: 'px-2 py-1',
    md: 'px-3 py-1.5',
    lg: 'px-4 py-2'
  };

  const textSizeClasses = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base'
  };

  // Extract text color from the status color
  const textColor = statusColor.split(' ')[0];

  return (
    <View className={`rounded-full ${statusColor} ${sizeClasses[size]}`}>
      <Text className={`font-medium ${textColor} ${textSizeClasses[size]}`}>
        {statusLabel}
      </Text>
    </View>
  );
};

export default BookingStatusBadge; 