import React from 'react';
import { Text, View } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { UserType } from '@/store/user-type-store';

interface EmptyStateProps {
  category: 'draft' | 'active' | 'past';
  userType: UserType;
}

const EmptyState: React.FC<EmptyStateProps> = ({ category, userType }) => {
  const getEmptyStateContent = () => {
    const isTraveler = userType === UserType.TRAVELLER;
    
    switch (category) {
      case 'draft':
        return {
          title: 'No Draft Bookings',
          message: isTraveler 
            ? 'You don\'t have any incomplete traveler bookings. Create a new booking to get started.'
            : 'You don\'t have any incomplete companion bookings. Create a new booking to get started.',
          icon: 'document-outline' as const,
          iconColor: '#f59e0b' // amber-500
        };
      case 'active':
        return {
          title: 'No Active Bookings',
          message: isTraveler
            ? 'You don\'t have any active traveler bookings. Create a booking to find companions for your trip.'
            : 'You don\'t have any active companion bookings. Create a booking to help travelers on their trips.',
          icon: 'calendar-outline' as const,
          iconColor: '#10b981' // emerald-500
        };
      case 'past':
        return {
          title: 'No Past Bookings',
          message: isTraveler
            ? 'You don\'t have any completed or cancelled traveler bookings yet. Your booking history will appear here.'
            : 'You don\'t have any completed or cancelled companion bookings yet. Your booking history will appear here.',
          icon: 'time-outline' as const,
          iconColor: '#6b7280' // gray-500
        };
      default:
        return {
          title: 'No Bookings Found',
          message: 'No bookings found for this category.',
          icon: 'search-outline' as const,
          iconColor: '#6b7280' // gray-500
        };
    }
  };

  const content = getEmptyStateContent();

  return (
    <View className="flex-1 items-center justify-center px-6 py-12">
      <View className="w-24 h-24 bg-gray-100 dark:bg-gray-800 rounded-full items-center justify-center mb-6">
        <Ionicons 
          name={content.icon} 
          size={48} 
          color={content.iconColor}
        />
      </View>
      <Text className="text-xl font-bold text-gray-800 dark:text-gray-200 mb-2 text-center">
        {content.title}
      </Text>
      <Text className="text-base text-gray-600 dark:text-gray-400 text-center leading-6">
        {content.message}
      </Text>
    </View>
  );
};

export default EmptyState; 