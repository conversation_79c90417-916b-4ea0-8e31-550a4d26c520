import React from 'react';
import { View } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface BookingListSkeletonProps {
  count?: number;
}

const BookingCardSkeleton: React.FC = () => (
  <View className="my-3 gap-4 rounded-lg bg-gray-100 dark:bg-gray-800 px-2.5 py-3">
    <View className="gap-3">
      {/* Date skeleton */}
      <View className="h-7 bg-gray-200 dark:bg-gray-700 rounded w-24 mx-auto" />
      
      {/* Route skeleton */}
      <View className="gap-1">
        <View className="flex-row items-center justify-between">
          <View className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-12" />
          <View className="flex-row items-center">
            <View className="w-[60px] h-4 bg-gray-200 dark:bg-gray-700 rounded" />
          </View>
          <View className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-12" />
        </View>
        
        {/* Time details skeleton */}
        <View className="flex-row items-center justify-between gap-1">
          <View className="flex-row gap-1">
            <View className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded" />
            <View className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16" />
          </View>
          <View className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20" />
          <View className="flex-row items-center gap-1">
            <View className="h-4 w-4 bg-gray-200 dark:bg-gray-700 rounded" />
            <View className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-16" />
          </View>
        </View>
      </View>
    </View>
    
    {/* Bottom section skeleton */}
    <View className="w-full flex-row justify-between">
      <View className="flex-row gap-2">
        <View className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full" />
        <View className="gap-1">
          <View className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20" />
          <View className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24" />
        </View>
      </View>
      <View className="items-center gap-1">
        <View className="h-5 bg-gray-200 dark:bg-gray-700 rounded w-16" />
        <View className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-20" />
      </View>
    </View>
  </View>
);

const BookingListSkeleton: React.FC<BookingListSkeletonProps> = ({ count = 3 }) => {
  return (
    <View className="flex-1">
      {Array.from({ length: count }).map((_, index) => (
        <BookingCardSkeleton key={index} />
      ))}
    </View>
  );
};

export default BookingListSkeleton; 