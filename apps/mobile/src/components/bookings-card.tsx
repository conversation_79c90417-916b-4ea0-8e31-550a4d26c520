import { Image } from 'expo-image';
import React from 'react';
import { type ImageProps, Pressable, Text, View } from 'react-native';
import { differenceInHours, format } from 'date-fns';
import type { RouterOutputs } from '@/lib/api';
import BookingStatusBadge from './bookings/booking-status-badge';
import { formatCompensation } from '@/utils/booking-utils';
import { BookingCompanion } from '@/types/booking';

interface BookingsCardProps {
  booking: BookingCompanion;
  onPress?: () => void;
  showStatus?: boolean;
}

const BookingsCard: React.FC<BookingsCardProps> = ({
  booking,
  onPress,
  showStatus = true,
}) => {

  const totalLength = booking.destinations.length;
  return (
    <Pressable
      className="my-3 gap-4 rounded-lg bg-custombg px-2.5 py-3"
      onPress={onPress}
    >
      <View className="gap-3">
        {/* Date and Status */}
        <View className="flex-row items-center justify-between">
          <Text className="font-PoppinsBold text-lg font-bold leading-7 dark:text-black-50">
            {booking.flightTime && format(new Date(booking.flightTime), 'dd MMM yyyy')}
          </Text>
          <Text className="font-PoppinsBold text-lg font-bold leading-7 dark:text-black-50">
            {booking.flightEndTime && format(new Date(booking.flightEndTime), 'dd MMM yyyy')}
          </Text>
        </View>

        {/* Route Information */}
        <View className="gap-1">
          <View className="flex-row items-center justify-between">
            <Text className="font-inter text-base font-bold dark:text-black-50">
              {booking.destinations[0].airport?.shortCode}
            </Text>

            <View className="flex-row items-center">
              <Image
                source={require('../../assets/images/circle.svg')}
                style={{ width: 18, height: 18 }}
                contentFit="contain"
              />

              <View className="w-[60px] border border-dashed border-black-0"></View>
              <Image
                source={require('../../assets/images/lineplane.svg')}
                contentFit="contain"
                style={{ height: 19, width: 18 }}
                className="absolute left-11"
              />
              <Image
                source={require('../../assets/images/circle.svg')}
                style={{ width: 18, height: 18 }}
                contentFit="contain"
              />
            </View>
            <Text className="font-inter text-base font-bold dark:text-black-50">
              {booking.destinations[totalLength - 1].airport?.shortCode}
            </Text>
          </View>

          {/* Flight Times */}
          <View className="flex-row items-center justify-between gap-1">
            <View className="flex-row gap-1">
              <Image
                source={require('../../assets/images/flight-take-off.svg')}
                style={{ height: 16, width: 16 }}
                contentFit="contain"
              />
              <Text className="font-inter text-sm font-medium dark:text-black-100">
                {booking.flightTime && format(new Date(booking.flightTime), 'hh:mm aaa')}
              </Text>
            </View>
            <Text className="font-inter text-sm font-medium dark:text-black-100">
              {booking.flightTime && booking.flightEndTime
                ? differenceInHours(new Date(booking.flightEndTime), new Date(booking.flightTime)) + ' Hours'
                : ''}
            </Text>
            <View className="right-0 flex-row items-center gap-1">
              <Image
                source={require('../../assets/images/flight-take-in.svg')}
                style={{ height: 16, width: 16 }}
                contentFit="contain"
              />
              <Text className="right-0 text-right font-inter text-sm font-medium dark:text-black-100">
                {booking.flightEndTime && format(new Date(booking.flightEndTime), 'hh:mm aaa')}
              </Text>
            </View>
          </View>
        </View>
      </View>

      {/* Bottom Section */}
      <View className="w-full flex-row justify-between">
        {/* User Information */}
        <View className="flex-row gap-2">
          <Image
            source={{
              uri: booking.travelersPhoto || 'https://placehold.co/40x40',
            }}
            style={{ width: 50, height: 50, borderRadius: 6 }}
            contentFit="cover"
          />
          <View className="gap-1">
            <Text className="text-base font-bold text-green dark:text-green">
              {booking.name} {booking.lastName}
            </Text>
            <Text className="text-[14px] font-normal leading-5 text-black-950 dark:text-black-0">
              {booking.gender}, {booking.about?.substring(0, 20)}...
            </Text>
          </View>
        </View>

        {/* Compensation and Status */}
        {booking.compensationValue && (
          <View className="items-center gap-1">
            <Text className="font-inter text-base font-bold dark:text-black-100">
              {formatCompensation(booking.compensationValue)}
            </Text>
            <BookingStatusBadge status={booking.status} size="sm" />
          </View>
        )}
      </View>
    </Pressable>
  );
};

export default BookingsCard;
