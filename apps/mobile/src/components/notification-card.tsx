import { View, Text, Image } from 'react-native';
import React from 'react';

type INotificatinCard={
    text:string
}
const NotificationCard :React.FC <INotificatinCard>= ({text}) => {
  return (
    <View className="py-3 mb-2.5 rounded-[12px] flex-row items-center gap-3">
      <View>
        <Image
          source={require('../../assets/images/notificationavtar.png')}
          resizeMode="contain"
          style={{ height: 44, width: 44 }}
        />
      </View>

      <View className="flex-1 gap-1 ">
        <View className="flex-row  w-full justify-between items-center ">
          <Text className="text-black-0 font-inter text-[14px] font-semibold leading-5">
            {text}
          </Text>
          <Text className="text-black-0 text-[10px] font-normal leading-4 dark:text-black-300">
            10:59 PM
          </Text>
        </View>

        <Text className="text-black-0 font-inter text-[12px] leading-4 font-normal dark:text-black-100">
          <PERSON><PERSON> has sent you a request to be your travel companion! Check out
          their profile and ...
        </Text>
      </View>
    </View>
  );
};

export default NotificationCard;
