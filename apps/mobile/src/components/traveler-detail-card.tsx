import React, { useCallback, useEffect, useState } from 'react';
import { View, Text, View as RNView, Pressable } from 'react-native';
import { tv } from 'tailwind-variants';
import type { VariantProps } from 'tailwind-variants';
import { Image } from 'expo-image';
import { BookingCompanion, BookingTraveler } from '@/types/booking';
import { TravelerConnectionRequestButtonActions } from './traveler-connection-request-button-actions';
import { formatCompensation } from '@/utils/booking-utils';
import { format } from 'date-fns';

const card = tv({
  slots: {
    container: 'px-4 py-4 rounded-[12px] gap-4 mt-4',
    image: 'h-12 w-12',
    title: 'font-bold text-[17px] font-PoppinsBold',
    subTitle: 'font-normal leading-5 font-inter',
    rating: 'font-bold',
    divider: 'border',
    tagsContainer: 'flex-row items-center gap-2',

    tag: 'px-2 py-[5px] bg-black-0 rounded-[20px]',
    tagtext: 'font-inter leading-4 text-[12px] font-normal',
    tagtext2: 'font-inter font-normal text-[12px] leading-4',

    footer: 'flex-row items-center gap-2',
  },

  variants: {
    variant: {
      default: {
        container:
          'bg-black-950 border border-secondary-650 dark:bg-primary-950',
        title: 'text-black-0 dark:text-secondary-700',
        subTitle: 'text-black-0 dark:text-black-50',
        divider: 'dark:border border-black-0',
        tag: 'bg-secondary-100 dark:bg-secondary-100',
        tagtext: 'text-black-0 dark:text-secondary-700',
        tagtext2: ' dark:text-secondary-700',

        footer: 'text-gray-300',
      },
      secondary: {
        container: 'bg-black-950 border border-borderdark dark:bg-bgdark',
        title: 'text-gray-800 dark:text-black-50',
        subTitle: 'text-black-0 dark:text-black-200',
        divider: 'dark:border border-black-0',
        tag: 'bg-primary-900 text-black-0',
        tagtext: 'text-black-0',
        tagtext2: 'text-black-0 dark:text-black-100',
        footer: 'text-gray-600',
      },
    },
  },

  defaultVariants: {
    variant: 'default',
  },
});

type CardVariants = VariantProps<typeof card>;

interface CompanionCardProps extends CardVariants {
  bookingCompanion: BookingCompanion;
  bookingTraveler: BookingTraveler;
  onPress?: () => void;
  variant?: 'default' | 'secondary';
}



export const TravelerDetailCard = React.forwardRef<RNView, CompanionCardProps>(
  ({ bookingCompanion, bookingTraveler, variant = 'default', onPress }, ref) => {
    const styles = React.useMemo(() => card({ variant }), [variant]);


    // console.log('connectionRequest', JSON.stringify(connectionRequest));
    // if (!connectionRequest) {
    //   return null;
    // }

    return (
      <Pressable
        onPress={onPress}
      >
        <View className={styles.container()} ref={ref}>
          {/* Header Section */}
          <View className="flex-row  justify-between items-center">
            <View className="flex-row items-center gap-4 flex-1">
              <Image
                source={{uri: bookingCompanion.travelersPhoto ?? 'https://placehold.co/60x60'}}
                contentFit="cover"
                style={{ height: 60, width: 60, borderRadius: 12 }}
              />

              <View className="gap-1 flex-1 ">
                <Text className={styles.title()}>{bookingCompanion.name}</Text>
                <Text className={styles.subTitle()}>{bookingCompanion.gender}</Text>
              </View>
            </View>

            <View className="gap-2.5 ">
              <View className="flex-row items-center gap-1">
                <Text className="text-black-950 leading-5 font-inter font-medium  dark:text-black-50 text-right">
                  {bookingCompanion.flightTime && format(new Date(bookingCompanion.flightTime), 'dd MMM yyyy')}
                </Text>
                {/* <Image
                  source={require('../../assets/images/single-star.png')}
                  contentFit="contain"
                  style={{ height: 12, width: 12 }}
                />
                <Image
                  source={require('../../assets/images/single-star.png')}
                  contentFit="contain"
                  style={{ height: 12, width: 12 }}
                />
                <Image
                  source={require('../../assets/images/single-star.png')}
                  contentFit="contain"
                  style={{ height: 12, width: 12 }}
                />
                <Image
                  source={require('../../assets/images/single-star.png')}
                  contentFit="contain"
                  style={{ height: 12, width: 12 }}
                /> */}
              </View>

              <View className="flex-row items-center justify-end">
                <Text className="text-black-950 leading-5 font-inter font-medium  dark:text-black-50">
                  {bookingCompanion.compensationValue && formatCompensation(bookingCompanion.compensationValue)}
                </Text>
              </View>
            </View>
          </View>
          <View className={styles.divider()} />

          {/* Static Section - Remains unchanged */}
          <View className="flex-row items-center gap-2">
            <Image
              source={require('../../assets/images/text-icon.png')}
              contentFit="contain"
              style={{ height: 24, width: 24 }}
            />
            {bookingCompanion.languages.map((language) => (
              <View key={language.language.id} className={styles.tag()}>
                <Text className={styles.tagtext()}>{language.language.name}</Text>
              </View>
            ))}

            {/* <Text className={styles.tagtext()}>+5More</Text> */}
          </View>

          <View className="flex-row items-center gap-2">
            <Image
              source={require('../../assets/images/genderpreference.png')}
              contentFit="contain"
              style={{ height: 24, width: 24 }}
            />
            <Text className="text-black-950 font-inter leading-4 text-[12px] dark:text-black-0">
              {bookingCompanion.genderPreference}
            </Text>
          </View>
          <View className="flex-row gap-2">
            <View className="px-[10px] py-[6px] rounded-[8px] flex-row items-center gap-2 dark:bg-black-0">
              <Image
                source={require('../../assets/images/plane.png')}
                contentFit="contain"
                style={{ height: 8.999, width: 9 }}
                tintColor={'#071952'}
              />
              <Text className="font-inter text-black-950 leading-[14px] text-[11px] font-normal dark:text-blue">
                {bookingCompanion.typeOfTraveler}
              </Text>
            </View>
            {/* <View className="px-[10px] py-[6px] rounded-[8px] dark:bg-black-0">
              <Text className="text-black-0 text-[11px] leading-[14px] font-normal dark:text-blue ">
                Assistance: 45 Trips
              </Text>
            </View> */}
          </View>
          {/* Action Buttons */}
          <TravelerConnectionRequestButtonActions bookingCompanion={bookingCompanion} bookingTraveler={bookingTraveler} />
        </View>
      </Pressable>
    );
  }
);

export default TravelerDetailCard;
