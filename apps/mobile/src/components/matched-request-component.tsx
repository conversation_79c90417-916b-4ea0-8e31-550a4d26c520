import { View, Text, Image, Pressable, Dimensions } from 'react-native';
import React from 'react';

const { height, width } = Dimensions.get('screen');

type IUserRequesProps = {
  onPressChat: () => void;
  onPressProfile: () => void;
  RatingNum: string;
  title: string;
  subtitle: string;
};
const MatchedRequestComponent: React.FC<IUserRequesProps> = ({
  onPressChat,
  onPressProfile,
  RatingNum,
  title,
  subtitle,
}) => {
  return (
    <View className="px-5 py-5 rounded-[8px] bg-black-0 gap-3 flex-1 mt-4  dark:bg-darkcardbg">
      <View className="flex-row items-center flex-1 gap-8">
        <View className="flex-row  gap-2 flex-1 self-start ">
          <Image
            source={require('../../assets/images/profile.png')}
            resizeMode="contain"
            style={{ width: 50, height: 50 }}
          />
          <View className="gap-1  ">
            <Text className="text-green  font-bold leading-6 text-[16px] dark:text-green">
              {title}
            </Text>
            <Text className="text-black-950 font-normal leading-5 text-[14px] dark:text-black-0 text-wrap">
              {subtitle}
            </Text>
          </View>
        </View>

        <View className="flex-grow flex-1 gap-1 items-center">
          <View className="flex-row gap-2 items-center self-end">
            <Image
              source={require('../../assets/images/single-star.png')}
              resizeMode="contain"
              style={{ height: 14, width: 14 }}
            />
            <Image
              source={require('../../assets/images/single-star.png')}
              resizeMode="contain"
              style={{ height: 14, width: 14 }}
            />
            <Image
              source={require('../../assets/images/single-star.png')}
              resizeMode="contain"
              style={{ height: 14, width: 14 }}
            />

            <Text className="text-black-950 dark:text-black-0">
              {RatingNum}
            </Text>
          </View>

          <View className="px-[10px] py-[6px]  bg-black-950 flex-row items-center gap-1 rounded-[8px] dark:bg-black-50 self-end">
            <Image
              source={require('../../assets/images/plane.png')}
              resizeMode="contain"
              style={{ height: 12, width: 12 }}
              tintColor={'#071952'}
            />
            <Text className=" font-inter text-black-950 font-medium leading-[14px] text-[11px] dark:text-blue">
              Solo Traveler
            </Text>
          </View>
        </View>
      </View>

      <View className="flex-row items-center justify-between">
        <Pressable
          className="bg-black-950 rounded-[12px] items-center justify-center dark:bg-black-0"
          onPress={onPressChat}
          style={{ height: height * 0.0472, width: width * 0.393 }}
        >
          <Text className=" font-inter leading-5 font-medium text-[14px] text-black-0  dark:text-blue">
            Start Chat
          </Text>
        </Pressable>

        <Pressable
          className="bg-black-950 rounded-[12px] items-center justify-center dark:bg-darkblackbutton"
          style={{ height: height * 0.0472, width: width * 0.393 }}
          onPress={onPressProfile}
        >
          <Text className="font-inter leading-5 font-medium text-[14px] text-black-0  dark:text-black-100">
            View Profile
          </Text>
        </Pressable>
      </View>
    </View>
  );
};

export default MatchedRequestComponent;
