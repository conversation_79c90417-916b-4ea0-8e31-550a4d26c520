import { cn } from '@/lib/utils';
import { FC } from 'react';
import { TextInput, TextInputProps } from 'react-native';

export interface IInputTextarea extends TextInputProps {
  placeholder: string;
}

const InputTextarea: FC<IInputTextarea> = ({
  placeholder,
  className,
  placeholderTextColor,
}) => {
  return (
    <TextInput
      multiline={true}
      verticalAlign="top"
      numberOfLines={4}
      placeholderTextColor={placeholderTextColor || 'gray'}
      placeholder={placeholder}
      className={cn(
        'bg-black-0  rounded-[12px] mt-2.5 flex-row items-center gap-2 py-[14px] px-5 border border-black-100 dark:bg-bgtextInput border-borderdark text-black-0',
        className,
      )}
    />
  );
};

InputTextarea.displayName = 'InputTextarea';

export default InputTextarea;
