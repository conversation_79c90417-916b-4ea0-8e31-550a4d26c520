import React, { useEffect, useState } from 'react';
import { View, Text, Alert } from 'react-native';
import { Modal, useModal } from './ui/modal';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { trpc } from '@/lib/api';
import { showMessage } from 'react-native-flash-message';
import { useMutation } from '@tanstack/react-query';

interface PriceNegotiationModalProps {
  isVisible: boolean;
  onClose: () => void;
  connectionRequestId: string;
  originalTravelerPrice: number;
  originalCompanionPrice: number;
  currentProposedPrice: number;
  onPriceAccepted: () => void;
}

export const PriceNegotiationModal: React.FC<PriceNegotiationModalProps> = ({
  isVisible,
  onClose,
  connectionRequestId,
  originalTravelerPrice,
  originalCompanionPrice,
  currentProposedPrice,
  onPriceAccepted,
}) => {
  const {ref, present, dismiss} = useModal();
  const [proposedPrice, setProposedPrice] = useState(currentProposedPrice / 100);
  const [message, setMessage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const proposePriceMutation = useMutation(trpc.connectionRequests.proposePrice.mutationOptions());
  const acceptPriceMutation = useMutation(trpc.connectionRequests.acceptNegotiatedPrice.mutationOptions());

  useEffect(() => {
    if (isVisible) {
      present();
    } else {
      dismiss();
    }
  }, [isVisible]);

  const handleProposePrice = async () => {
    if (proposedPrice <= 0) {
      showMessage({
        message: 'Please enter a valid price',
        type: 'danger',
      });
      return;
    }

    setIsSubmitting(true);
    try {
      await proposePriceMutation.mutateAsync({
        connectionRequestId,
        proposedPrice: Math.round(proposedPrice * 100),
        message: message.trim() || undefined,
      });

      showMessage({
        message: 'Price proposal sent successfully',
        type: 'success',
      });
      onClose();
    } catch (error) {
      showMessage({
        message: 'Failed to send price proposal',
        type: 'danger',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleAcceptPrice = () => {
    Alert.alert(
      'Confirm Price',
      `Are you sure you want to accept the proposed price of $${(currentProposedPrice / 100).toFixed(2)}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Accept',
          style: 'default',
          onPress: async () => {
            setIsSubmitting(true);
            try {
              await acceptPriceMutation.mutateAsync({
                connectionRequestId,
                confirmPrice: currentProposedPrice,
              });

              showMessage({
                message: 'Price accepted! Request is now pending final acceptance.',
                type: 'success',
              });
              onPriceAccepted();
              onClose();
            } catch (error) {
              showMessage({
                message: 'Failed to accept price',
                type: 'danger',
              });
            } finally {
              setIsSubmitting(false);
            }
          },
        },
      ]
    );
  };

  return (
    <Modal
      ref={ref}
    //   isVisible={isVisible}
      onDismiss={() => {
        dismiss();
        onClose();
      }}
      title="Price Negotiation"
    >
      <View className="p-4 space-y-4 bg-black-950 flex-1">
        <View className="space-y-2">
          <Text className="text-lg font-semibold text-white">
            Price Mismatch Detected
          </Text>
          <Text className="text-white">
            Traveler offering: ${(originalTravelerPrice / 100).toFixed(2)}
          </Text>
          <Text className="text-white">
            Companion requesting: ${(originalCompanionPrice / 100).toFixed(2)}
          </Text>
        </View>

        <View className="space-y-2">
          <Text className="text-lg font-semibold text-white">
            Current Proposed Price
          </Text>
          <Text className="text-2xl font-bold text-blue-600">
            ${(currentProposedPrice / 100).toFixed(2)}
          </Text>
        </View>

        <View className="space-y-2">
          <Text className="text-sm font-medium text-white">
            Propose New Price (USD)
          </Text>
          <Input
            value={proposedPrice.toString()}
            onChangeText={(text) => setProposedPrice(parseFloat(text) || 0)}
            placeholder="Enter your proposed price"
            keyboardType="numeric"
          />
        </View>

        <View className="space-y-2">
          <Text className="text-sm font-medium text-white">
            Message (Optional)
          </Text>
          <Input
            value={message}
            onChangeText={setMessage}
            placeholder="Add a message to your proposal"
            // multiline
            // numberOfLines={3}
          />
        </View>

        <View className="flex-row gap-3 pt-4">
          <Button
            label="Propose"
            variant="secondary"
            loading={isSubmitting}
            fullWidth={false}
            onPress={handleProposePrice}
            className="flex-grow"
          />
        </View>

        {/* <Button
          label="Cancel"
          variant="outline"
          onPress={onClose}
          disabled={isSubmitting}
        /> */}
      </View>
    </Modal>
  );
}; 