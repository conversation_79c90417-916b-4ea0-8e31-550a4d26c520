import { Env } from '@env';
import { format } from 'date-fns';
import { Image } from 'expo-image';
import React, { useEffect, useState } from 'react';
import { ActivityIndicator, Pressable, Text, View } from 'react-native';

import { databases } from '@/lib/appwrite';
import { trpc } from '@/lib/api';
import { useQuery } from '@tanstack/react-query';
import { Button } from './ui/button';
import BookingStatusBadge from './bookings/booking-status-badge';
import { formatCompensation } from '@/utils/booking-utils';

type IBookingsCardProps = {
  bookingId: string;
  onPress?: () => void;
  detailed?: boolean;
};
const CompanionBookingsCard: React.FC<IBookingsCardProps> = ({
  bookingId,
  onPress,
  detailed = true,
}) => {
  const { data: bookingDetails, isLoading, isError, refetch } = useQuery(trpc.bookingTravelers.getById.queryOptions({ id: bookingId }));

  if (isLoading) {
    return <ActivityIndicator />;
  }

  if (isError || !bookingDetails) {
    return (
      <View className="flex-1 items-center justify-center">
        <Text className="text-red-500">Error fetching booking details</Text>
        <Button
          variant="secondary"
          label="Retry"
          onPress={() => {
            refetch();
          }}
        />
      </View>
    );
  }

  const totalDestinations = bookingDetails?.destinations.length;

  return (
    <Pressable
      className="my-3 gap-4 rounded-lg bg-custombg px-2.5 py-3"
      onPress={onPress}
    >
      <View className="gap-3">
        <View className="flex-row items-center justify-between">
          <Text className="text-center font-PoppinsBold text-lg font-bold leading-7 dark:text-black-50  ">
            {bookingDetails.flightTime && format(bookingDetails.flightTime, 'dd MMM yyyy')}
          </Text>
          <Text className="text-center font-PoppinsBold text-lg font-bold leading-7 dark:text-black-50  ">
            {bookingDetails.flightEndTime && format(bookingDetails.flightEndTime, 'dd MMM yyyy')}
          </Text>
        </View>

        <View className="gap-1">
          <View className="flex-row items-center justify-between">
            <Text className="font-inter text-base font-bold  dark:text-black-50">
              {bookingDetails.destinations[0]?.airport?.shortCode}
            </Text>

            <View className="flex-row items-center">
              <Image
                source={require('../../assets/images/circle.svg')}
                style={{ width: 18, height: 18 }}
                contentFit="contain"
              />

              <View className="w-[60px] border border-dashed  border-black-0 "></View>
              <Image
                source={require('../../assets/images/lineplane.svg')}
                contentFit="contain"
                style={{ height: 19, width: 18 }}
                className="absolute left-11"
              />
              <Image
                source={require('../../assets/images/circle.svg')}
                style={{ width: 18, height: 18 }}
                contentFit="contain"
              />
            </View>
            <Text className="font-inter text-base font-bold  dark:text-black-50">
              {
                bookingDetails.destinations[totalDestinations - 1]
                  ?.airport?.shortCode
              }
            </Text>
          </View>

          <View className="flex-row items-center justify-between gap-1">
            <View className="flex-row gap-1">
              {bookingDetails.flightTime && (
                <>
                  <Image
                    source={require('../../assets/images/flight-take-off.svg')}
                    style={{ height: 16, width: 16 }}
                    contentFit="contain"
                  />
                  <Text className="font-inter text-sm  font-medium dark:text-black-100">
                    {format(bookingDetails.flightTime, 'hh:mm a')}
                  </Text>
                </>
              )}
            </View>
            <Text className="font-inter text-sm  font-medium dark:text-black-100">
              {/* {'travelTiming'} */}
            </Text>
            <View className="right-0 flex-row items-center gap-1">
              {bookingDetails.flightEndTime && (
                <>
                  <Image
                    source={require('../../assets/images/flight-take-in.svg')}
                    style={{ height: 16, width: 16 }}
                    contentFit="contain"
                  />
                  <Text className="right-0 text-right  font-inter text-sm font-medium dark:text-black-100">
                    {format(bookingDetails.flightEndTime, 'hh:mm a')}
                  </Text>
                </>
              )}
            </View>
          </View>
        </View>
        {/* Bottom Section */}
        {detailed && (
          <View className="w-full flex-row justify-between">
            {/* User Information */}
            <View className="flex-row gap-2">
              <Image
                source={{
                  uri: bookingDetails.companionPhoto || 'https://placehold.co/40x40',
                }}
                style={{ width: 50, height: 50, borderRadius: 6 }}
                contentFit="cover"
              />
              <View className="gap-1">
                <Text className="text-base font-bold text-green dark:text-green">
                  {bookingDetails.name} {bookingDetails.lastName}
                </Text>
                <Text className="text-[14px] font-normal leading-5 text-black-950 dark:text-black-0">
                  {bookingDetails.gender}, {bookingDetails.about?.substring(0, 20)}...
                </Text>
              </View>
            </View>

            {/* Compensation and Status */}
            {bookingDetails.compensationValue && (
              <View className="items-center gap-1">
                <Text className="font-inter text-base font-bold dark:text-black-100">
                  {formatCompensation(bookingDetails.compensationValue)}
                </Text>
                <BookingStatusBadge status={bookingDetails.status} size="sm" />
              </View>
            )}
          </View>
        )}
      </View>
    </Pressable>
  );
};

export default CompanionBookingsCard;
