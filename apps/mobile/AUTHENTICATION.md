# Authentication System

This document describes the authentication system implemented for the Thedal mobile app using Appwrite and Expo Router.

## Architecture Overview

The authentication system is built with the following components:

### 1. Route Groups
- `(auth)` - Unauthenticated users (login, signup, etc.)
- `(app)` - Authenticated users (main app screens)
- `onboarding` - First-time user experience
- `welcome` - Welcome screen after onboarding

### 2. Core Components

#### AuthContext (`/lib/auth/auth-context.tsx`)
- Manages authentication state
- Provides user information
- Handles loading states
- Offers sign out functionality

#### AuthRedirect Hook (`/lib/auth/use-auth-redirect.ts`)
- Automatically redirects users based on authentication status
- Prevents unauthorized access to protected routes
- Handles navigation between auth and app groups

#### ProtectedRoute Component (`/components/protected-route.tsx`)
- Wraps screens that require authentication
- Shows loading state while checking auth
- Provides fallback for unauthenticated users

#### AppwriteAuthService (`/lib/auth/appwrite-auth.service.ts`)
- Handles all Appwrite authentication operations
- Provides clean API for auth operations
- Includes error handling and type safety

## Usage

### Basic Authentication Flow

1. **App Launch**: The app checks for existing authentication
2. **Unauthenticated**: Redirects to `/(auth)` group
3. **Authenticated**: Redirects to `/(app)` group
4. **Loading**: Shows loading screen while checking auth status

### Using Authentication in Components

```typescript
import { useAuthContext } from '@/lib/auth/auth-context';
import { useSignOut } from '@/lib/auth/use-sign-out';

function MyComponent() {
  const { user, isLoading } = useAuthContext();
  const { signOut } = useSignOut();

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <View>
      <Text>Welcome, {user?.name}</Text>
      <Button onPress={signOut} title="Sign Out" />
    </View>
  );
}
```


### Authentication Operations

```typescript
import { AppwriteAuthService } from '@/lib/auth/appwrite-auth.service';

// Sign up
const result = await AppwriteAuthService.signUp({
  email: '<EMAIL>',
  password: 'password123',
  name: 'John Doe'
});

// Sign in
const result = await AppwriteAuthService.signIn({
  email: '<EMAIL>',
  password: 'password123'
});

// Sign out
const result = await AppwriteAuthService.signOut();
```

## Route Structure

```
src/app/
├── (auth)/
│   ├── _layout.tsx          # Auth group layout
│   ├── index.tsx            # Redirects to login
│   ├── login.tsx            # Login screen
│   ├── create-account.tsx   # Sign up screen
│   ├── login-guest.tsx      # Guest login
│   └── verification.tsx     # Email verification
├── (app)/
│   ├── _layout.tsx          # App group layout (protected)
│   ├── index.tsx            # Redirects to home
│   ├── home.tsx             # Home screen
│   ├── chat.tsx             # Chat screen
│   ├── match.tsx            # Match screen
│   ├── offers.tsx           # Offers screen
│   └── profile.tsx          # Profile screen
├── onboarding.tsx           # Onboarding flow
├── welcome.tsx              # Welcome screen
├── loading.tsx              # Loading screen
└── _layout.tsx              # Root layout with auth logic
```

## Security Features

1. **Automatic Redirects**: Users are automatically redirected based on auth status
2. **Route Protection**: Protected routes are wrapped with authentication checks
3. **Session Management**: Appwrite handles session persistence and security
4. **Loading States**: Proper loading states prevent unauthorized access during auth checks
5. **Error Handling**: Comprehensive error handling for all auth operations

## Configuration

### Appwrite Setup

The Appwrite client is configured in `/lib/appwrite.ts`:

```typescript
export const client = new Client()
  .setProject('YOUR_PROJECT_ID')
  .setPlatform('com.nextflytech.thedal');
```

### Environment Variables

Make sure to set the following environment variables:

```env
EXPO_PUBLIC_APPWRITE_PROJECT_ID=your_project_id
EXPO_PUBLIC_APPWRITE_ENDPOINT=your_endpoint
```

## Best Practices

1. **Always use the AuthContext**: Don't directly call Appwrite methods in components
2. **Handle loading states**: Always check `isLoading` before rendering protected content
3. **Use ProtectedRoute**: Wrap any screen that requires authentication
4. **Error handling**: Always handle authentication errors gracefully
5. **Type safety**: Use the provided TypeScript interfaces for type safety

## Troubleshooting

### Common Issues

1. **Infinite redirects**: Check that auth state is properly managed
2. **Loading never ends**: Ensure Appwrite client is properly configured
3. **Session not persisting**: Verify Appwrite session configuration
4. **Type errors**: Make sure all imports are correct and types match

### Debug Mode

Enable debug logging by adding console logs in the auth context:

```typescript
console.log('Auth state:', { user, isLoading });
```

## Migration from Old System

If migrating from the previous authentication system:

1. Replace direct Appwrite calls with `AppwriteAuthService`
2. Wrap protected screens with `ProtectedRoute`
3. Use `useAuthContext` instead of local auth state
4. Update navigation to use the new route groups
5. Remove old auth-related code 