# Connectivity Modal Implementation

## Overview

This implementation adds a network connectivity modal that appears when there's no internet connection and automatically disappears when connectivity is restored.

## Features

- **Undismissible Modal**: The modal cannot be dismissed by the user and will only disappear when internet connectivity is restored
- **Automatic Navigation**: When connectivity is restored, the app automatically navigates back to the root page
- **Real-time Monitoring**: Uses `expo-network`'s `useNetworkState()` hook to monitor network connectivity in real-time
- **Dark Mode Support**: The modal supports both light and dark themes
- **Global Integration**: The modal is integrated at the app level and appears over all screens

## Implementation Details

### Files Created/Modified:

1. **`src/lib/hooks/use-network-connectivity.tsx`** - Custom hook for network connectivity
2. **`src/components/connectivity-modal.tsx`** - The modal component
3. **`src/app/_layout.tsx`** - Added modal to the main app layout
4. **`src/lib/hooks/index.tsx`** - Exported the new hook

### Dependencies Added:

- `expo-network` - For network connectivity monitoring

## Usage

### Testing the Modal

To test the connectivity modal, you can temporarily enable test mode:

1. Open `src/components/connectivity-modal.tsx`
2. Change `const TEST_MODE = false;` to `const TEST_MODE = true;`
3. The modal will appear regardless of network connectivity
4. Remember to set it back to `false` before production

### How It Works

1. The `useNetworkConnectivity` hook monitors network state using `expo-network`
2. When `isConnected` becomes `false`, the modal appears
3. When `isConnected` becomes `true`, the modal disappears and navigates to root
4. The modal is rendered at the app level, so it appears over all screens

### Customization

You can customize the modal by modifying:

- **Icon**: Change the image source in the modal
- **Colors**: Modify the Tailwind classes for different styling
- **Text**: Update the title and description text
- **Behavior**: Modify the navigation logic in the `useEffect`

## Network Connectivity Logic

The connectivity check considers a device connected if:

- `networkState.isConnected` is `true`
- `networkState.isInternetReachable` is `true`

This ensures both network interface availability and actual internet connectivity.

## Notes

- The modal uses a semi-transparent background overlay
- The modal is centered and responsive
- The implementation follows the existing project's styling patterns
- The hook automatically handles network state changes without manual polling
