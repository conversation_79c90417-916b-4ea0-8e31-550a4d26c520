{"name": "thedal-mobile", "version": "0.0.6", "private": true, "main": "expo-router/entry", "scripts": {"start": "cross-env EXPO_NO_DOTENV=1 expo start", "prebuild": "cross-env EXPO_NO_DOTENV=1 pnpm expo prebuild", "android": "cross-env EXPO_NO_DOTENV=1 expo run:android", "ios": "cross-env EXPO_NO_DOTENV=1 expo run:ios", "web": "cross-env EXPO_NO_DOTENV=1 expo start --web", "xcode": "xed -b ios", "doctor": "npx expo-doctor@latest", "preinstall": "npx only-allow pnpm", "start:staging": "cross-env APP_ENV=staging pnpm run start", "prebuild:staging": "cross-env APP_ENV=staging pnpm run prebuild", "prebuild:development": "cross-env APP_ENV=development pnpm run prebuild", "android:staging": "cross-env APP_ENV=staging pnpm run android", "ios:staging": "cross-env APP_ENV=staging pnpm run ios", "start:production": "cross-env APP_ENV=production pnpm run start", "prebuild:production": "cross-env APP_ENV=production pnpm run prebuild", "android:production": "cross-env APP_ENV=production pnpm run android", "ios:production": "cross-env APP_ENV=production pnpm run ios", "build:development:ios": "cross-env APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile development --platform ios", "build:development:android": "cross-env APP_ENV=development EXPO_NO_DOTENV=1 eas build --profile development --platform android ", "build:staging:ios": "cross-env APP_ENV=staging EXPO_NO_DOTENV=1 eas build --profile staging --platform ios", "build:staging:android": "cross-env APP_ENV=staging EXPO_NO_DOTENV=1 eas build --profile staging --platform android ", "build:production:ios": "cross-env APP_ENV=production EXPO_NO_DOTENV=1 eas build --profile production --platform ios", "build:production:android": "cross-env APP_ENV=production EXPO_NO_DOTENV=1 eas build --profile production --platform android ", "app-release": "cross-env SKIP_BRANCH_PROTECTION=true np --no-publish --no-cleanup --no-release-draft", "version": "pnpm run prebuild && git add .", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc  --noemit", "lint:translations": "eslint ./src/translations/ --fix --ext .json  ", "test": "jest", "check-all": "pnpm run lint && pnpm run type-check && pnpm run lint:translations && pnpm run test", "test:ci": "pnpm run test --coverage", "test:watch": "pnpm run test --watch", "install-maestro": "curl -Ls 'https://get.maestro.mobile.dev' | bash", "e2e-test": "maestro test .maestro/ -e APP_ID=com.obytes.development"}, "dependencies": {"@date-fns/tz": "^1.2.0", "@expo-google-fonts/poppins": "^0.2.3", "@expo/metro-runtime": "^5.0.4", "@gorhom/bottom-sheet": "^5.1.4", "@hookform/resolvers": "^3.9.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/datetimepicker": "^8.4.1", "@react-native-community/netinfo": "^11.4.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/material-top-tabs": "^7.2.13", "@react-navigation/native": "^7.1.6", "@react-navigation/native-stack": "^7.3.10", "@repo/customer-api": "workspace:*", "@shopify/flash-list": "1.7.6", "@stripe/stripe-react-native": "0.50.1", "@tanstack/react-query": "^5.52.1", "@trpc/client": "catalog:", "@trpc/server": "catalog:", "@trpc/tanstack-react-query": "catalog:", "app-icon-badge": "^0.1.2", "axios": "^1.7.5", "babel-plugin-react-compiler": "19.1.0-rc.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "expo": "~53.0.19", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-calendar": "~14.1.4", "expo-clipboard": "~7.1.5", "expo-constants": "~17.1.7", "expo-dev-client": "~5.2.4", "expo-document-picker": "~13.1.6", "expo-font": "~13.3.2", "expo-image": "~2.3.2", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-localization": "~16.1.6", "expo-mail-composer": "~14.1.5", "expo-network": "^7.1.5", "expo-radio-button": "^1.0.8", "expo-router": "~5.1.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-updates": "~0.28.17", "expo-web-browser": "~14.2.0", "i18next": "^23.14.0", "lodash.memoize": "^4.1.2", "nativewind": "^4.1.21", "react": "catalog:react19", "react-dom": "catalog:react19", "react-error-boundary": "^4.0.13", "react-hook-form": "catalog:", "react-i18next": "^15.0.1", "react-native": "0.79.5", "react-native-appwrite": "^0.6.0", "react-native-avoid-softinput": "^7.0.0", "react-native-country-codes-picker": "^2.3.5", "react-native-dropdown-picker": "^5.4.6", "react-native-edge-to-edge": "^1.6.0", "react-native-flash-message": "^0.4.2", "react-native-gesture-handler": "^2.27.2", "react-native-image-slider-box": "^2.0.7", "react-native-international-phone-number": "^0.9.2", "react-native-keyboard-controller": "^1.15.2", "react-native-mmkv": "~3.2.0", "react-native-modern-datepicker": "1.0.0-beta.91", "react-native-otp-entry": "^1.8.2", "react-native-pager-view": "^6.7.1", "react-native-paper": "^5.13.1", "react-native-reanimated": "~3.17.5", "react-native-restart": "0.0.27", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-simple-toast": "^3.3.2", "react-native-star-rating-widget": "^1.9.2", "react-native-svg": "~15.11.2", "react-native-tab-view": "^4.0.5", "react-native-text-input-otp": "^1.0.7", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.3.0", "react-query-kit": "^3.3.0", "stream-chat-react-native": "^8.3.1", "stripe": "catalog:", "superjson": "catalog:", "tailwind-variants": "^0.2.1", "use-debounce": "^10.0.4", "zod": "catalog:", "zustand": "catalog:"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.9", "@babel/runtime": "^7.26.10", "@commitlint/cli": "^19.2.2", "@commitlint/config-conventional": "^19.2.2", "@dev-plugins/react-query": "^0.3.1", "@expo/config": "~11.0.10", "@testing-library/jest-dom": "^6.5.0", "@testing-library/react-native": "^12.7.2", "@types/i18n-js": "^3.8.9", "@types/jest": "^29.5.12", "@types/lodash.memoize": "^4.1.9", "@types/react": "catalog:react19", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "babel-plugin-module-resolver": "^5.0.2", "cross-env": "^7.0.3", "dotenv": "^16.4.5", "eslint": "^9.24.0", "eslint-config-expo": "^9.2.0", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-typescript": "^3.6.3", "eslint-plugin-i18n-json": "^4.0.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-react-compiler": "19.0.0-beta-a7bf2bd-20241110", "eslint-plugin-simple-import-sort": "^10.0.0", "eslint-plugin-tailwindcss": "^3.15.2", "eslint-plugin-testing-library": "^7.2.0", "eslint-plugin-unicorn": "^46.0.1", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-expo": "~53.0.9", "jest-junit": "^16.0.0", "lint-staged": "^15.2.9", "np": "^10.0.7", "prettier": "^3.3.3", "tailwindcss": "catalog:", "ts-jest": "^29.1.2", "typescript": "catalog:"}, "repository": {"type": "git", "url": "git+https://github.com/user/repo-name.git"}, "packageManager": "pnpm@9.12.3", "expo": {"doctor": {"reactNativeDirectoryCheck": {"listUnknownPackages": false, "exclude": ["react-native-restart"]}}, "install": {"exclude": ["eslint-config-expo"]}}, "osMetadata": {"initVersion": "7.0.4"}}