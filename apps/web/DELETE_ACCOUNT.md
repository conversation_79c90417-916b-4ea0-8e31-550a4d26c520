# Delete Account Functionality

## Overview

The delete account functionality allows users to request permanent deletion of their account and all associated data. This is a two-step process:

1. User submits a deletion request with their phone number and reason (with hCaptcha verification)
2. Admin team reviews and approves the request (can take up to 2 weeks)

## Environment Variables

The project uses `@t3-oss/env-nextjs` for type-safe environment variable validation. Add the following environment variables to your `.env.local` file:

```bash
# hCaptcha Configuration (Required)
NEXT_PUBLIC_HCAPTCHA_SITE_KEY=your_hcaptcha_site_key_here
HCAPTCHA_SECRET_KEY=your_hcaptcha_secret_key_here

# Database Configuration (Optional)
POSTGRES_URL=your_database_url_here
```

### Getting hCaptcha Keys

1. Sign up at [hCaptcha](https://www.hcaptcha.com/)
2. Create a new site
3. Copy the Site Key and Secret Key
4. Add them to your environment variables

### Environment Validation

The environment variables are validated at runtime using Zod schemas:
- `NEXT_PUBLIC_HCAPTCHA_SITE_KEY` - Required for frontend hCaptcha widget
- `HC<PERSON>TCHA_SECRET_KEY` - Required for backend verification
- `POSTGRES_URL` - Optional for database operations

## Database Schema

The deletion requests are stored in the `account_deletion_requests` table with the following structure:

```sql
CREATE TABLE account_deletion_requests (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  phone TEXT NOT NULL,
  reason TEXT NOT NULL,
  status TEXT DEFAULT 'PENDING' CHECK (status IN ('PENDING', 'APPROVED', 'REJECTED')),
  admin_notes TEXT,
  ip_address TEXT,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## API Endpoints

### POST /api/delete-account

Submits a new account deletion request.

**Request Body:**
```json
{
  "phone": "string (required)",
  "reason": "string (required)",
  "hCaptchaToken": "string (required)"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Account deletion request submitted successfully",
  "requestId": "uuid"
}
```

## Frontend Implementation

### Page: `/delete-account`

- **Form Fields:**
  - Phone Number (required)
  - Reason for Deletion (required, textarea)
  - hCaptcha verification (required)
  
- **Features:**
  - Form validation
  - hCaptcha integration for spam prevention
  - Success/error notifications
  - Clear warnings about permanent deletion
  - Information about 2-week processing time
  - IP address and user agent tracking
  
- **UI Components Used:**
  - Card, Button, Input, Textarea
  - Alert components for warnings
  - Toast notifications
  - hCaptcha widget

## Admin Workflow

1. **Review Requests:** Admins can view pending deletion requests
2. **Approve/Reject:** Update request status with admin notes
3. **Data Cleanup:** When approved, permanently delete user data
4. **Notification:** Notify user of approval/rejection

## Security Considerations

- All requests are logged for audit purposes with IP address and user agent
- hCaptcha verification prevents automated spam submissions
- Phone number verification may be required
- Admin approval prevents accidental deletions
- 2-week processing time allows for reconsideration
- No login required, making it accessible for users who can't access their accounts

## TODO Items

- [x] Implement actual database integration
- [x] Create admin dashboard for managing requests
- [ ] Add email notifications
- [ ] Implement phone number verification
- [ ] Add data export before deletion (GDPR compliance)
- [ ] Create automated cleanup scripts
- [ ] Add rate limiting to prevent abuse
- [ ] Implement phone number format validation

## Files Created/Modified

### Web App
- `apps/web/app/delete-account/page.tsx` - Frontend page with hCaptcha integration
- `apps/web/app/api/delete-account/route.ts` - API endpoint with captcha verification and database integration
- `apps/web/app/page.tsx` - Added navigation link
- `apps/web/package.json` - Added hCaptcha and env dependencies
- `apps/web/env.ts` - Type-safe environment configuration
- `apps/web/DELETE_ACCOUNT.md` - This documentation

### Admin Panel
- `apps/admin/src/app/(with-layout)/deletion-requests/page.tsx` - Admin page for viewing deletion requests
- `apps/admin/src/app/(with-layout)/deletion-requests/columns.tsx` - Table columns definition
- `apps/admin/src/app/(with-layout)/deletion-requests/data-table-row-actions.tsx` - Row actions component
- `apps/admin/src/app/(with-layout)/deletion-requests/deletion-requests-table.tsx` - Main table component
- `apps/admin/src/server/api/routers/deletion-requests.ts` - tRPC router for deletion requests
- `apps/admin/src/server/api/root.ts` - Added deletion requests router
- `apps/admin/src/components/app-sidebar.tsx` - Added sidebar navigation item

### Database
- `packages/db/drizzle/schemas/user-profile-schema.ts` - Database schema (removed appwriteUserId)

## Dependencies Added

- `@hcaptcha/react-hcaptcha` - React component for hCaptcha integration
- `@t3-oss/env-nextjs` - Type-safe environment variable validation 