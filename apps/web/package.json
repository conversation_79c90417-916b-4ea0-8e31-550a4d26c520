{"name": "thedal-web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hcaptcha/react-hcaptcha": "^1.9.0", "@t3-oss/env-nextjs": "catalog:", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "catalog:", "@radix-ui/react-alert-dialog": "catalog:", "@radix-ui/react-aspect-ratio": "catalog:", "@radix-ui/react-avatar": "catalog:", "@radix-ui/react-checkbox": "catalog:", "@radix-ui/react-collapsible": "catalog:", "@radix-ui/react-context-menu": "catalog:", "@radix-ui/react-dialog": "catalog:", "@radix-ui/react-dropdown-menu": "catalog:", "@radix-ui/react-hover-card": "catalog:", "@radix-ui/react-label": "catalog:", "@radix-ui/react-menubar": "catalog:", "@radix-ui/react-navigation-menu": "catalog:", "@radix-ui/react-popover": "catalog:", "@radix-ui/react-progress": "catalog:", "@radix-ui/react-radio-group": "catalog:", "@radix-ui/react-scroll-area": "catalog:", "@radix-ui/react-select": "catalog:", "@radix-ui/react-separator": "catalog:", "@radix-ui/react-slider": "catalog:", "@radix-ui/react-slot": "catalog:", "@radix-ui/react-switch": "catalog:", "@radix-ui/react-tabs": "catalog:", "@radix-ui/react-toast": "catalog:", "@radix-ui/react-toggle": "catalog:", "@radix-ui/react-toggle-group": "catalog:", "@radix-ui/react-tooltip": "catalog:", "@repo/db": "workspace:*", "autoprefixer": "catalog:", "class-variance-authority": "catalog:", "clsx": "catalog:", "cmdk": "catalog:", "date-fns": "catalog:", "embla-carousel-react": "catalog:", "input-otp": "catalog:", "lucide-react": "catalog:", "next": "catalog:", "next-themes": "catalog:", "react": "catalog:react19", "react-day-picker": "catalog:", "react-dom": "catalog:react19", "react-hook-form": "catalog:", "react-resizable-panels": "catalog:", "recharts": "catalog:", "sonner": "catalog:", "tailwind-merge": "catalog:", "tailwindcss-animate": "catalog:", "vaul": "catalog:", "zod": "catalog:", "@tailwindcss/typography": "latest"}, "devDependencies": {"@types/node": "^22", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "postcss": "^8", "tailwindcss": "catalog:", "typescript": "catalog:"}}