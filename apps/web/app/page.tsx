import Link from "next/link"
import Image from "next/image"
import { <PERSON>e, Shield, Heart, Award, ChevronRight, Menu } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"

export default function LandingPage() {
  return (
    <main>
      {/* Hero Section */}
      <section className="py-20 md:py-28">
        <div className="container mx-auto px-4 grid md:grid-cols-2 gap-12 items-center">
          <div className="space-y-6">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold leading-tight">
              Safe <span className="text-primary">Flight Companionship</span> for Those Who Need It
            </h1>
            <p className="text-xl text-gray-400 max-w-md">
              Providing trusted companions for elderly and individuals with special needs who require assistance
              during air travel.
            </p>
            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="px-8">
                Book a Companion
                <ChevronRight className="ml-2 h-4 w-4" />
              </Button>
              <Button size="lg" variant="outline" className="px-8">
                Learn More
              </Button>
            </div>
          </div>
          <div className="relative h-[400px] rounded-xl overflow-hidden">
            <Image
              src="/placeholder.svg?height=800&width=600"
              alt="Caregiver assisting elderly person at airport"
              fill
              className="object-cover"
              priority
            />
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">How We Help</h2>
            <p className="text-xl text-gray-400 max-w-2xl mx-auto">
              Our trained companions provide personalized assistance for a stress-free flying experience.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gray-800 p-8 rounded-xl">
              <Plane className="h-12 w-12 text-primary mb-4" />
              <h3 className="text-xl font-bold mb-2">Airport Navigation</h3>
              <p className="text-gray-400">
                Assistance with check-in, security, finding gates, and navigating busy terminals with ease.
              </p>
            </div>

            <div className="bg-gray-800 p-8 rounded-xl">
              <Shield className="h-12 w-12 text-primary mb-4" />
              <h3 className="text-xl font-bold mb-2">Vetted Companions</h3>
              <p className="text-gray-400">
                All companions undergo thorough background checks and training in elderly and special needs care.
              </p>
            </div>

            <div className="bg-gray-800 p-8 rounded-xl">
              <Heart className="h-12 w-12 text-primary mb-4" />
              <h3 className="text-xl font-bold mb-2">Medical Awareness</h3>
              <p className="text-gray-400">
                Companions are trained to understand medical needs, medication schedules, and emergency procedures.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works */}
      <section id="how-it-works" className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">How It Works</h2>
            <p className="text-xl text-gray-400 max-w-2xl mx-auto">
              Booking a flight companion is simple and straightforward.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="bg-primary/20 text-primary h-16 w-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold">1</span>
              </div>
              <h3 className="text-xl font-bold mb-2">Tell Us Your Needs</h3>
              <p className="text-gray-400">
                Share your travel details, special requirements, and any medical considerations we should know about.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-primary/20 text-primary h-16 w-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold">2</span>
              </div>
              <h3 className="text-xl font-bold mb-2">Match with a Companion</h3>
              <p className="text-gray-400">
                We'll pair you with a qualified companion who has experience with your specific needs.
              </p>
            </div>

            <div className="text-center">
              <div className="bg-primary/20 text-primary h-16 w-16 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl font-bold">3</span>
              </div>
              <h3 className="text-xl font-bold mb-2">Travel with Confidence</h3>
              <p className="text-gray-400">
                Your companion will meet you at the departure airport and stay with you until you're safely at your
                destination.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section id="testimonials" className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">What Our Clients Say</h2>
            <p className="text-xl text-gray-400 max-w-2xl mx-auto">
              Hear from families who have trusted thedal for their loved ones' travel needs.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gray-800 p-8 rounded-xl">
              <div className="flex items-center mb-4">
                <div className="h-12 w-12 rounded-full bg-gray-700 mr-4"></div>
                <div>
                  <h4 className="font-bold">Margaret J.</h4>
                  <p className="text-gray-400 text-sm">83 years old, traveled to visit family</p>
                </div>
              </div>
              <p className="text-gray-300">
                "I was nervous about flying alone after my hip replacement. My thedal companion made everything so
                easy—from handling my luggage to making sure I had time for bathroom breaks. I couldn't have made the
                trip to see my grandchildren without this service."
              </p>
            </div>

            <div className="bg-gray-800 p-8 rounded-xl">
              <div className="flex items-center mb-4">
                <div className="h-12 w-12 rounded-full bg-gray-700 mr-4"></div>
                <div>
                  <h4 className="font-bold">Robert T.</h4>
                  <p className="text-gray-400 text-sm">Son of 90-year-old traveler</p>
                </div>
              </div>
              <p className="text-gray-300">
                "My father has early-stage dementia but wanted to attend his brother's 95th birthday. We were worried
                about him traveling alone, but his thedal companion was amazing. They kept him oriented and
                comfortable throughout the journey. Worth every penny for our peace of mind."
              </p>
            </div>

            <div className="bg-gray-800 p-8 rounded-xl">
              <div className="flex items-center mb-4">
                <div className="h-12 w-12 rounded-full bg-gray-700 mr-4"></div>
                <div>
                  <h4 className="font-bold">Aisha K.</h4>
                  <p className="text-gray-400 text-sm">Traveler with visual impairment</p>
                </div>
              </div>
              <p className="text-gray-300">
                "As someone with a visual impairment, airports can be overwhelming. My companion from thedal was
                patient, respectful, and made sure I knew what was happening around me at all times. They described
                everything and helped me navigate with dignity."
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <div className="max-w-3xl mx-auto bg-gradient-to-r from-primary/20 to-purple-900/20 p-12 rounded-2xl">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Ready to Travel with Confidence?</h2>
            <p className="text-xl text-gray-400 mb-8 max-w-xl mx-auto">
              Don't let mobility issues or health concerns keep you or your loved ones from traveling. Our companions
              are ready to help.
            </p>
            <Button size="lg" className="px-8">
              Book a Companion
              <ChevronRight className="ml-2 h-4 w-4" />
            </Button>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20 bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Our Services</h2>
            <p className="text-xl text-gray-400 max-w-2xl mx-auto">
              Comprehensive assistance tailored to individual needs.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <div className="bg-gray-800 p-8 rounded-xl">
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <Award className="h-6 w-6 mr-2 text-primary" />
                Standard Assistance
              </h3>
              <ul className="space-y-2 text-gray-300">
                <li>• Airport check-in and security navigation</li>
                <li>• Assistance with carry-on luggage</li>
                <li>• Gate location and boarding support</li>
                <li>• In-flight comfort checks</li>
                <li>• Connection assistance</li>
                <li>• Arrival support until meeting family/transportation</li>
              </ul>
            </div>

            <div className="bg-gray-800 p-8 rounded-xl">
              <h3 className="text-xl font-bold mb-4 flex items-center">
                <Award className="h-6 w-6 mr-2 text-primary" />
                Premium Care
              </h3>
              <ul className="space-y-2 text-gray-300">
                <li>• All Standard Assistance services</li>
                <li>• Medication reminders and assistance</li>
                <li>• Wheelchair or mobility aid management</li>
                <li>• Special meal arrangements</li>
                <li>• Bathroom assistance when needed</li>
                <li>• Communication with family throughout journey</li>
                <li>• Door-to-door service coordination</li>
              </ul>
            </div>
          </div>
        </div>
      </section>
    </main>
  )
}

