import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { env } from "@/env";
import { db } from "@repo/db";
import { accountDeletionRequests } from "@repo/db";

// Validation schema for the request body
const deleteAccountSchema = z.object({
  phone: z.string().min(1, "Phone number is required"),
  reason: z.string().min(1, "Reason is required"),
  hCaptchaToken: z.string().min(1, "hCaptcha verification is required"),
});

// hCaptcha verification function
async function verifyHCaptcha(token: string, ip: string): Promise<boolean> {
  try {
    const response = await fetch("https://hcaptcha.com/siteverify", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        secret: env.HCAPTCHA_SECRET_KEY,
        response: token,
        remoteip: ip,
      }),
    });

    const data = await response.json();
    return data.success === true;
  } catch (error) {
    console.error("hCaptcha verification error:", error);
    return false;
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the request body
    const validatedData = deleteAccountSchema.parse(body);
    
    // Get client IP address
    const ip = request.headers.get("x-forwarded-for") || 
               request.headers.get("x-real-ip") || 
               "unknown";
    
    // Verify hCaptcha
    const isCaptchaValid = await verifyHCaptcha(validatedData.hCaptchaToken, ip);
    if (!isCaptchaValid) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Captcha verification failed. Please try again." 
        },
        { status: 400 }
      );
    }
    
    // Get user agent
    const userAgent = request.headers.get("user-agent") || "unknown";
    
    // Insert the deletion request into the database
    const [deletionRequest] = await db.insert(accountDeletionRequests).values({
      phone: validatedData.phone,
      reason: validatedData.reason,
      status: "PENDING",
      ipAddress: ip,
      userAgent: userAgent,
    }).returning();
    
    console.log("Account deletion request saved:", deletionRequest);
    
    // TODO: Send notification to admin team about the deletion request
    // This could be an email, Slack notification, or database entry for admin dashboard
    
    return NextResponse.json(
      { 
        success: true, 
        message: "Account deletion request submitted successfully",
        requestId: deletionRequest.id 
      },
      { status: 200 }
    );
    
  } catch (error) {
    console.error("Error processing delete account request:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { 
          success: false, 
          message: "Invalid request data",
          errors: error.errors 
        },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { 
        success: false, 
        message: "Internal server error" 
      },
      { status: 500 }
    );
  }
} 