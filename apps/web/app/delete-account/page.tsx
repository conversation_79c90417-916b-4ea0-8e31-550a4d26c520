"use client";

import { useState, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, Info, Trash2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import HCaptcha from "@hcaptcha/react-hcaptcha";
import { env } from "@/env";

export default function DeleteAccountPage() {
  const [phone, setPhone] = useState("");
  const [reason, setReason] = useState("");
  const [captchaToken, setCaptchaToken] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const captchaRef = useRef<HCaptcha>(null);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!phone.trim() || !reason.trim()) {
      toast({
        title: "Validation Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
      return;
    }

    if (!captchaToken) {
      toast({
        title: "Captcha Required",
        description: "Please complete the captcha verification.",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      const response = await fetch("/api/delete-account", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          phone: phone.trim(),
          reason: reason.trim(),
          hCaptchaToken: captchaToken,
        }),
      });

      const data = await response.json();

      if (response.ok) {
        setIsSubmitted(true);
        toast({
          title: "Request Submitted",
          description: "Your account deletion request has been submitted successfully.",
        });
      } else {
        throw new Error(data.message || "Failed to submit request");
      }
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to submit your request. Please try again.",
        variant: "destructive",
      });
      // Reset captcha on error
      captchaRef.current?.resetCaptcha();
      setCaptchaToken("");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCaptchaChange = (token: string) => {
    setCaptchaToken(token);
  };

  const handleCaptchaExpire = () => {
    setCaptchaToken("");
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <Info className="w-6 h-6 text-green-600" />
            </div>
            <CardTitle className="text-xl">Request Submitted</CardTitle>
            <CardDescription>
              Your account deletion request has been submitted successfully.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                Your account will be deleted once the request is approved. This process can take up to 2 weeks.
              </AlertDescription>
            </Alert>
            <Button 
              onClick={() => window.location.href = "/"} 
              className="w-full"
            >
              Return to Home
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8 flex flex-col md:flex-row items-center md:items-stretch justify-between gap-8">
      {/* Left: Info/Graphics (hidden on mobile) */}
      <div className="hidden md:flex flex-col justify-center w-1/2 pr-8">
        <div className=" p-8 flex flex-col items-center text-center shadow-lg">
          <div className="mb-6">
            <svg width="80" height="80" viewBox="0 0 80 80" fill="none">
              <circle cx="40" cy="40" r="40" fill="#FEE2E2"/>
              <path d="M40 25V45" stroke="#DC2626" strokeWidth="3" strokeLinecap="round"/>
              <circle cx="40" cy="55" r="2.5" fill="#DC2626"/>
            </svg>
          </div>
          <h2 className="text-2xl font-bold mb-2 text-red-700">Are you sure you want to leave?</h2>
          <p className="text-gray-300 mb-4">
            Deleting your account is permanent and cannot be undone. You will lose:
          </p>
          <ul className="text-left text-gray-300 space-y-2 mb-4">
            <li className="flex items-center">
              <span className="mr-2">
                <svg width="20" height="20" fill="none"><circle cx="10" cy="10" r="10" fill="#F87171"/><path d="M7 10l2 2 4-4" stroke="#fff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
              </span>
              All your bookings and travel history
            </li>
            <li className="flex items-center">
              <span className="mr-2">
                <svg width="20" height="20" fill="none"><circle cx="10" cy="10" r="10" fill="#FBBF24"/><path d="M7 10l2 2 4-4" stroke="#fff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
              </span>
              Your profile and preferences
            </li>
            <li className="flex items-center">
              <span className="mr-2">
                <svg width="20" height="20" fill="none"><circle cx="10" cy="10" r="10" fill="#60A5FA"/><path d="M7 10l2 2 4-4" stroke="#fff" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/></svg>
              </span>
              Messages and connections
            </li>
          </ul>
          <div className="text-gray-500 text-sm">
            <span className="font-semibold">Need help?</span> 
            <br />
            Contact <a href="mailto:<EMAIL>" className="text-primary underline"><EMAIL></a> for assistance or to discuss your concerns.
          </div>
        </div>
      </div>
      {/* Right: Delete Account Form */}
      <div className="w-full md:w-1/2 flex items-center">
        <Card className="w-full">
          <CardHeader className="text-center">
            <div className="mx-auto w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-4">
              <Trash2 className="w-6 h-6 text-red-600" />
            </div>
            <CardTitle className="text-xl">Delete Account</CardTitle>
            <CardDescription>
              Request to permanently delete your account and all associated data
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <Alert className="border-orange-200 bg-orange-50">
                <AlertTriangle className="h-4 w-4 text-orange-600" />
                <AlertDescription className="text-orange-800">
                  This action cannot be undone. All your data, bookings, and account information will be permanently deleted.
                </AlertDescription>
              </Alert>

              <div className="space-y-2">
                <label htmlFor="phone" className="text-sm font-medium">
                  Phone Number *
                </label>
                <Input
                  id="phone"
                  type="tel"
                  placeholder="Enter your phone number"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                  required
                />
              </div>

              <div className="space-y-2">
                <label htmlFor="reason" className="text-sm font-medium">
                  Reason for Deletion *
                </label>
                <Textarea
                  id="reason"
                  placeholder="Please tell us why you want to delete your account..."
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                  rows={4}
                  required
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Security Verification *
                </label>
                <div className="">
                  <HCaptcha
                    ref={captchaRef}
                    sitekey={env.NEXT_PUBLIC_HCAPTCHA_SITE_KEY}
                    onVerify={handleCaptchaChange}
                    onExpire={handleCaptchaExpire}
                    theme="dark"
                  />
                </div>
              </div>

              <Alert className="border-blue-200 bg-blue-50">
                <Info className="h-4 w-4 text-blue-600" />
                <AlertDescription className="text-blue-800">
                  Your account will be deleted once this request is approved by our team. This process can take up to 2 weeks.
                </AlertDescription>
              </Alert>

              <Button
                type="submit"
                variant="destructive"
                disabled={isSubmitting || !captchaToken}
                className="w-full"
              >
                {isSubmitting ? "Submitting..." : "Submit Deletion Request"}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 