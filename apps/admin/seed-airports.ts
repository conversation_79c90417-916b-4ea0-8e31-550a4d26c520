import { db } from "@repo/db";
import { airports, cities } from "@repo/db/schema";
import { eq, and } from "@repo/db";
import * as fs from "fs";
import * as path from "path";

// Read airports data from JSON file
function loadAirportsData() {
  try {
    const airportsPath = path.join(process.cwd(), "airports.json");
    const airportsData = fs.readFileSync(airportsPath, "utf8");
    return JSON.parse(airportsData);
  } catch (error) {
    console.error("Error loading airports.json:", error);
    throw error;
  }
}

async function seedAirports() {
  try {
    console.log("Starting airport and city seeding process...");

    // Load airports data from JSON file
    const airportsData = loadAirportsData();
    console.log(`Loaded ${airportsData.length} airports from JSON file`);

    // Map to track created cities to avoid duplicates
    const createdCities = new Map<string, string>();

    for (const airport of airportsData) {
      // Skip if missing essential data
      if (!airport.code || !airport.name || !airport.city || !airport.country) {
        console.log(
          `Skipping airport with incomplete data: ${airport.code || "Unknown"}`,
        );
        continue;
      }

      // Create a unique key for the city
      const cityKey = `${airport.city.trim()}-${airport.state?.trim() || ""}-${airport.country.trim()}`;

      let cityId: string;

      // Check if we've already created this city
      if (createdCities.has(cityKey)) {
        cityId = createdCities.get(cityKey)!;
        console.log(
          `Using existing city: ${airport.city}, ${airport.country} (${cityId})`,
        );
      } else {
        // Check if city already exists in the database
        const existingCities = await db
          .select()
          .from(cities)
          .where(
            and(
              eq(cities.name, airport.city),
              eq(cities.country, airport.country),
              airport.state ? eq(cities.state, airport.state) : eq(cities.state, "")
            )
          );

        if (existingCities.length > 0) {
          cityId = existingCities[0]?.id ?? "";
          console.log(
            `Found existing city in database: ${airport.city}, ${airport.country} (${cityId})`,
          );
        } else {
          // Create new city
          const cityData = {
            name: airport.city,
            state: airport.state || "",
            country: airport.country,
          };

          const newCity = await db.insert(cities).values(cityData).returning();
          cityId = newCity[0]?.id ?? "";
          if (!cityId) {
            console.error(`Failed to create city: ${airport.city}, ${airport.country}`);
            continue;
          }
          console.log(
            `Created new city: ${airport.city}, ${airport.country} (${cityId})`,
          );
        }

        // Store the city ID for future reference
        createdCities.set(cityKey, cityId);
      }

      // Create the airport with all available data
      try {
        const airportData = {
          shortCode: airport.code,
          name: airport.name,
          locationCity: cityId, // Relation to city
          // Additional fields from JSON
          lat: airport.lat ? airport.lat : null,
          lon: airport.lon ? airport.lon : null,
          city: airport.city,
          state: airport.state || "",
          country: airport.country,
          timezone: airport.tz || "",
          type: airport.type || "",
          icao: airport.icao || "",
          runwayLength: airport.runway_length || "",
          elevation: airport.elev || "",
          directFlights: airport.direct_flights || "",
          carriers: airport.carriers || "",
          phone: airport.phone || "",
          email: airport.email || "",
          url: airport.url || "",
          woeid: airport.woeid || "",
        };

        await db.insert(airports).values(airportData);
        console.log(
          `Created airport: ${airport.code} - ${airport.name}`,
        );
      } catch (error) {
        console.error(`Error creating airport ${airport.code}:`, error);
      }
    }

    console.log("Airport seeding completed successfully");
  } catch (error) {
    console.error("Error seeding airports:", error);
  }
}

// Run the seeding function
seedAirports()
  .then(() => {
    console.log("Seeding process finished");
    process.exit(0);
  })
  .catch((err) => {
    console.error("Seeding process failed:", err);
    process.exit(1);
  });
