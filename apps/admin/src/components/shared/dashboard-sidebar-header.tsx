"use client";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuItem,
  DropdownMenuGroup,
  DropdownMenuShortcut,
} from "../ui/dropdown-menu";

import { useDebounce } from "@uidotdev/usehooks";
import { Input } from "../ui/input";
import { Button } from "../ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Bell, Star } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
const SidebarHeader = () => {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const router = useRouter();
  const searchParams = useSearchParams();
  const debouncedSearchTerm = useDebounce(searchTerm, 300);

  useEffect(() => {
    const searchResults = () => {
      const params = new URLSearchParams(searchParams.toString());
      if (debouncedSearchTerm) params.set("searchTerm", debouncedSearchTerm);
      else params.delete("searchTerm");
      router.replace(`?${params}`);
    };

    searchResults();
  }, [debouncedSearchTerm]);

  return (
    <>
      <header className="mt-3 flex items-center gap-2 px-5 transition-[width,height] ease-linear group-has-[[data-collapsible=icon]]/sidebar-wrapper:h-12">
        <div className="flex items-center gap-6">
          <div className="text-lg font-semibold text-[#1A1A1A]">
            App Ratings
          </div>
          <div className="flex items-center gap-1.5">
            <Star className="size-[25px]" fill="#FFC727" stroke="#FFC727" />
            <Star className="size-[25px]" fill="#FFC727" stroke="#FFC727" />
            <Star className="size-[25px]" fill="#FFC727" stroke="#FFC727" />
            <Star className="size-[25px]" fill="#FFC727" stroke="#FFC727" />
          </div>
        </div>
        <div className="ml-auto flex items-center space-x-6">
          <Input
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            type="search"
            placeholder="Search..."
            className="md:w-[100px] lg:w-[300px]"
          />

          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <div className="rounded-[6px] bg-[#F2F2F2] p-[6.86px]">
                <Bell className="h-6 w-6" />
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">shadcn</p>
                  <p className="text-xs leading-none text-muted-foreground">
                    <EMAIL>
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                <DropdownMenuItem>
                  Profile
                  <DropdownMenuShortcut>⇧⌘P</DropdownMenuShortcut>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  Billing
                  <DropdownMenuShortcut>⌘B</DropdownMenuShortcut>
                </DropdownMenuItem>
                <DropdownMenuItem>
                  Settings
                  <DropdownMenuShortcut>⌘S</DropdownMenuShortcut>
                </DropdownMenuItem>
                <DropdownMenuItem>New Team</DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuItem>
                Log out
                <DropdownMenuShortcut>⇧⌘Q</DropdownMenuShortcut>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </header>
    </>
  );
};

export default SidebarHeader;
