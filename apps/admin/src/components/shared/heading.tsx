"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "../ui/tabs";
import { But<PERSON> } from "../ui/button";
import { CalendarDateRangePicker } from "../date-range-picker";
import StatsCard from "../stats-card";
import { usePathname } from "next/navigation";

const Heading = ({ title }: { title: string }) => {
  const pathname = usePathname();
  return (
    <>
      <div className="flex-1 space-y-4 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">{title}</h2>
          <div className="flex items-center space-x-2">
            <CalendarDateRangePicker />
            <Button className="bg-primary-main text-white">Download</Button>
          </div>
        </div>
        {pathname === "/" && (
          <Tabs defaultValue="today" className="space-y-4">
            <TabsList>
              <TabsTrigger value="today">Today</TabsTrigger>
              <TabsTrigger value="weekly"> Weekly</TabsTrigger>
              <TabsTrigger value="monthly"> Monthly </TabsTrigger>
              <TabsTrigger value="yearly"> Yearly </TabsTrigger>
              <TabsTrigger value="all"> All </TabsTrigger>
            </TabsList>
            <TabsContent value="today" className="space-y-4">
              <StatsCard />
            </TabsContent>
          </Tabs>
        )}
      </div>
    </>
  );
};

export default Heading;
