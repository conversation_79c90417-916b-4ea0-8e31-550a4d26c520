"use client"

import { Home, type LucideIcon } from "lucide-react"

import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"

export function NavMain({
  items,
}: {
  items: {
    title: string
    icon?: LucideIcon
    isActive?: boolean
    items?: {
      title: string
      url: string
      icon?:LucideIcon
    }[]
  }[]
}) {
  return (
    <SidebarGroup>
      <SidebarGroupLabel className="flex items-center gap-2">
        <Home className="text-[#071952]"/>
        <span className="text-[#071952] font-jakartaSans font-bold text-sm">Dashboard</span>
      </SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => (
          <SidebarMenuItem key={item.title}>
            <SidebarMenuButton 
              tooltip={item.title}
              className="text-[#A1A1AA] font-jakartaSans font-medium text-[11px] leading-[18px] hover:text-[#071952]"
            >
              {item.icon && <item.icon className="text-[#071952]" />}
              <span>{item.title}</span>
            </SidebarMenuButton>
            {item.items && (
              <SidebarMenuSub>
                {item.items.map((subItem) => (
                  <SidebarMenuSubItem key={subItem.title}>
                    <SidebarMenuSubButton asChild>
                      <a href={subItem.url} className="hover:text-[#071952]">
                        {subItem.icon && <subItem.icon className="text-[#4D4D4D]" />}
                        <span className="font-jakartaSans text-[13px] text-[#4D4D4D] leading-[21px] hover:text-[#071952]">
                          {subItem.title}
                        </span>
                      </a>
                    </SidebarMenuSubButton>
                  </SidebarMenuSubItem>
                ))}
              </SidebarMenuSub>
            )}
          </SidebarMenuItem>
        ))}
      </SidebarMenu>
    </SidebarGroup>
  )
}
