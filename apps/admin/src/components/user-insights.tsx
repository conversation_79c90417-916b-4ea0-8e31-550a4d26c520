'use client'
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>, ResponsiveContainer, Label } from "recharts"

import { Card,CardContent,CardDescription,CardHeader,CardTitle } from "./ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "./ui/chart";
const data = [
    { name: "Users", value: 456000 },
    { name: "Companions", value: 1000 },
    { name: "Daily Visitors", value: 1900 },
    { name: "Both Accounts", value: 1570000 }
  ]
  const COLORS = ['#3b82f6', '#f97316', '#22c55e', '#eab308']
  const chartData = [
    { browser: "chrome", visitors: 275, fill: "var(--color-chrome)" },
    { browser: "safari", visitors: 200, fill: "var(--color-safari)" },
    { browser: "firefox", visitors: 287, fill: "var(--color-firefox)" },
    { browser: "edge", visitors: 173, fill: "var(--color-edge)" },
    { browser: "other", visitors: 190, fill: "var(--color-other)" },
  ]
  const chartConfig = {
    visitors: {
      label: "Visitors",
    },
    chrome: {
      label: "Chrome",
      color: "hsl(var(--chart-1))",
    },
    safari: {
      label: "Safari",
      color: "hsl(var(--chart-2))",
    },
    firefox: {
      label: "Firefox",
      color: "hsl(var(--chart-3))",
    },
    edge: {
      label: "Edge",
      color: "hsl(var(--chart-4))",
    },
    other: {
      label: "Other",
      color: "hsl(var(--chart-5))",
    },
  } 
const UserInsights=()=>{
    return(
        <>
        <Card className="w-5/12">
                  <CardHeader>
                    <CardTitle>User Insights</CardTitle>
                    <CardDescription>
                    Increased from last month 3400.
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                  <ChartContainer
          config={chartConfig}
          className="mx-auto aspect-square max-h-[350px]"
        >
          <PieChart>
            <ChartTooltip
              cursor={false}
              content={<ChartTooltipContent hideLabel />}
            />
            <Pie
              data={chartData}
              dataKey="visitors"
              nameKey="browser"
              innerRadius={110}
              strokeWidth={8}
            >
              <Label
                content={({ viewBox }) => {
                  if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                    return (
                      <text
                        x={viewBox.cx}
                        y={viewBox.cy}
                        textAnchor="middle"
                        dominantBaseline="middle"
                      >
                        <tspan
                          x={viewBox.cx}
                          y={viewBox.cy}
                          className="fill-foreground text-3xl font-bold"
                        >
                          {`22.870k`}
                        </tspan>
                        <tspan
                          x={viewBox.cx}
                          y={(viewBox.cy || 0) + 24}
                          className="fill-muted-foreground whitespace-pre-wrap"
                        >
                          Visitors this year on application
                        </tspan>
                      </text>
                    )
                  }
                }}
              />
            </Pie>
          </PieChart>
        </ChartContainer>
        <div className="mt-4 mb-4 flex flex-col space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
            <span className="h-3 w-3 rounded-full bg-blue-500 mr-2"></span>
            <span>Users</span>
            </div>
            <div className="flex justify-end font-bold text-[#3A3A3A]"> 456k</div>
          </div>
          <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="h-3 w-3 rounded-full bg-orange-500 mr-2"></span>
            <span>Companion</span>
            </div>
            <div className="flex justify-end font-bold text-[#3A3A3A">1k</div>

          </div>
          <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="h-3 w-3 rounded-full bg-green-500 mr-2"></span>
            <span>Daily Visitors</span>
            </div>
            <div className="flex justify-end font-bold text-[#3A3A3A">1.9k</div>

          </div>
          <div className="flex items-center justify-between">
          <div className="flex items-center">
            <span className="h-3 w-3 rounded-full bg-yellow-500 mr-2"></span>
            <span>Users who have both accounts</span>
          </div>
          <div className="flex justify-end font-bold text-[#3A3A3A">15.7M</div>

        </div>
        </div>
                  </CardContent>
                </Card>
        </>
    )
}

export default UserInsights;