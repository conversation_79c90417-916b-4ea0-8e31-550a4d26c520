"use client"

import { Table } from "@tanstack/react-table"
import { X } from "lucide-react"
import { Input } from "./ui/input"
import { Button } from "./ui/button"
import { DataTableFacetedFilter } from "./data-table-faceted-filter"
import { priorities} from "./data"
import { DataTableViewOptions } from "./data-table-view-options"


interface DataTableToolbarProps<TData> {
  table: Table<TData>
}
export const statuses = [
  {
    value: "upcoming",
    label: "Upcoming",
   
  },
  {
    value: "ongoing",
    label: "Ongoing",
  },
  {
    value: "past",
    label: "Past",
  },

]
export const nationality=[
  {
    value: "indian",
    label: "Indian",
   
  },
  {
    value: "china",
    label: "China",
   
  },
  {
    value: "nepal",
    label: "Nepal",
  },
]

export function DataTableToolbar<TData>({
  table,
}: DataTableToolbarProps<TData>) {
  const isFiltered = table.getState().columnFilters.length > 0

  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <Input
          placeholder="Filter tasks..."
          value={(table.getColumn("title")?.getFilterValue() as string) ?? ""}
          onChange={(event) =>
            table.getColumn("title")?.setFilterValue(event.target.value)
          }
          className="h-8 w-[150px] lg:w-[250px]"
        />
        {table.getColumn("flightBookingStatus") && (
          <DataTableFacetedFilter
            column={table.getColumn("flightBookingStatus")}
            title="Status"
            options={statuses}
          />
        )}
        {table.getColumn("name") && (
          <DataTableFacetedFilter
            column={table.getColumn("name")}
            title="Nationality"
            options={nationality}
          />
        )}
        {isFiltered && (
          <Button
            variant="ghost"
            onClick={() => table.resetColumnFilters()}
            className="h-8 px-2 lg:px-3"
          >
            Reset
            <X />
          </Button>
        )}
      </div>
      <DataTableViewOptions table={table} />
    </div>
  )
}
