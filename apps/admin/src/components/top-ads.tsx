import { But<PERSON> } from "./ui/button";
import { <PERSON><PERSON><PERSON><PERSON>, CardTitle,CardDescription,Card,CardContent } from "./ui/card";
import { Users, Gift } from 'lucide-react'
const TopAds=()=>{
    const ads = [
        { id: 1, likes: 4950, redeems: 380 },
        { id: 2, likes: 3200, redeems: 275 },
        { id: 3, likes: 5100, redeems: 420 },
        { id: 4, likes: 2800, redeems: 310 },
      ]
    return(
        <>
                <Card className="">
                    <div className="flex items-center justify-between mb-2">
        <CardHeader>
          
            <CardTitle>Top Ads</CardTitle>
            <CardDescription>
              Based on no. of clicks and redeems
            </CardDescription>
            </CardHeader>
          <div className="pr-6">
          <Button variant="outline" className="bg-white">
            View All
          </Button>
          </div>
          </div>
       
        <CardContent className="">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {ads.map((ad) => (
              <Card key={ad.id} className="overflow-hidden border-none">
                <CardContent className="p-0 ">
                  <img
                    src={`banner.png`}
                    alt={`Ad ${ad.id}`}
                    className="w-full object-cover"
                  />
                  <div className="p-4">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-2">
                        <Users size={16} className="text-[#1F7A5F]" />
                        <span className="text-sm text-[#1F7A5F] font-semibold">{ad.likes.toLocaleString()} likes</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Users size={16} className="text-[#1F7A5F]" />
                        <span className="text-sm text-[#1F7A5F] font-semibold">{ad.redeems.toLocaleString()} redeem</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </CardContent>
      </Card>

        </>
    )
}

export default TopAds;