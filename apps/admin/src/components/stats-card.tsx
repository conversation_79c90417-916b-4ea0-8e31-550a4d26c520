import { Activity, DollarSign, PanelTop, Users } from "lucide-react";
import { <PERSON>Title,CardContent,Card,CardDescription,CardFooter,CardHeader } from "../components/ui/card";
import Image from "next/image";
const data=[
  {
    title:"Total User",
    icon:Users,
    value:"3.56 Lakh",
     subText:"Compare to last month (2.4lkh)"
  },
  {
    title:"Total Trips",
    icon:PanelTop,
    value:"45",
     subText:"Compare to last month (100)"
  },
  {
    title:"Total Revenue",
    icon:DollarSign,
    value:"$ 45,000",
     subText:"Compare to last month (100)"
  },
  {
    title:"Average Time Spent",
    icon: Activity,
    value:"45 min",
     subText:"Compare to last month (100)"
  }
]
const StatsCard=()=>{
    return(
        <>
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {data.map((item,index)=>(
            <Card key={index}>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-[#333333] text-sm font-medium ">
                      {item.title}
                    </CardTitle>
                    <item.icon className="text-[#4D4D4D]"/>
                   
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold text-primary-main">{item.value}</div>
                    <p className="text-xs text-muted-foreground">
                      {item.subText}
                    </p>
                  </CardContent>
                </Card>
          ))}
                
               
              </div>
        </>
    )
}
export default StatsCard