"use client";

import * as React from "react";
import {
  Users,
  Plane,
  UserCheck,
  CreditCard,
  Settings,
  BarChart3,
  MessageSquare,
  Bell,
  Share2,
  FileText,
  Calendar,
  DollarSign,
  Star,
  Globe,
  MapPin,
  Building,
  Megaphone,
  Home,
  Trash2,
  Gift,
} from "lucide-react";
import Image from "next/image";

import { NavMain } from "@/components/nav-main";
import { NavUser } from "@/components/nav-user";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
} from "@/components/ui/sidebar";

// Reorganized data with APP CONFIG at the top and relevant icons
const data = {
  navMain: [
    {
      title: "APP CONFIG",
      icon: Settings,
      isActive: true,
      items: [
        {
          title: "Language",
          url: "/app-config/language",
          icon: Globe,
        },
        {
          title: "Airports",
          url: "/app-config/airports",
          icon: Plane,
        },
        {
          title: "Cities",
          url: "/app-config/cities",
          icon: MapPin,
        },
        {
          title: "Promotions",
          url: "/app-config/promotions",
          icon: Gift,
        },
        // {
        //   title: "Airline",
        //   url: "#",
        //   icon: Building,
        // },
        // {
        //   title: "Ads Section",
        //   url: "#",
        //   icon: Megaphone,
        // },
        {
          title: "Reports",
          url: "#",
          icon: FileText,
        },
      ],
    },
    {
      title: "USER INSIGHTS",
      icon: Users,
      items: [
        {
          title: "User Data",
          url: "/user-insights/user-data",
          icon: BarChart3,
        },
        {
          title: "Leaderboard",
          url: "/user-insights/leader-board",
          icon: Star,
        },
        {
          title: "Deletion Requests",
          url: "/user-insights/deletion-requests",
          icon: Trash2,
        },
        {
          title: "Chats",
          url: "#",
          icon: MessageSquare,
        },
        {
          title: "Notifications",
          url: "#",
          icon: Bell,
        },
        {
          title: "Referrals",
          url: "#",
          icon: Share2,
        },
      ],
    },
    {
      title: "TRIP MANAGEMENT",
      icon: Plane,
      items: [
        {
          title: "Bookings",
          url: "/trip-management/bookings",
          icon: Calendar,
        },
        {
          title: "Reports",
          url: "#",
          icon: FileText,
        },
        {
          title: "Payments & Charges",
          url: "#",
          icon: DollarSign,
        },
      ],
    },
    {
      title: "COMPANION MANAGEMENT",
      icon: UserCheck,
      items: [
        {
          title: "Companion Info",
          url: "#",
          icon: Users,
        },
        {
          title: "Earnings",
          url: "#",
          icon: DollarSign,
        },
        {
          title: "Ratings & Complaints",
          url: "#",
          icon: Star,
        },
      ],
    },
    {
      title: "PAYMENTS",
      icon: CreditCard,
      items: [
        {
          title: "Reports",
          url: "#",
          icon: FileText,
        },
      ],
    },
  ],
};

export function AppSidebar({
  user,
  ...props
}: React.ComponentProps<typeof Sidebar> & {
  user: { name: string; email: string; avatar: string };
}) {
  return (
    <Sidebar collapsible="none" {...props}>
      <SidebarHeader>
        <div className="relative ml-2 h-[51px] w-[76px]">
          <Image src={"/thedal-logo.png"} alt="thedal-logo" fill={true} />
        </div>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={user} />
      </SidebarFooter>
    </Sidebar>
  );
}
