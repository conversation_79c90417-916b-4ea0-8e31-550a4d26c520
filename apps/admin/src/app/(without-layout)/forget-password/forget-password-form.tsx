"use client";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useState } from "react";
import { authClient } from "@/lib/auth-client";

const zodValidation = z.object({
  email: z.string().email(),
});

export function ForgetPasswordForm() {
  const [isLoading, setIsLoading] = useState(false);
  const form = useForm<z.infer<typeof zodValidation>>({
    resolver: zodResolver(zodValidation),
    defaultValues: {
      email: "",
    },
  });

  const onSubmit = async (data: z.infer<typeof zodValidation>) => {
    setIsLoading(true);
    const { data: res, error } = await authClient.requestPasswordReset({
      email: data.email,
      redirectTo: "/reset-password",
    });
    if (error) {
      toast.error("Error sending recovery email");
    } else {
      toast.success("Recovery email sent");
    }
    setIsLoading(false);
  };

  return (
    <Card className="mx-auto max-w-sm">
      <CardHeader>
        <CardTitle className="text-2xl">Reset password</CardTitle>
        <CardDescription>
          Enter your email below to send reset password link
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid gap-4">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="grid gap-2">
                <FormField
                  control={form.control}
                  name="email"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Email</FormLabel>
                      <FormControl>
                        <Input
                          id="email"
                          type="email"
                          {...field}
                          placeholder="<EMAIL>"
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
              <Button
                disabled={isLoading}
                type="submit"
                className="mt-2 w-full"
              >
                Send reset password link
              </Button>
            </form>
          </Form>
          <div className="grid gap-2">
            <div className="flex items-center justify-center">
              Remember your password?&nbsp;
              <Link href="/login" className="inline-block underline">
                Login
              </Link>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
