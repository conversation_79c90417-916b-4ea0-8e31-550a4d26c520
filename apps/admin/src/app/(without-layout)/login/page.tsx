import { LoginForm } from "./login-form";
import { authClient } from "@/lib/auth-client";
import { redirect } from "next/navigation";

export default async function Page() {
  const session = await authClient.getSession().catch(() => null) ;
  console.log("user is finally",session)
  if (session?.data) redirect("/");

  return (
    <div className="flex h-screen w-full items-center justify-center px-4">
      <LoginForm />
    </div>
  );
}
