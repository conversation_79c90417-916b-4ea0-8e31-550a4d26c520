import { type NextRequest, NextResponse } from "next/server";
import { AppwriteStorageService } from "@/lib/appwrite-storage";

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get("file") as File;

    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }

    // Validate file type
    if (!file.type.startsWith("image/")) {
      return NextResponse.json(
        { error: "File must be an image" },
        { status: 400 }
      );
    }

    // Validate file size (5MB limit)
    const maxSize = 5 * 1024 * 1024; // 5MB
    if (file.size > maxSize) {
      return NextResponse.json(
        { error: "File size must be less than 5MB" },
        { status: 400 }
      );
    }

    // Upload to Appwrite using the service
    const result = await AppwriteStorageService.uploadPromotionImage(file);

    return NextResponse.json({
      fileId: result.fileId,
      url: result.url,
      name: file.name,
      size: file.size,
      type: file.type,
    });

  } catch (error) {
    console.error("Error uploading file:", error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "Failed to upload file"
      },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const fileId = searchParams.get("fileId");

    if (!fileId) {
      return NextResponse.json(
        { error: "No file ID provided" },
        { status: 400 }
      );
    }

    // Delete from Appwrite storage using the service
    await AppwriteStorageService.deletePromotionImage(fileId);

    return NextResponse.json({ success: true });

  } catch (error) {
    console.error("Error deleting file:", error);
    return NextResponse.json(
      { 
        error: error instanceof Error ? error.message : "Failed to delete file"
      },
      { status: 500 }
    );
  }
}
