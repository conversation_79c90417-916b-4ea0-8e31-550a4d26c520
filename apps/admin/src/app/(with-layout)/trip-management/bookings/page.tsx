"use server";
import { DataTable } from "@/components/data-table";

import { env } from "@/env";
import Heading from "./heading";
import { columns } from "./column";

const Bookings = async () => {
  

  return (
    <>
      <div className="flex flex-col gap-4 px-5">
        <Heading />
        <DataTable
          data={[]}
          columns={columns}
          filterCols={["name"]}
        />
      </div>
    </>
  );
};

export default Bookings;
