"use client";

import { DataTableColumnHeader } from "@/components/data-table-column-header";
import { type ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
// import { DataTableRowActions } from "./data-table-row-actions";
import { type Booking } from "./schema";

export const columns: ColumnDef<Booking>[] = [
  {
    accessorKey: "id",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Booking ID" />
    ),
    cell: ({ row }) => (
      <div className="relative aspect-square w-[30px] object-cover">
        {row.getValue("id")}
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "bookingDetails",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Booking Details"
        className="w-[150px]"
      />
    ),
    cell: ({ row }) => {
      const bookingDetails = row.original.bookingDetails;
      console.log("booking details", bookingDetails);
      //   {
      //     bookingDetails.map((i)=>{
      //     return <span>{row.getValue("bookingDetails")}</span>;

      //     })
      //   }
    },
    enableSorting: false,
  },
  {
    accessorKey: "compensationValue",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Compensation Value"
        className="w-[150px]"
      />
    ),
    cell: ({ row }) => {
      return <span>{row.getValue("compensationValue")}</span>;
    },
    enableSorting: false,
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Created At" />
    ),
    cell: ({ row }) => {
      return <span>{format(row.getValue("createdAt"), "dd MMMM yyyy")}</span>;
    },
  },

  //   {
  //     id: "actions",
  //     cell: ({ row }) => <DataTableRowActions row={row} />,
  //   },
];
