import { AppSidebar } from "@/components/app-sidebar";
import SidebarHeader from "@/components/shared/dashboard-sidebar-header";
import {
  SidebarInset,
  SidebarProvider,
} from "@/components/ui/sidebar";

import "@/styles/globals.css";

import { GeistSans } from "geist/font/sans";
import { type Metadata } from "next";
import { Toaster } from "@/components/ui/sonner";
import { redirect } from "next/navigation";
import { authClient } from "@/lib/auth-client";
import { auth } from "@/server/auth";
import { headers } from "next/headers";
import { TRPCReactProvider } from "@/trpc/react";

export const metadata: Metadata = {
  title: "Thedal Admin",
  description: "Thedal Admin",
  icons: [{ rel: "icon", url: "/favicon.ico" }],
};

export default async function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const session = await auth.api.getSession({
    headers: await headers(),
  });

  if (!session?.session) {
    redirect("/login");
    return null;
  }

  return (
    <html lang="en" className={`${GeistSans.variable}`}>
      <body>
        <TRPCReactProvider>
          <SidebarProvider>
            <AppSidebar
              user={{ name: session.user?.name, email: session.user?.email, avatar: "" }}
            />
            <SidebarInset>
              <SidebarHeader />
              {children}
            </SidebarInset>
          </SidebarProvider>
          <Toaster richColors />
        </TRPCReactProvider>
      </body>
    </html>
  );
}
