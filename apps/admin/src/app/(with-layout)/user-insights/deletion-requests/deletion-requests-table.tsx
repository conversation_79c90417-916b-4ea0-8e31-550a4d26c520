"use client"

import { DataTable } from "@/components/data-table"
import { columns } from "./columns"
import { api } from "@/trpc/react"

export function DeletionRequestsTable() {
  const { data: deletionRequests, isLoading, error } = api.deletionRequests.getAll.useQuery()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg">Loading deletion requests...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-red-600">Error loading deletion requests: {error.message}</div>
      </div>
    )
  }

  if (!deletionRequests || deletionRequests.length === 0) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-lg text-gray-500">No deletion requests found</div>
      </div>
    )
  }

  return (
    <DataTable 
      data={deletionRequests} 
      columns={columns} 
      filterCols={["phone", "status"]} 
    />
  )
} 