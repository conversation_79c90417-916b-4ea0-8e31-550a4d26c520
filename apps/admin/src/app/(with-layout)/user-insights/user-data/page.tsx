import { DataTable } from "@/components/data-table";
import Heading from "@/components/shared/heading";
import { columns } from "./columns";
const tasks = [
  {
    photo: "/user-image.png",
    documentUploadAndemailId: "rajee<PERSON><PERSON><PERSON><PERSON>@gmail",
    flightBookingStatus: "upcoming",
    label: "uploaded",
    compensationValue: 890,
    phoneNo: 9820103489,
    name: "<PERSON><PERSON><PERSON>",
    travellerType: "group",
    nationality: "indian",
  },
  {
    photo: "/user-image.png",
    documentUploadAndemailId: "rajeev<PERSON><PERSON>s@gmail",
    flightBookingStatus: "ongoing",
    label: "not uploaded",
    compensationValue: 890,
    phoneNo: 9820103489,
    name: "<PERSON><PERSON><PERSON>(china)",
    travellerType: "family",
    nationality: "china",
  },
  {
    photo: "/user-image.png",
    documentUploadAndemailId: "rajeev<PERSON><PERSON>s@gmail",
    flightBookingStatus: "",
    label: "uploaded",
    compensationValue: 890,
    phoneNo: 9820103489,
    name: "<PERSON><PERSON>",
    travellerType: "solo",
    nationality: "indian",
  },
];
const UserData = () => {
  return (
    <>
      <div className="flex flex-col gap-4 px-5">
        <Heading title="User Data" />
        <DataTable data={tasks} columns={columns} filterCols={["name"]} />
      </div>
    </>
  );
};

export default UserData;
