"use client"

import { DataTableColumnHeader } from "@/components/data-table-column-header"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"
import { ColumnDef } from "@tanstack/react-table"
import { Task } from "./schema"
import Image from "next/image"
import { statuses } from "@/components/data-table-toolbar"
import { DataTableRowActions } from "./data-table-row-actions"

export const labels = [
  {
    value: "uploaded",
    label: "Uploaded",
  },
  {
    value: "not uploaded",
    label: "Not Uploaded",
  },
]

export const traveller=[
  {
    value:"group",
    label:"Group"
  },
  {
    value:"solo",
    label:"Solo"
  },
  {
    value:"family",
    label:"Family"
  }
]

export const columns: ColumnDef<Task>[] = [
  {
    accessorKey: "photo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Photo" />
    ),
    cell: ({ row }) => <div className="relative w-[30px] object-cover aspect-square">
      <Image src={row.getValue('photo')} alt="user-image" fill={true}
      className="rounded-full"
      />
    </div>,
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Name" className="w-[100px]" />
    ),
    cell: ({ row }) => 
      {
      const travel=traveller.find((t)=>t.value === row.original.travellerType)

      return (
        <div className="flex flex-col gap-[3px] items-start">
          <span>{row.getValue("name")}</span>
          {travel && <Badge  className="font-medium rounded-[6px] py-[3px] px-2 bg-[#EBFAF5] text-[#1F7A5F] text-[10px] leading-[14px]">{travel.label} traveller</Badge>}
        </div>
      )
    },
    enableSorting: false,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: "phoneNo",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Phone no." />
    ),
    cell: ({ row }) => {

      return (
        <div className="flex items-center">
          <span>{row.getValue("phoneNo")}</span>
        </div>
      )
    },
    enableSorting: false,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: "documentUploadAndemailId",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Document Uploaded & Email Id" className="w-[280px]" />
    ),
    cell: ({ row }) => {
      const label = labels.find((label) => label.value === row.original.label)

      return (
        <div className="flex space-x-2">
          {label && <Badge variant="outline">{label.label}</Badge>}
          <span className="max-w-[500px] truncate font-medium">
            {row.getValue("documentUploadAndemailId")}
          </span>
        </div>
      )
    },
  },
  {
    accessorKey: "flightBookingStatus",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Flight Booking Status" className="w-[150px]" />
    ),
    cell: ({ row }) => {
      const status = statuses.find(
        (status) => status.value === row.getValue("flightBookingStatus")
      )

      if (!status) {
        return null
      }


      return (
        <div className="flex gap-2 w-[100px] items-center">
           {(status.label === "Upcoming" || status.label === "Ongoing") ? (
            <>
            <Badge variant="outline">Yes</Badge>
            </>
          ):(
            <>
            <Badge variant="outline">No</Badge>
            </>
          )}
          <span>{status.label}</span>
         
        </div>
      )
    },
    enableSorting: false,
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: "compensationValue",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Compensation Value" />
    ),
    cell: ({ row }) => {

      return (
        <div className="flex items-center">
          <span>${row.getValue("compensationValue")}</span>
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
]
