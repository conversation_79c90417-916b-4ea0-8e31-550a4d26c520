import { z } from "zod"

// We're keeping a simple non-relational schema here.
// IRL, you will have a schema for your data models.
export const taskSchema = z.object({
  photo: z.string(),
  name:z.string(),
  travellerType:z.string(),
  documentUploadAndemailId: z.string(),
  flightBookingStatus: z.string(),
  label: z.string(),
  compensationValue: z.number(),
  phoneNo:z.number(),
  nationality:z.string()
})

export type Task = z.infer<typeof taskSchema>
