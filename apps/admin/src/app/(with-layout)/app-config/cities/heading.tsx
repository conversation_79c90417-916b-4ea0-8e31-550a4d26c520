"use client";

import { CalendarDateRangePicker } from "@/components/date-range-picker";
import { But<PERSON> } from "@/components/ui/button";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { X } from "lucide-react";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Sheet,
  SheetClose,
  SheetFooter,
  SheetContent,
  SheetDescription,
  SheetTrigger,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { api } from "@/trpc/react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { countryList } from "./country-list";

const cityFormSchema = z.object({
  country: z.string().min(1, { message: "Country is required" }),
  state: z.string().trim().min(2, { message: "State is required." }),
  city: z.string().trim().min(2, { message: "City is required." }),
});
const Heading = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams.toString());
  const cityId = params.get("cityId");

  const form = useForm<z.infer<typeof cityFormSchema>>({
    resolver: zodResolver(cityFormSchema),
    defaultValues: {
      state: "",
      city: "",
      country: "",
    },
  });

  const { data: city } = api.cities.getById.useQuery(
    { id: cityId! },
    { enabled: cityId !== "new" && !!cityId }
  );

  useEffect(() => {
    if (city) {
      setFormLoading(true);
      form.setValue("city", city.name);
      form.setValue("state", city.state);
      form.setValue("country", city.country);
      setFormLoading(false);
    }
  }, [city, form]);

  const handleSheetOpen = () => {
    setFormLoading(false);
    params.set("cityId", "new");
    router.push(`?${params}`);
  };

  const handleSheetClose = () => {
    params.delete("cityId");
    router.replace(pathname);
  };

  const createCityMutation = api.cities.create.useMutation({
    onSuccess: () => {
      toast.success("City successfully created");
      form.reset({
        city: "",
        country: "",
        state: "",
      });
      router.replace(pathname);
    },
    onError: (error: any) => {
      console.log("error is", error);
      toast.error("Error in creating city");
    },
  });

  const updateCityMutation = api.cities.update.useMutation({
    onSuccess: () => {
      toast.success("City successfully updated");
      form.reset({
        city: "",
        country: "",
        state: "",
      });
      router.replace(pathname);
    },
    onError: (error: any) => {
      console.log("error is", error);
      toast.error("Error in updating city");
    },
  });

  const onerror = (err: any) => {
    console.log("error is", err);
  };

  const onSubmit = async (values: z.infer<typeof cityFormSchema>) => {
    try {
      setIsLoading(true);
      if (cityId === "new") {
        await createCityMutation.mutateAsync({
          name: values.city,
          country: values.country,
          state: values.state,
        });
      } else {
        await updateCityMutation.mutateAsync({
          id: cityId!,
          name: values.city,
          country: values.country,
          state: values.state,
        });
      }
    } catch (error) {
      console.log("error is", error);
      toast.error("Error in creating or updating cities");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="flex-1 space-y-4 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">City</h2>
          <div className="flex items-center space-x-2">
            <CalendarDateRangePicker />
            <Button
              onClick={handleSheetOpen}
            >
              Add City
            </Button>
            <Sheet
              open={cityId ? true : false}
              onOpenChange={() => handleSheetClose}
            >
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Add City</SheetTitle>
                  <SheetDescription>
                    Enter the details of the city, including the country and
                    state, to allow the user to select language preferences
                    while completing their profile and flight information.
                  </SheetDescription>
                </SheetHeader>

                {formLoading ? (
                  <>
                    <div className="space-y-8 pt-10">
                      <div className="space-y-2">
                        <div className="h-5 w-[100px] animate-pulse rounded bg-gray-200"></div>
                        <div className="h-10 w-full animate-pulse rounded bg-gray-200"></div>
                        <div className="h-4 w-[150px] animate-pulse rounded bg-gray-200"></div>
                      </div>

                      <div className="flex justify-end">
                        <div className="h-10 w-[100px] animate-pulse rounded bg-gray-200"></div>
                      </div>
                    </div>
                  </>
                ) : (
                  <Form {...form}>
                    <form
                      onSubmit={form.handleSubmit(onSubmit, onerror)}
                      className="space-y-8 pt-10"
                    >
                      <FormField
                        control={form.control}
                        name="country"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Country</FormLabel>
                            <FormControl>
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <SelectTrigger className="w-full">
                                  <SelectValue placeholder="Select country" />
                                </SelectTrigger>
                                <SelectContent className="max-w-[340px]">
                                  {countryList.map((country, index) => (
                                    <>
                                      <SelectItem value={country} key={index}>
                                        {country}
                                      </SelectItem>
                                    </>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="state"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>State</FormLabel>
                            <FormControl>
                              <Input placeholder="" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="city"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>City</FormLabel>
                            <FormControl>
                              <Input placeholder="" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex justify-end">
                        <Button
                          disabled={isLoading}
                          type="submit"
                          size="lg"
                          className="font-medium w-full"
                        >
                          Save
                        </Button>
                      </div>
                    </form>
                  </Form>
                )}
                <SheetClose
                  className="absolute right-4 top-4 rounded-sm"
                  onClick={handleSheetClose}
                >
                  <X className="h-4 w-4" />
                </SheetClose>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </>
  );
};

export default Heading;
