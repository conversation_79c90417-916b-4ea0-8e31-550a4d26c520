"use server";

import { DataTable } from "@/components/data-table";
import Heading from "./heading";
import { columns } from "./column";
import { api } from "@/trpc/server";

const City = async () => {
  const cities = await api.cities.getAll();
  
  const mappedCities = cities.map((city) => ({
    id: city.id,
    createdAt: city.createdAt.toISOString(),
    city: city.name,
    country: city.country,
    state: city.state,
  }));
  return (
    <>
      <div className="flex flex-col gap-4 px-5">
        <Heading />
        <DataTable
          data={mappedCities}
          columns={columns}
          filterCols={["country", "state", "city"]}
        />
      </div>
    </>
  );
};
export default City;
