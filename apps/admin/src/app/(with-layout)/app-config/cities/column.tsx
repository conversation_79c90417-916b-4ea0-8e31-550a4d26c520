"use client";

import { DataTableColumnHeader } from "@/components/data-table-column-header";
import { type ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { type City } from "./schema";
import { DataTableRowActions } from "./data-table-row-actions";

export const columns: ColumnDef<City>[] = [
  {
    accessorKey: "id",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="City ID"
        className="w-[170px]"
      />
    ),
    cell: ({ row }) => (
      <div className="relative aspect-square w-[30px] object-cover">
        {row.getValue("id")}
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "country",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Country"
        className="w-[150px]"
      />
    ),
    cell: ({ row }) => {
      return <span>{row.getValue("country") || "Not added"}</span>;
    },
    enableSorting: false,
  },
  {
    accessorKey: "state",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="State"
        className="w-[150px]"
      />
    ),
    cell: ({ row }) => {
      return <span>{row.getValue("state") || "Not added"}</span>;
    },
    enableSorting: false,
  },
  {
    accessorKey: "city",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="City"
        className="w-[150px]"
      />
    ),
    cell: ({ row }) => {
      return <span>{row.getValue("city")}</span>;
    },
    enableSorting: false,
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Added On" />
    ),
    cell: ({ row }) => {
      return <span>{format(row.getValue("createdAt"), "dd MMMM yyyy")}</span>;
    },
  },

  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
];
