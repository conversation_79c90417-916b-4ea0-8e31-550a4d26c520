import { DataTable } from "@/components/data-table";
import { columns } from "./column";
import Heading from "./heading";
import { api } from "@/trpc/server";
import dayjs from "dayjs";

const Language = async () => {
  const languages = await api.languages.getAll();
  
  const mappedLanguages = languages.map((language) => ({
    id: language.id,
    name: language.name,
    createdAt: dayjs(language.createdAt).format("DD/MM/YYYY"),
  }));

  return (
    <>
      <div className="flex flex-col gap-4 px-5">
        <Heading />
        <DataTable
          data={mappedLanguages}
          columns={columns}
          filterCols={["name"]}
        />
      </div>
    </>
  );
};

export default Language;
