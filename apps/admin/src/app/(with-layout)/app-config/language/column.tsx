"use client";

import { DataTableColumnHeader } from "@/components/data-table-column-header";
import { type ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { DataTableRowActions } from "./data-table-row-actions";
import { type Language } from "./schema";

export const columns: ColumnDef<Language>[] = [
  {
    accessorKey: "id",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Language ID" />
    ),
    cell: ({ row }) => (
      <div className="relative aspect-square w-[30px] object-cover">
        {row.getValue("id")}
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Language Name"
        className="w-[150px]"
      />
    ),
    cell: ({ row }) => {
      return <span>{row.getValue("name")}</span>;
    },
    enableSorting: false,
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Added On" />
    ),
    cell: ({ row }) => {
      return <span>{format(row.getValue("createdAt"), "dd MMMM yyyy")}</span>;
    },
  },
  // {
  //   accessorKey: "title",
  //   header: ({ column }) => (
  //     <DataTableColumnHeader column={column} title="Number of Language Users" className="w-[280px]" />
  //   ),
  //   cell: ({ row }) => {
  //     return (
  //       <div className="flex space-x-2">
  //          <Badge variant="outline">{row.original.usersCount}</Badge>
  //         <span className="max-w-[500px] truncate font-medium">
  //           {row.getValue("title")}
  //         </span>
  //       </div>
  //     )
  //   },
  // },

  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
];
