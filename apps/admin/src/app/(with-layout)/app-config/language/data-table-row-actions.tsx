"use client";

import { type Row } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter, useSearchParams } from "next/navigation";
import { api } from "@/trpc/react";
import { toast } from "sonner";
import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Language } from "./schema";

interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
}

export function DataTableRowActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const searchParams = useSearchParams();
  const [showDialog, setShowDialog] = useState(false);
  const params = new URLSearchParams(searchParams.toString());
  const router = useRouter();

  const deleteLanguageMutation = api.languages.delete.useMutation({
    onSuccess: () => {
      toast.success("Language deleted successfully");
      window.location.reload();
    },
    onError: (error: any) => {
      console.log("error is", error);
      toast.error("Error in deleting language");
    },
  });

  const handleEditLanguage = (data: { id: string }) => {
    params.set("languageId", data.id);
    router.push(`?${params}`);
  };

  const handleDeleteLanguage = async (data: { id: string }) => {
    try {
      await deleteLanguageMutation.mutateAsync({ id: data.id });
    } catch (err) {
      console.log("error is", err);
      toast.error("Error in deleting language");
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
          >
            <MoreHorizontal />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          <DropdownMenuItem
            onClick={() => handleEditLanguage(row.original as Language)}
          >
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setShowDialog(true)}>
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <AlertDialog open={showDialog} onOpenChange={setShowDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Are you absolutely sure you want to delete this record?
            </AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this partcular language from your
              table.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => handleDeleteLanguage(row.original as Language)}
            >
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
