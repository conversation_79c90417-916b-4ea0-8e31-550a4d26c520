"use client";

import { CalendarDateRangePicker } from "@/components/date-range-picker";
import { But<PERSON> } from "@/components/ui/button";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { X } from "lucide-react";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Sheet,
  SheetClose,
  <PERSON>et<PERSON>ooter,
  SheetContent,
  SheetDescription,
  SheetTrigger,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { api } from "@/trpc/react";
import { useEffect, useState } from "react";
import { toast } from "sonner";

const languageFormSchema = z.object({
  name: z.string().trim().min(1, { message: "Language name is required." }),
});
const Heading = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams.toString());
  const languageId = params.get("languageId");

  const form = useForm<z.infer<typeof languageFormSchema>>({
    resolver: zodResolver(languageFormSchema),
    defaultValues: {
      name: "",
    },
  });

  const { data: language } = api.languages.getById.useQuery(
    { id: languageId! },
    { enabled: languageId !== "new" && !!languageId }
  );

  useEffect(() => {
    if (language) {
      setFormLoading(true);
      form.setValue("name", language.name);
      setFormLoading(false);
    }
  }, [language, form]);

  const handleSheetOpen = () => {
    setFormLoading(false);
    params.set("languageId", "new");
    router.push(`?${params}`);
  };

  const handleSheetClose = () => {
    params.delete("languageId");
    router.replace(pathname);
  };

  const createLanguageMutation = api.languages.create.useMutation({
    onSuccess: () => {
      toast.success("Language has been successfully created");
      form.reset({
        name: "",
      });
      params.delete("languageId");
      router.replace(pathname);
    },
    onError: (error: any) => {
      console.log("error", error);
      toast.error("Error in creating language");
    },
  });

  const updateLanguageMutation = api.languages.update.useMutation({
    onSuccess: () => {
      toast.success("Language has been successfully updated");
      form.reset({
        name: "",
      });
      params.delete("languageId");
      router.replace(pathname);
    },
    onError: (error: any) => {
      console.log("error", error);
      toast.error("Error in updating language");
    },
  });

  const onerror = (err: any) => {
    console.log("error is", err);
  };

  const onSubmit = async (values: z.infer<typeof languageFormSchema>) => {
    setIsLoading(true);
    try {
      if (languageId === "new") {
        await createLanguageMutation.mutateAsync({
          name: values.name,
        });
      } else {
        await updateLanguageMutation.mutateAsync({
          id: languageId!,
          name: values.name,
        });
      }
    } catch (error) {
      console.log("error", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="flex-1 space-y-4 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Language</h2>
          <div className="flex items-center space-x-2">
            <CalendarDateRangePicker />
            <Button
              onClick={handleSheetOpen}
            >
              Add Language
            </Button>
            <Sheet
              open={languageId ? true : false}
              onOpenChange={() => handleSheetClose}
            >
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Add Langauge</SheetTitle>
                  <SheetDescription>
                    You can enter name of the language that you want to show in
                    language preferences for the user to select while filling up
                    companion of user flow( info about user, and flight info)
                  </SheetDescription>
                </SheetHeader>

                {formLoading ? (
                  <>
                    <div className="space-y-8 pt-10">
                      <div className="space-y-2">
                        <div className="h-5 w-[100px] animate-pulse rounded bg-gray-200"></div>
                        <div className="h-10 w-full animate-pulse rounded bg-gray-200"></div>
                        <div className="h-4 w-[150px] animate-pulse rounded bg-gray-200"></div>
                      </div>

                      <div className="flex justify-end">
                        <div className="h-10 w-[100px] animate-pulse rounded bg-gray-200"></div>
                      </div>
                    </div>
                  </>
                ) : (
                  <Form {...form}>
                    <form
                      onSubmit={form.handleSubmit(onSubmit, onerror)}
                      className="space-y-8 pt-10"
                    >
                      <FormField
                        control={form.control}
                        name="name"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Language Name</FormLabel>
                            <FormControl>
                              <Input placeholder="" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex justify-end">
                        <Button
                          disabled={isLoading}
                          type="submit"
                          size="lg"
                          className="font-medium w-full"
                        >
                          Save
                        </Button>
                      </div>
                    </form>
                  </Form>
                )}
                <SheetClose
                  className="absolute right-4 top-4 rounded-sm"
                  onClick={handleSheetClose}
                >
                  <X className="h-4 w-4" />
                </SheetClose>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </>
  );
};

export default Heading;
