"use client";

import { type Row } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { useRouter, useSearchParams } from "next/navigation";

import { toast } from "sonner";
import { useState } from "react";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { api } from "@/trpc/react";
import { Airport } from "./schema";
interface DataTableRowActionsProps<TData> {
  row: Row<TData>;
}

export function DataTableRowActions<TData>({
  row,
}: DataTableRowActionsProps<TData>) {
  const searchParams = useSearchParams();
  const [showDialog, setShowDialog] = useState(false);
  const params = new URLSearchParams(searchParams.toString());
  const router = useRouter();
  const deleteAirportMutation = api.airports.delete.useMutation({
    onSuccess: () => {
      toast.success("Airport deleted successfully");
      window.location.reload();
    },
    onError: (error: any) => {
      console.log("error is", error);
      toast.error("Error in deleting airport");
    },
  });

  const handleEditAirport = (data: { airportId: string }) => {
    params.set("airportId", data.airportId);
    router.push(`?${params}`);
  };

  const handleDeleteAirport = async (data: { airportId: string }) => {
    try {
      await deleteAirportMutation.mutateAsync({ id: data.airportId });
    } catch (err) {
      console.log("error is", err);
      toast.error("Error in deleting airport");
    }
  };

  return (
    <>
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button
            variant="ghost"
            className="flex h-8 w-8 p-0 data-[state=open]:bg-muted"
          >
            <MoreHorizontal />
            <span className="sr-only">Open menu</span>
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-[160px]">
          <DropdownMenuItem
            onClick={() => handleEditAirport(row.original as Airport)}
          >
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setShowDialog(true)}>
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <AlertDialog open={showDialog} onOpenChange={setShowDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>
              Are you absolutely sure you want to delete this record?
            </AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete this partcular language from your
              table.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={() => handleDeleteAirport(row.original as Airport)}
            >
              Continue
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}
