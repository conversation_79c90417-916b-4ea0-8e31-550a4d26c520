import { DataTable } from "@/components/data-table";
import Heading from "./heading";
import { columns } from "./column";
import { api } from "@/trpc/server";
import dayjs from "dayjs";

const Airports = async () => {
  const airports = await api.airports.getAll();
  
  const mappedAirports = airports.map((airport) => ({
    airportId: airport.id,
    airportName: airport.name,
    createdAt: dayjs(airport.createdAt).format("DD/MM/YYYY"),
    shortCode: airport.shortCode,
    airportLocation: airport.locationCity,
  }));

  return (
    <>
      <div className="flex flex-col gap-4 px-5">
        <Heading />
        <DataTable
          data={mappedAirports}
          columns={columns}
          filterCols={["airportName", "airportLocation", "shortCode"]}
        />
      </div>
    </>
  );
};

export default Airports;
