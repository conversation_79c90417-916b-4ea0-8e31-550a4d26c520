"use client";

import { CalendarDateRangePicker } from "@/components/date-range-picker";
import { But<PERSON> } from "@/components/ui/button";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetTrigger,
  SheetHeader,
  SheetTitle,
  SheetClose,
} from "@/components/ui/sheet";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

import { z } from "zod";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { X } from "lucide-react";
import { api } from "@/trpc/react";
import { toast } from "sonner";
import { useEffect, useState } from "react";
import { countryList } from "../cities/country-list";
const airportFormSchema = z.object({
  airportName: z
    .string()
    .trim()
    .min(1, { message: "Airport name is required." }),
  airportCity: z.string().min(2, { message: "Airport city is required." }),
  shortCode: z
    .string()
    .trim()
    .min(1, { message: "Airport short code is required." }),
});
const Heading = () => {
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const router = useRouter();
  const params = new URLSearchParams(searchParams);
  const airportId = searchParams.get("airportId");
  const [isLoading, setLoading] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [location, setLocation] = useState<
    {
      cityId: string;
      cityName: string;
    }[]
  >([]);

  const form = useForm<z.infer<typeof airportFormSchema>>({
    resolver: zodResolver(airportFormSchema),
    defaultValues: {
      airportCity: "",
      airportName: "",
      shortCode: "",
    },
  });

  const { data: cities } = api.cities.getAll.useQuery();
  const { data: airport } = api.airports.getById.useQuery(
    { id: airportId! },
    { enabled: airportId !== "new" && !!airportId }
  );

  //fetch the airport details on the basis of airport id
  useEffect(() => {
    if (airport) {
      setFormLoading(true);
      form.setValue("airportName", airport.name);
      form.setValue("airportCity", airport.locationCity);
      form.setValue("shortCode", airport.shortCode);
      setFormLoading(false);
    }
  }, [airport, form]);

  //fetch all the cities from the database
  useEffect(() => {
    if (cities) {
      const locations = cities.map((city) => ({
        cityId: city.id,
        cityName: city.name,
      }));
      setLocation(locations);
    }
  }, [cities]);

  const handleSheetOpen = () => {
    setFormLoading(false);
    params.set("airportId", "new");
    router.push(`?${params.toString()}`);
  };

  const handleSheetClose = () => {
    params.delete("airportId");
    router.replace(pathname);
  };

  const onerror = (err: any) => {
    console.log("error is", err);
  };

  const createAirportMutation = api.airports.create.useMutation({
    onSuccess: () => {
      toast.success("Airport successfully created");
      form.reset({
        airportCity: "",
        airportName: "",
        shortCode: "",
      });
      params.delete("airportId");
      router.replace(pathname);
    },
    onError: (error: any) => {
      console.log("error is", error);
      toast.error("Error in creating airport.");
    },
  });

  const updateAirportMutation = api.airports.update.useMutation({
    onSuccess: () => {
      toast.success("Airport successfully updated");
      form.reset({
        airportCity: "",
        airportName: "",
        shortCode: "",
      });
      params.delete("airportId");
      router.replace(pathname);
    },
    onError: (error: any) => {
      console.log("error is", error);
      toast.error("Error in updating airport.");
    },
  });

  async function onSubmit(values: z.infer<typeof airportFormSchema>) {
    try {
      setLoading(true);
      if (airportId === "new") {
        await createAirportMutation.mutateAsync({
          name: values.airportName,
          locationCity: values.airportCity,
          shortCode: values.shortCode,
        });
      } else {
        await updateAirportMutation.mutateAsync({
          id: airportId!,
          name: values.airportName,
          locationCity: values.airportCity,
          shortCode: values.shortCode,
        });
      }
    } catch (err) {
      console.log("error is", err);
      toast.error("Error in creating or updating airport.");
    } finally {
      setLoading(false);
    }
  }
  return (
    <>
      <div className="flex-1 space-y-4 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Airports</h2>
          <div className="flex items-center space-x-2">
            <CalendarDateRangePicker />
            <Button
              onClick={handleSheetOpen}
            >
              Add Airport
            </Button>
            <Sheet
              open={airportId ? true : false}
              onOpenChange={handleSheetClose}
            >
              <SheetContent>
                <SheetHeader>
                  <SheetTitle>Add Airport</SheetTitle>
                  <SheetDescription>
                    You can enter name of the language that you want to show in
                    language preferences for the user to select while filling up
                    companion of user flow( info about user, and flight info)
                  </SheetDescription>
                </SheetHeader>
                {formLoading ? (
                  <>
                    <div className="space-y-8 pt-10">
                      <div className="space-y-2">
                        <div className="h-5 w-[100px] animate-pulse rounded bg-gray-200"></div>
                        <div className="h-10 w-full animate-pulse rounded bg-gray-200"></div>
                        <div className="h-4 w-[150px] animate-pulse rounded bg-gray-200"></div>
                      </div>

                      <div className="flex justify-end">
                        <div className="h-10 w-[100px] animate-pulse rounded bg-gray-200"></div>
                      </div>
                    </div>
                  </>
                ) : (
                  <Form {...form}>
                    <form
                      onSubmit={form.handleSubmit(onSubmit, onerror)}
                      className="space-y-8 pt-10"
                    >
                      <FormField
                        control={form.control}
                        name="shortCode"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Airport ShortCode</FormLabel>
                            <FormControl>
                              <Input placeholder="DEL" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="airportName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Airport Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Indra Gandhi Airport"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="airportCity"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Airport City</FormLabel>
                            <FormControl>
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <SelectTrigger className="w-full">
                                  <SelectValue placeholder="Select city" />
                                </SelectTrigger>
                                <SelectContent className="max-w-[340px]">
                                  {location.map((city, index) => (
                                    <>
                                      <SelectItem
                                        value={city.cityId}
                                        key={index}
                                      >
                                        {city.cityName}
                                      </SelectItem>
                                    </>
                                  ))}
                                </SelectContent>
                              </Select>
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <div className="flex justify-end">
                        <Button
                          disabled={isLoading}
                          type="submit"
                          size="lg"
                          className="font-medium w-full"
                        >
                          Save
                        </Button>
                      </div>
                    </form>
                  </Form>
                )}

                <SheetClose
                  className="absolute right-4 top-4 rounded-sm"
                  onClick={handleSheetClose}
                >
                  <X className="h-4 w-4" />
                </SheetClose>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </>
  );
};

export default Heading;
