"use client";

import { DataTableColumnHeader } from "@/components/data-table-column-header";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { ColumnDef } from "@tanstack/react-table";
import Image from "next/image";
import { statuses } from "@/components/data-table-toolbar";
import { Airport } from "./schema";
import { format } from "date-fns";
import { DataTableRowActions } from "./data-table-row-actions";

export const columns: ColumnDef<Airport>[] = [
  {
    accessorKey: "airportId",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Airport ID"
        className="w-[180px]"
      />
    ),
    cell: ({ row }) => (
      <div className="relative aspect-square w-[30px] object-cover">
        {row.getValue("airportId")}
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "shortCode",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Airport Shortcode"
        className="w-[150px]"
      />
    ),
    cell: ({ row }) => {
      return <span>{row.getValue("shortCode")}</span>;
    },
    enableSorting: false,
  },
  {
    accessorKey: "airportName",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Airport Name"
        className="w-[150px]"
      />
    ),
    cell: ({ row }) => {
      return <span>{row.getValue("airportName")}</span>;
    },
    enableSorting: false,
    // filterFn: (row, id, value) => {
    //   return value?.includes(row.getValue(id));
    // },
  },
  {
    accessorKey: "airportLocation",
    header: ({ column }) => (
      <DataTableColumnHeader
        column={column}
        title="Airport Location"
        className="w-[150px]"
      />
    ),
    cell: ({ row }) => {
      return <span>{row.getValue("airportLocation")}</span>;
    },
    enableSorting: false,
    // filterFn: (row, id, value) => {
    //   return value.includes(row.getValue(id));
    // },
  },
  {
    accessorKey: "createdAt",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Added On" />
    ),
    cell: ({ row }) => {
      return <span>{format(row.getValue("createdAt"), "dd MMMM yyyy")}</span>;
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  // {
  //   accessorKey: "title",
  //   header: ({ column }) => (
  //     <DataTableColumnHeader column={column} title="Number of Language Users" className="w-[280px]" />
  //   ),
  //   cell: ({ row }) => {
  //     return (
  //       <div className="flex space-x-2">
  //          <Badge variant="outline">{row.original.usersCount}</Badge>
  //         <span className="max-w-[500px] truncate font-medium">
  //           {row.getValue("title")}
  //         </span>
  //       </div>
  //     )
  //   },
  // },

  {
    id: "actions",
    cell: ({ row }) => <DataTableRowActions row={row} />,
  },
];
