"use server";

import { DataTable } from "@/components/data-table";
import Heading from "./heading";
import { columns } from "./columns";
import { api } from "@/trpc/server";

const PromotionsPage = async () => {
  const promotions = await api.promotions.getAll({ limit: 50, offset: 0 });
  
  const mappedPromotions = promotions.map((promotion) => ({
    ...promotion,
    isActive: promotion.isActive ?? true,
    targetUserType: promotion.targetUserType ?? "ALL",
    priority: promotion.priority ?? 0,
    status: getPromotionStatus(promotion),
  }));

  function getPromotionStatus(promotion: {
    isActive: boolean | null;
    startDate?: Date;
    endDate?: Date;
  }): "Inactive" | "Scheduled" | "Expired" | "Active" {
    const now = new Date();
    if (!promotion.isActive) return "Inactive";
    if (promotion.startDate && promotion.startDate > now) return "Scheduled";
    if (promotion.endDate && promotion.endDate < now) return "Expired";
    return "Active";
  }

  return (
    <>
      <div className="flex flex-col gap-4 px-5">
        <Heading />
        <DataTable
          data={mappedPromotions}
          columns={columns}
          filterCols={["title", "targetUserType", "status"]}
        />
      </div>
    </>
  );
};

export default PromotionsPage;
