"use client";

import { DataTableColumnHeader } from "@/components/data-table-column-header";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import type { ColumnDef } from "@tanstack/react-table";
import { format } from "date-fns";
import { Eye, MousePointer, Gift, MoreHorizontal, Edit, Trash, ToggleLeft, ToggleRight } from "lucide-react";
import Image from "next/image";
import type { Promotion } from "./schema";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { api } from "@/trpc/react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

const PromotionActions = ({ promotion }: { promotion: Promotion }) => {
  const utils = api.useUtils();
  const router = useRouter();
  
  const toggleActive = api.promotions.toggleActive.useMutation({
    onSuccess: () => {
      void utils.promotions.getAll.invalidate();
      toast.success(
        promotion.isActive 
          ? "Promotion deactivated successfully" 
          : "Promotion activated successfully"
      );
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const deletePromotion = api.promotions.delete.useMutation({
    onSuccess: () => {
      void utils.promotions.getAll.invalidate();
      toast.success("Promotion deleted successfully");
    },
    onError: (error) => {
      toast.error(error.message);
    },
  });

  const handleToggleActive = () => {
    toggleActive.mutate({
      id: promotion.id,
      isActive: !promotion.isActive,
    });
  };

  const handleEdit = () => {
    router.push(`?promotionId=${promotion.id}`);
  };

  const handleDelete = () => {
    if (confirm("Are you sure you want to delete this promotion?")) {
      deletePromotion.mutate({ id: promotion.id });
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>
        <DropdownMenuItem
          onClick={() => navigator.clipboard.writeText(promotion.id)}
        >
          Copy promotion ID
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleToggleActive}>
          {promotion.isActive ? (
            <>
              <ToggleLeft className="mr-2 h-4 w-4" />
              Deactivate
            </>
          ) : (
            <>
              <ToggleRight className="mr-2 h-4 w-4" />
              Activate
            </>
          )}
        </DropdownMenuItem>
        <DropdownMenuItem onClick={handleEdit}>
          <Edit className="mr-2 h-4 w-4" />
          Edit
        </DropdownMenuItem>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={handleDelete}
          className="text-red-600"
        >
          <Trash className="mr-2 h-4 w-4" />
          Delete
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

const getPromotionTypeColor = (type: string | null) => {
  switch (type) {
    case "DISCOUNT":
      return "bg-blue-100 text-blue-800";
    case "CASHBACK":
      return "bg-green-100 text-green-800";
    case "FREE_SERVICE":
      return "bg-purple-100 text-purple-800";
    case "SPECIAL_OFFER":
      return "bg-orange-100 text-orange-800";
    default:
      return "bg-gray-100 text-gray-800";
  }
};

const getStatusColor = (promotion: Promotion) => {
  const now = new Date();
  if (!promotion.isActive) {
    return "bg-gray-100 text-gray-800";
  }
  if (promotion.startDate > now) {
    return "bg-yellow-100 text-yellow-800";
  }
  if (promotion.endDate < now) {
    return "bg-red-100 text-red-800";
  }
  return "bg-green-100 text-green-800";
};

const getStatusText = (promotion: Promotion) => {
  const now = new Date();
  if (!promotion.isActive) return "Inactive";
  if (promotion.startDate > now) return "Scheduled";
  if (promotion.endDate < now) return "Expired";
  return "Active";
};

export const columns: ColumnDef<Promotion>[] = [
  {
    accessorKey: "imageUrl",
    header: "Image",
    cell: ({ row }) => {
      const imageUrl = row.original.imageUrl;
      return (
        <div className="relative aspect-square w-[40px] overflow-hidden rounded-md">
          {imageUrl ? (
            <Image
              src={imageUrl}
              alt="Promotion"
              fill
              className="object-cover"
            />
          ) : (
            <div className="flex h-full w-full items-center justify-center bg-gray-100">
              <Gift className="h-4 w-4 text-gray-400" />
            </div>
          )}
        </div>
      );
    },
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "title",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Title" />
    ),
    cell: ({ row }) => {
      const promotion = row.original;
      return (
        <div className="space-y-1">
          <div className="font-medium">{promotion.title}</div>
        </div>
      );
    },
  },
  {
    accessorKey: "targetUserType",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Target" />
    ),
    cell: ({ row }) => {
      const type = row.original.targetUserType;
      return (
        <Badge
          variant="secondary"
          className={getPromotionTypeColor(type)}
        >
          {type.replace("_", " ")}
        </Badge>
      );
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id));
    },
  },
  {
    accessorKey: "priority",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Priority" />
    ),
    cell: ({ row }) => {
      const priority = row.original.priority;
      return priority;
    },
  },
  {
    accessorKey: "analytics",
    header: "Analytics",
    cell: ({ row }) => {
      const analytics = row.original.analytics;
      return (
        <div className="flex space-x-4 text-sm">
          <div className="flex items-center space-x-1">
            <Eye className="h-3 w-3" />
            <span>{analytics.views}</span>
          </div>
          <div className="flex items-center space-x-1">
            <MousePointer className="h-3 w-3" />
            <span>{analytics.clicks}</span>
          </div>
        </div>
      );
    },
    enableSorting: false,
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const promotion = row.original;
      return (
        <Badge
          variant="secondary"
          className={getStatusColor(promotion)}
        >
          {getStatusText(promotion)}
        </Badge>
      );
    },
    filterFn: (row, id, value) => {
      const promotion = row.original;
      const status = getStatusText(promotion);
      return value.includes(status);
    },
  },
  {
    accessorKey: "startDate",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Start Date" />
    ),
    cell: ({ row }) => {
      return format(row.getValue("startDate"), "MMM dd, yyyy");
    },
  },
  {
    accessorKey: "endDate",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="End Date" />
    ),
    cell: ({ row }) => {
      return format(row.getValue("endDate"), "MMM dd, yyyy");
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <PromotionActions promotion={row.original} />,
  },
];
