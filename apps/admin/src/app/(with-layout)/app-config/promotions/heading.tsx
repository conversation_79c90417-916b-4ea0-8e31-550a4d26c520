"use client";

import { CalendarDateRangePicker } from "@/components/date-range-picker";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { X } from "lucide-react";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Sheet,
  SheetClose,
  <PERSON>et<PERSON>ooter,
  <PERSON>etContent,
  SheetDescription,
  SheetTrigger,
  SheetHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { api } from "@/trpc/react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { createPromotionSchema } from "./schema";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { CalendarIcon, Upload } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import Image from "next/image";

type CreatePromotionForm = z.infer<typeof createPromotionSchema>;

const Heading = () => {
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const [formLoading, setFormLoading] = useState(false);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const searchParams = useSearchParams();
  const params = new URLSearchParams(searchParams.toString());
  const promotionId = params.get("promotionId");

  const form = useForm<CreatePromotionForm>({
    resolver: zodResolver(createPromotionSchema),
    defaultValues: {
      title: "",
      imageUrl: undefined,
      imageFileId: undefined,
      url: undefined,
      isActive: true,
      startDate: undefined,
      endDate: undefined,
      targetUserType: "ALL",
      priority: 0,
    },
  });

  const { data: promotion } = api.promotions.getById.useQuery(
    { id: promotionId! },
    { enabled: promotionId !== "new" && !!promotionId }
  );

  useEffect(() => {
    if (promotion) {
      setFormLoading(true);
      form.setValue("title", promotion.title);
      if (promotion.imageUrl) {
        form.setValue("imageUrl", promotion.imageUrl);
        setImagePreview(promotion.imageUrl);
      }
      if (promotion.imageFileId) {
        form.setValue("imageFileId", promotion.imageFileId);
      }
      if (promotion.url) {
        form.setValue("url", promotion.url);
      }
      form.setValue("isActive", promotion.isActive ?? true);
      form.setValue("startDate", promotion.startDate);
      form.setValue("endDate", promotion.endDate);
      form.setValue("targetUserType", promotion.targetUserType ?? "ALL");
      form.setValue("priority", promotion.priority ?? 0);
      setFormLoading(false);
    }
  }, [promotion, form]);

  const handleSheetOpen = () => {
    setFormLoading(false);
    params.set("promotionId", "new");
    router.push(`?${params}`);
  };

  const handleSheetClose = () => {
    params.delete("promotionId");
    form.reset();
    setImagePreview(null);
    setImageFile(null);
    router.replace(pathname);
  };

  const createPromotionMutation = api.promotions.create.useMutation({
    onSuccess: () => {
      toast.success("Promotion successfully created");
      form.reset();
      setImagePreview(null);
      setImageFile(null);
      router.replace(pathname);
    },
    onError: (error: any) => {
      console.log("error is", error);
      toast.error("Error in creating promotion");
    },
  });

  const updatePromotionMutation = api.promotions.update.useMutation({
    onSuccess: () => {
      toast.success("Promotion successfully updated");
      form.reset();
      setImagePreview(null);
      setImageFile(null);
      router.replace(pathname);
    },
    onError: (error: any) => {
      console.log("error is", error);
      toast.error("Error in updating promotion");
    },
  });

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setImageFile(file);
      const reader = new FileReader();
      reader.onload = () => {
        setImagePreview(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadImage = async (file: File): Promise<string> => {
    const formData = new FormData();
    formData.append("file", file);

    const response = await fetch("/api/upload-promotion-image", {
      method: "POST",
      body: formData,
    });

    if (!response.ok) {
      throw new Error("Failed to upload image");
    }

    const data = await response.json();
    return data.url;
  };

  const onSubmit = async (values: CreatePromotionForm) => {
    try {
      setIsLoading(true);
      
      let imageUrl = values.imageUrl;
      if (imageFile) {
        imageUrl = await uploadImage(imageFile);
      }

      const promotionData = {
        ...values,
        imageUrl,
      };

      if (promotionId === "new") {
        await createPromotionMutation.mutateAsync(promotionData);
      } else {
        await updatePromotionMutation.mutateAsync({
          id: promotionId!,
          ...promotionData,
        });
      }
    } catch (error) {
      console.log("error is", error);
      toast.error("Error in creating or updating promotion");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <div className="flex-1 space-y-4 pt-6">
        <div className="flex items-center justify-between space-y-2">
          <h2 className="text-3xl font-bold tracking-tight">Promotions</h2>
          <div className="flex items-center space-x-2">
            <CalendarDateRangePicker />
            <Button onClick={handleSheetOpen}>
              Add Promotion
            </Button>
            <Sheet
              open={promotionId ? true : false}
              onOpenChange={() => handleSheetClose}
            >
              <SheetContent className="min-w-[1000px] sm:max-w-[600px] overflow-y-auto">
                <SheetHeader>
                  <SheetTitle>
                    {promotionId === "new" ? "Add Promotion" : "Edit Promotion"}
                  </SheetTitle>
                  <SheetDescription>
                    Create promotional offers to attract and retain customers.
                    Set banners, URLs, and target audiences.
                  </SheetDescription>
                </SheetHeader>

                {formLoading ? (
                  <div className="space-y-8 pt-10">
                    <div className="space-y-2">
                      <div className="h-5 w-[100px] animate-pulse rounded bg-gray-200"></div>
                      <div className="h-10 w-full animate-pulse rounded bg-gray-200"></div>
                      <div className="h-4 w-[150px] animate-pulse rounded bg-gray-200"></div>
                    </div>
                    <div className="flex justify-end">
                      <div className="h-10 w-[100px] animate-pulse rounded bg-gray-200"></div>
                    </div>
                  </div>
                ) : (
                  <Form {...form}>
                    <form
                      onSubmit={form.handleSubmit(onSubmit)}
                      className="space-y-6 pt-6"
                    >
                      {/* Image Upload */}
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Promotion Image</label>
                        <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                          {imagePreview ? (
                            <div className="space-y-4">
                              <div className="relative mx-auto w-32 h-32">
                                <Image
                                  src={imagePreview}
                                  alt="Preview"
                                  fill
                                  className="object-cover rounded-lg"
                                />
                              </div>
                              <Button
                                type="button"
                                variant="outline"
                                onClick={() => {
                                  setImagePreview(null);
                                  setImageFile(null);
                                }}
                              >
                                Remove Image
                              </Button>
                            </div>
                          ) : (
                            <div className="space-y-4">
                              <Upload className="mx-auto h-12 w-12 text-gray-400" />
                              <div>
                                <label htmlFor="image-upload" className="cursor-pointer">
                                  <span className="text-indigo-600 hover:text-indigo-500">
                                    Upload an image
                                  </span>
                                  <input
                                    id="image-upload"
                                    type="file"
                                    accept="image/*"
                                    className="sr-only"
                                    onChange={handleImageUpload}
                                  />
                                </label>
                                <p className="text-gray-500">or drag and drop</p>
                              </div>
                              <p className="text-xs text-gray-500">
                                PNG, JPG, WEBP up to 10MB
                              </p>
                            </div>
                          )}
                        </div>
                      </div>

                      <FormField
                        control={form.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Title</FormLabel>
                            <FormControl>
                              <Input placeholder="Enter promotion title" {...field} />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="url"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>URL (Optional)</FormLabel>
                            <FormControl>
                              <Input placeholder="https://example.com" {...field} />
                            </FormControl>
                            <FormDescription>
                              The URL users will be directed to when clicking this promotion
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-2 gap-4">
                        <FormField
                          control={form.control}
                          name="startDate"
                          render={({ field }) => (
                            <FormItem className="flex flex-col">
                              <FormLabel>Start Date</FormLabel>
                              <Popover>
                                <PopoverTrigger asChild>
                                  <FormControl>
                                    <Button
                                      variant="outline"
                                      className={cn(
                                        "pl-3 text-left font-normal",
                                        !field.value && "text-muted-foreground"
                                      )}
                                    >
                                      {field.value ? (
                                        format(field.value, "PPP")
                                      ) : (
                                        <span>Pick a date</span>
                                      )}
                                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                    </Button>
                                  </FormControl>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0" align="start">
                                  <Calendar
                                    mode="single"
                                    selected={field.value}
                                    onSelect={field.onChange}
                                    disabled={(date) =>
                                      date < new Date(new Date().setHours(0, 0, 0, 0))
                                    }
                                    initialFocus
                                  />
                                </PopoverContent>
                              </Popover>
                              <FormMessage />
                            </FormItem>
                          )}
                        />

                        <FormField
                          control={form.control}
                          name="endDate"
                          render={({ field }) => (
                            <FormItem className="flex flex-col">
                              <FormLabel>End Date</FormLabel>
                              <Popover>
                                <PopoverTrigger asChild>
                                  <FormControl>
                                    <Button
                                      variant="outline"
                                      className={cn(
                                        "pl-3 text-left font-normal",
                                        !field.value && "text-muted-foreground"
                                      )}
                                    >
                                      {field.value ? (
                                        format(field.value, "PPP")
                                      ) : (
                                        <span>Pick a date</span>
                                      )}
                                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                    </Button>
                                  </FormControl>
                                </PopoverTrigger>
                                <PopoverContent className="w-auto p-0" align="start">
                                  <Calendar
                                    mode="single"
                                    selected={field.value}
                                    onSelect={field.onChange}
                                    disabled={(date) =>
                                      date < new Date(new Date().setHours(0, 0, 0, 0))
                                    }
                                    initialFocus
                                  />
                                </PopoverContent>
                              </Popover>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <FormField
                        control={form.control}
                        name="targetUserType"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Target User Type</FormLabel>
                            <Select onValueChange={field.onChange} value={field.value}>
                              <FormControl>
                                <SelectTrigger>
                                  <SelectValue placeholder="Select target user type" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent>
                                <SelectItem value="ALL">All Users</SelectItem>
                                <SelectItem value="NEW_USERS">New Users</SelectItem>
                                <SelectItem value="EXISTING_USERS">Existing Users</SelectItem>
                                <SelectItem value="TRAVELERS">Travelers</SelectItem>
                                <SelectItem value="COMPANIONS">Companions</SelectItem>
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="priority"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Priority</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                placeholder="0"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                              />
                            </FormControl>
                            <FormDescription>
                              Higher priority promotions are shown first
                            </FormDescription>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="isActive"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-start space-x-3 space-y-0">
                            <FormControl>
                              <Checkbox
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <div className="space-y-1 leading-none">
                              <FormLabel>
                                Active Promotion
                              </FormLabel>
                              <FormDescription>
                                This promotion will be available to users when active
                              </FormDescription>
                            </div>
                          </FormItem>
                        )}
                      />

                      <div className="flex justify-end">
                        <Button
                          disabled={isLoading}
                          type="submit"
                          size="lg"
                          className="font-medium w-full"
                        >
                          Save Promotion
                        </Button>
                      </div>
                    </form>
                  </Form>
                )}
                <SheetClose
                  className="absolute right-4 top-4 rounded-sm"
                  onClick={handleSheetClose}
                >
                  <X className="h-4 w-4" />
                </SheetClose>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </div>
    </>
  );
};

export default Heading;