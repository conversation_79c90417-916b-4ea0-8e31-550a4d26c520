import { z } from "zod";

export const promotionSchema = z.object({
  id: z.string(),
  title: z.string(),
  imageUrl: z.string().nullable(),
  imageFileId: z.string().nullable(),
  url: z.string().nullable(),
  startDate: z.date(),
  endDate: z.date(),
  isActive: z.boolean(),
  targetUserType: z.enum(["ALL", "NEW_USERS", "EXISTING_USERS", "TRAVELERS", "COMPANIONS"]),
  priority: z.number(),
  createdBy: z.string().nullable(),
  createdAt: z.date(),
  updatedAt: z.date(),
  analytics: z.object({
    views: z.number(),
    clicks: z.number(),
  }),
});

export type Promotion = z.infer<typeof promotionSchema>;

export const createPromotionSchema = z.object({
  title: z.string().min(1, "Title is required"),
  imageUrl: z.string().url().optional(),
  imageFileId: z.string().optional(),
  url: z.string().url().optional(),
  startDate: z.date(),
  endDate: z.date(),
  isActive: z.boolean().default(true),
  targetUserType: z.enum(["ALL", "NEW_USERS", "EXISTING_USERS", "TRAVELERS", "COMPANIONS"]).default("ALL"),
  priority: z.number().default(0),
});
