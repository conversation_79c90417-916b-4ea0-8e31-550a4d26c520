import { env } from "@/env";
import { Client, Storage, ID } from "node-appwrite";
import { tryCatch } from "./try-catch";

// Initialize the Appwrite client
const client = new Client();

client
  .setEndpoint(env.APPWRITE_ENDPOINT)
  .setProject(env.APPWRITE_PROJECT_ID)
  .setKey(env.APPWRITE_API_KEY);

const storage = new Storage(client);

// Storage bucket ID for promotions - using the same bucket as mobile app
const STORAGE_BUCKET_ID = env.STORAGE_BUCKET_ID; // This should match mobile app's STORAGE_BUCKET_ID

export interface UploadResult {
  fileId: string;
  url: string;
}

export class AppwriteStorageService {
  static async uploadPromotionImage(file: File): Promise<UploadResult> {
    try {
      // Upload file to Appwrite storage with proper permissions
      const { data: uploadedFile, error } = await tryCatch(
        storage.createFile(
          STORAGE_BUCKET_ID,
          ID.unique(),
          file,
          ["read(\"any\")", "write(\"any\")"] // Permissions - allow anyone to read and write
        )
      );

      if (error) {
        console.error("Appwrite upload error:", error);
        throw new Error(`Failed to upload image: ${error.message}`);
      }

      // Get the file URL
      const fileUrl = `${env.APPWRITE_ENDPOINT}/storage/buckets/${STORAGE_BUCKET_ID}/files/${uploadedFile.$id}/view?project=${env.APPWRITE_PROJECT_ID}`;

      return {
        fileId: uploadedFile.$id,
        url: fileUrl,
      };
    } catch (error) {
      console.error("Error uploading file to Appwrite:", error);
      throw new Error(`Failed to upload image to storage: ${error instanceof Error ? error.message : "Unknown error"}`);
    }
  }

  static async deletePromotionImage(fileId: string): Promise<void> {
    try {
      await storage.deleteFile(STORAGE_BUCKET_ID, fileId);
    } catch (error) {
      console.error("Error deleting file from Appwrite:", error);
      throw new Error("Failed to delete image from storage");
    }
  }

  static getFileUrl(fileId: string): string {
    return `${env.APPWRITE_ENDPOINT}/storage/buckets/${STORAGE_BUCKET_ID}/files/${fileId}/view?project=${env.APPWRITE_PROJECT_ID}`;
  }

  static async getFileInfo(fileId: string) {
    try {
      return await storage.getFile(STORAGE_BUCKET_ID, fileId);
    } catch (error) {
      console.error("Error getting file info:", error);
      throw new Error("Failed to get file information");
    }
  }
}

// Alternative implementation using FormData for better browser compatibility
export class AppwriteStorageServiceBrowser {
  private static client = new Client()
    .setEndpoint(env.APPWRITE_ENDPOINT)
    .setProject(env.APPWRITE_PROJECT_ID);

  static async uploadPromotionImageFromBrowser(file: File): Promise<UploadResult> {
    try {
      // For browser-based uploads, we'll need to use a different approach
      // This would typically involve a server endpoint that handles the upload
      const formData = new FormData();
      formData.append("file", file);
      formData.append("bucketId", STORAGE_BUCKET_ID);

      const response = await fetch("/api/upload-promotion-image", {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error("Upload failed");
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("Error uploading file:", error);
      throw new Error("Failed to upload image");
    }
  }
}

export { STORAGE_BUCKET_ID };
