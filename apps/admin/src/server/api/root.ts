import { postRouter } from "@/server/api/routers/post";
import { airportRouter } from "@/server/api/routers/airports";
import { cityRouter } from "@/server/api/routers/cities";
import { languageRouter } from "@/server/api/routers/languages";
import { deletionRequestsRouter } from "@/server/api/routers/deletion-requests";
import { promotionRouter } from "@/server/api/routers/promotions";
import { createCallerFactory, createTRPCRouter } from "@/server/api/trpc";

/**
 * This is the primary router for your server.
 *
 * All routers added in /api/routers should be manually added here.
 */
export const appRouter = createTRPCRouter({
  post: postRouter,
  airports: airportRouter,
  cities: cityRouter,
  languages: languageRouter,
  deletionRequests: deletionRequestsRouter,
  promotions: promotionRouter,
});

// export type definition of API
export type AppRouter = typeof appRouter;

/**
 * Create a server-side caller for the tRPC API.
 * @example
 * const trpc = createCaller(createContext);
 * const res = await trpc.post.all();
 *       ^? Post[]
 */
export const createCaller = createCallerFactory(appRouter);
