import { z } from "zod";

import { createTR<PERSON><PERSON>outer, publicProcedure } from "@/server/api/trpc";
import { posts } from "@repo/db";

export const postRouter = createTRPCRouter({
  hello: publicProcedure
    .input(z.object({ text: z.string() }))
    .query(({ input }) => {
      return {
        greeting: `Hello ${input.text}`,
      };
    }),

  create: publicProcedure
    .input(z.object({ name: z.string().min(1) }))
    .mutation(async ({ ctx, input }) => {
      await ctx.db.insert(posts).values({
        name: input.name,
      });
    }),

  getLatest: publicProcedure.query(async ({ ctx }) => {
    const result = await ctx.db
      .select()
      .from(posts)
      .orderBy(posts.createdAt)
      .limit(1);
    return result[0] ?? null;
  }),
});
