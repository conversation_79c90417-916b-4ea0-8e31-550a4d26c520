import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "@/server/api/trpc";
import { languages } from "@repo/db/schema";
import { eq } from "@repo/db";

export const languageRouter = createTRPCRouter({
  getAll: publicProcedure.query(async ({ ctx }) => {
    return await ctx.db.select().from(languages).orderBy(languages.name);
  }),

  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const result = await ctx.db
        .select()
        .from(languages)
        .where(eq(languages.id, input.id))
        .limit(1);
      return result[0];
    }),

  create: publicProcedure
    .input(
      z.object({
        name: z.string().min(1),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.insert(languages).values({
        name: input.name,
      }).returning();
    }),

  update: publicProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .update(languages)
        .set({
          name: input.name,
          updatedAt: new Date(),
        })
        .where(eq(languages.id, input.id))
        .returning();
    }),

  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .delete(languages)
        .where(eq(languages.id, input.id))
        .returning();
    }),
}); 