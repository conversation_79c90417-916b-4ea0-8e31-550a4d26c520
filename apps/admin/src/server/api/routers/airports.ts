import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "@/server/api/trpc";
import { airports } from "@repo/db/schema";
import { eq } from "@repo/db";

export const airportRouter = createTRPCRouter({
  getAll: publicProcedure.query(async ({ ctx }) => {
    return await ctx.db.select().from(airports).orderBy(airports.name);
  }),

  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const result = await ctx.db
        .select()
        .from(airports)
        .where(eq(airports.id, input.id))
        .limit(1);
      return result[0];
    }),

  create: publicProcedure
    .input(
      z.object({
        name: z.string().min(1),
        locationCity: z.string().min(1),
        shortCode: z.string().min(1).max(10),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.insert(airports).values({
        name: input.name,
        locationCity: input.locationCity,
        shortCode: input.shortCode,
      }).returning();
    }),

  update: publicProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1),
        locationCity: z.string().min(1),
        shortCode: z.string().min(1).max(10),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .update(airports)
        .set({
          name: input.name,
          locationCity: input.locationCity,
          shortCode: input.shortCode,
          updatedAt: new Date(),
        })
        .where(eq(airports.id, input.id))
        .returning();
    }),

  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .delete(airports)
        .where(eq(airports.id, input.id))
        .returning();
    }),
}); 