import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "@/server/api/trpc";
import { 
  promotions, 
  promotionAnalytics
} from "@repo/db/schema";
import { eq, sql, desc, asc, and, gte, lte, count } from "@repo/db";

// Validation schemas
const createPromotionSchema = z.object({
  title: z.string().min(1, "Title is required"),
  imageUrl: z.string().url().optional(),
  imageFileId: z.string().optional(),
  url: z.string().url().optional(),
  startDate: z.date(),
  endDate: z.date(),
  isActive: z.boolean().default(true),
  targetUserType: z.enum(["ALL", "NEW_USERS", "EXISTING_USERS", "TRAVELERS", "COMPANIONS"]).default("ALL"),
  priority: z.number().default(0),
});

const updatePromotionSchema = createPromotionSchema.extend({
  id: z.string(),
});

export const promotionRouter = createTRPCRouter({
  // Get all promotions with analytics
  getAll: publicProcedure
    .input(z.object({
      limit: z.number().min(1).max(100).default(10),
      offset: z.number().min(0).default(0),
      sortBy: z.enum(["createdAt", "priority", "title", "startDate", "endDate"]).default("createdAt"),
      sortOrder: z.enum(["asc", "desc"]).default("desc"),
      status: z.enum(["all", "active", "inactive", "expired"]).default("all"),
    }).optional())
    .query(async ({ ctx, input = {} }) => {
      const { limit = 10, offset = 0, sortBy = "createdAt", sortOrder = "desc", status = "all" } = input;
      
      let whereConditions = [];
      const now = new Date();
      
      // Filter by status
      if (status === "active") {
        whereConditions.push(
          and(
            eq(promotions.isActive, true),
            lte(promotions.startDate, now),
            gte(promotions.endDate, now)
          )
        );
      } else if (status === "inactive") {
        whereConditions.push(eq(promotions.isActive, false));
      } else if (status === "expired") {
        whereConditions.push(lte(promotions.endDate, now));
      }

      // Map sortBy to valid column names
      const validColumns = {
        'title': promotions.title,
        'createdAt': promotions.createdAt,
        'updatedAt': promotions.updatedAt,
        'startDate': promotions.startDate,
        'endDate': promotions.endDate,
        'isActive': promotions.isActive,
        'priority': promotions.priority,
      };
      
      const orderByColumn = validColumns[sortBy as keyof typeof validColumns] || promotions.createdAt;
      const orderDirection = sortOrder === "asc" ? asc : desc;

      const result = await ctx.db
        .select({
          promotion: promotions,
          viewCount: sql<number>`COALESCE(analytics.view_count, 0)`,
          clickCount: sql<number>`COALESCE(analytics.click_count, 0)`,
        })
        .from(promotions)
        .leftJoin(
          sql`(
            SELECT 
              promotion_id,
              COUNT(*) as view_count,
              COUNT(*) as click_count
            FROM promotion_analytics 
            GROUP BY promotion_id
          ) analytics`,
          sql`analytics.promotion_id = ${promotions.id}`
        )
        .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
        .orderBy(orderDirection(orderByColumn))
        .limit(limit)
        .offset(offset);

      return result.map(row => ({
        ...row.promotion,
        analytics: {
          views: row.viewCount,
          clicks: row.clickCount,
        }
      }));
    }),

  // Get total count for pagination
  getCount: publicProcedure
    .input(z.object({
      status: z.enum(["all", "active", "inactive", "expired"]).default("all"),
    }).optional())
    .query(async ({ ctx, input = {} }) => {
      const { status = "all" } = input;
      
      let whereConditions = [];
      const now = new Date();
      
      if (status === "active") {
        whereConditions.push(
          and(
            eq(promotions.isActive, true),
            lte(promotions.startDate, now),
            gte(promotions.endDate, now)
          )
        );
      } else if (status === "inactive") {
        whereConditions.push(eq(promotions.isActive, false));
      } else if (status === "expired") {
        whereConditions.push(lte(promotions.endDate, now));
      }

      const result = await ctx.db
        .select({ count: count() })
        .from(promotions)
        .where(whereConditions.length > 0 ? and(...whereConditions) : undefined);

      return result[0]?.count ?? 0;
    }),

  // Get promotion by ID
  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const result = await ctx.db
        .select()
        .from(promotions)
        .where(eq(promotions.id, input.id))
        .limit(1);
      return result[0];
    }),

  // Create new promotion
  create: publicProcedure
    .input(createPromotionSchema)
    .mutation(async ({ ctx, input }) => {
      // Validate dates
      if (input.endDate <= input.startDate) {
        throw new Error("End date must be after start date");
      }

      const result = await ctx.db.insert(promotions).values({
        title: input.title,
        imageUrl: input.imageUrl,
        imageFileId: input.imageFileId,
        url: input.url,
        startDate: input.startDate,
        endDate: input.endDate,
        isActive: input.isActive,
        targetUserType: input.targetUserType,
        priority: input.priority,
        // TODO: Add createdBy when auth is available
        // createdBy: ctx.session?.user?.id,
      }).returning();

      return result[0];
    }),

  // Update promotion
  update: publicProcedure
    .input(updatePromotionSchema)
    .mutation(async ({ ctx, input }) => {
      // Validate dates
      if (input.endDate <= input.startDate) {
        throw new Error("End date must be after start date");
      }

      const result = await ctx.db
        .update(promotions)
        .set({
          title: input.title,
          imageUrl: input.imageUrl,
          imageFileId: input.imageFileId,
          url: input.url,
          startDate: input.startDate,
          endDate: input.endDate,
          isActive: input.isActive,
          targetUserType: input.targetUserType,
          priority: input.priority,
          updatedAt: new Date(),
        })
        .where(eq(promotions.id, input.id))
        .returning();

      return result[0];
    }),

  // Toggle promotion active status
  toggleActive: publicProcedure
    .input(z.object({ 
      id: z.string(), 
      isActive: z.boolean() 
    }))
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.db
        .update(promotions)
        .set({
          isActive: input.isActive,
          updatedAt: new Date(),
        })
        .where(eq(promotions.id, input.id))
        .returning();

      return result[0];
    }),

  // Delete promotion
  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      const result = await ctx.db
        .delete(promotions)
        .where(eq(promotions.id, input.id))
        .returning();

      return result[0];
    }),

  // Get promotion analytics
  getAnalytics: publicProcedure
    .input(z.object({ 
      promotionId: z.string(),
      dateRange: z.object({
        from: z.date(),
        to: z.date(),
      }).optional(),
    }))
    .query(async ({ ctx, input }) => {
      let whereConditions = [eq(promotionAnalytics.promotionId, input.promotionId)];
      
      if (input.dateRange) {
        whereConditions.push(
          and(
            gte(promotionAnalytics.createdAt, input.dateRange.from),
            lte(promotionAnalytics.createdAt, input.dateRange.to)
          )
        );
      }

      const analytics = await ctx.db
        .select({
          count: count(),
          date: sql<string>`DATE(${promotionAnalytics.createdAt})`,
        })
        .from(promotionAnalytics)
        .where(and(...whereConditions))
        .groupBy(sql`DATE(${promotionAnalytics.createdAt})`)
        .orderBy(sql`DATE(${promotionAnalytics.createdAt})`);

      return {
        dailyAnalytics: analytics,
      };
    }),

  // Get promotion usage stats
  getUsageStats: publicProcedure
    .query(async ({ ctx }) => {
      const now = new Date();
      
      const stats = await ctx.db
        .select({
          totalPromotions: count(),
          activePromotions: sql<number>`COUNT(CASE WHEN ${promotions.isActive} = true AND ${promotions.startDate} <= ${now} AND ${promotions.endDate} >= ${now} THEN 1 END)`,
          expiredPromotions: sql<number>`COUNT(CASE WHEN ${promotions.endDate} < ${now} THEN 1 END)`,
          scheduledPromotions: sql<number>`COUNT(CASE WHEN ${promotions.startDate} > ${now} THEN 1 END)`,
        })
        .from(promotions);

      const totalInteractions = await ctx.db
        .select({ count: count() })
        .from(promotionAnalytics);

      return {
        ...stats[0],
        totalViews: totalInteractions[0]?.count ?? 0,
        totalClicks: totalInteractions[0]?.count ?? 0,
      };
    }),
});
