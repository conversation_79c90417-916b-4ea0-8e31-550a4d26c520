import { z } from "zod";
import { createTR<PERSON><PERSON>outer, publicProcedure } from "@/server/api/trpc";
import { accountDeletionRequests } from "@repo/db";
import { eq, desc } from "drizzle-orm";

export const deletionRequestsRouter = createTRPCRouter({
  getAll: publicProcedure.query(async ({ ctx }) => {
    return await ctx.db
      .select()
      .from(accountDeletionRequests)
      .orderBy(desc(accountDeletionRequests.createdAt));
  }),

  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const result = await ctx.db
        .select()
        .from(accountDeletionRequests)
        .where(eq(accountDeletionRequests.id, input.id))
        .limit(1);
      return result[0];
    }),

  updateStatus: publicProcedure
    .input(
      z.object({
        id: z.string(),
        status: z.enum(["PENDING", "APPROVED", "REJECTED"]),
        adminNotes: z.string().optional(),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .update(accountDeletionRequests)
        .set({
          status: input.status,
          adminNotes: input.adminNotes,
          updatedAt: new Date(),
        })
        .where(eq(accountDeletionRequests.id, input.id))
        .returning();
    }),

  getPendingCount: publicProcedure.query(async ({ ctx }) => {
    const result = await ctx.db
      .select({ count: accountDeletionRequests.id })
      .from(accountDeletionRequests)
      .where(eq(accountDeletionRequests.status, "PENDING"));
    
    return result.length;
  }),
}); 