import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "@/server/api/trpc";
import { cities } from "@repo/db/schema";
import { eq } from "@repo/db";

export const cityRouter = createTRPCRouter({
  getAll: publicProcedure.query(async ({ ctx }) => {
    return await ctx.db.select().from(cities).orderBy(cities.name);
  }),

  getById: publicProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const result = await ctx.db
        .select()
        .from(cities)
        .where(eq(cities.id, input.id))
        .limit(1);
      return result[0];
    }),

  create: publicProcedure
    .input(
      z.object({
        name: z.string().min(1),
        country: z.string().min(1),
        state: z.string().min(1),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db.insert(cities).values({
        name: input.name,
        country: input.country,
        state: input.state,
      }).returning();
    }),

  update: publicProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string().min(1),
        country: z.string().min(1),
        state: z.string().min(1),
      })
    )
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .update(cities)
        .set({
          name: input.name,
          country: input.country,
          state: input.state,
          updatedAt: new Date(),
        })
        .where(eq(cities.id, input.id))
        .returning();
    }),

  delete: publicProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ ctx, input }) => {
      return await ctx.db
        .delete(cities)
        .where(eq(cities.id, input.id))
        .returning();
    }),
}); 