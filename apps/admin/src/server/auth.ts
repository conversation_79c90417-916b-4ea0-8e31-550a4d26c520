import { betterAuth } from "better-auth";
import { drizzleAdapter } from "better-auth/adapters/drizzle";
import { adminAccount, adminSession, adminUser, adminVerification } from "@repo/db/schema";
import { db } from "@repo/db";

export const auth = betterAuth({
  database: drizzleAdapter(db, {
    provider: "pg",
    schema: {
      user: adminUser,
      account: adminAccount,
      session: adminSession,
      verification: adminVerification,
    },
  }),
  emailAndPassword: {
    enabled: true,
    sendResetPassword: async ({ user, url }) => {
      console.log("sendResetPassword", user, url);
    },
  },
});