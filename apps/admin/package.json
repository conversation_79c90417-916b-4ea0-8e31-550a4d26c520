{"name": "admin", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "dev": "next dev --turbo -p 3000", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit", "seed:airports": "pnpm with-env pnpx tsx seed-airports.ts", "with-env": "dotenv -e ../../.env --"}, "dependencies": {"@hookform/resolvers": "^3.9.1", "@radix-ui/react-alert-dialog": "catalog:", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.4", "@radix-ui/react-icons": "^1.3.2", "@repo/db": "workspace:*", "@repo/tailwind-config": "workspace:*", "@t3-oss/env-nextjs": "^0.12.0", "@tailwindcss/typography": "catalog:", "@tanstack/react-query": "^5.69.0", "@tanstack/react-table": "^8.20.5", "@trpc/client": "^11.0.0", "@trpc/react-query": "^11.0.0", "@trpc/server": "^11.0.0", "@uidotdev/usehooks": "^2.4.1", "autoprefixer": "catalog:", "better-auth": "^1.3.2", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "geist": "^1.3.0", "lucide-react": "^0.460.0", "next": "^15.2.3", "next-themes": "^0.4.3", "node-appwrite": "catalog:", "postgres": "^3.4.4", "react": "catalog:react19", "react-day-picker": "8.10.1", "react-dom": "catalog:react19", "react-hook-form": "^7.53.0", "recharts": "^2.13.3", "server-only": "^0.0.1", "superjson": "^2.2.1", "sonner": "^1.7.0", "tailwind-merge": "^2.5.4", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.0.15", "@types/node": "^20.14.10", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "eslint": "^9.23.0", "eslint-config-next": "^15.2.3", "postcss": "^8.5.3", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "catalog:", "typescript": "catalog:", "typescript-eslint": "^8.27.0"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "pnpm@10.13.1"}