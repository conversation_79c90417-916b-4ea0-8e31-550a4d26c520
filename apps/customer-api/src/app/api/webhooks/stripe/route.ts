import { appRouter } from '@repo/customer-api';
import { createTRPCContext } from '@repo/customer-api';
import { NextResponse, type NextRequest } from 'next/server';

export async function POST(req: NextRequest) {
  // 1. Read the body and headers ONCE
  const rawBody = await req.text();
  const signature = req.headers.get('stripe-signature')!;

  console.log("signature", signature);
  console.log("rawBody", rawBody);

  // 2. Create a tRPC caller
  // We pass the rawBody and signature into the context
  const caller = appRouter.createCaller(
    await createTRPCContext({
      headers: req.headers,
      rawBody: rawBody,
      signature: signature,
    }),
  );

  try {
    // 3. Call the specific procedure and await its result
    await caller.stripeWebhook.handleWebhook({})
    return NextResponse.json({ received: true }, { status: 200 });
  } catch (error: unknown) {
    console.error('Webhook handler failed:', error);
    return NextResponse.json(
      { message: error instanceof Error ? error.message : 'Webhook handler failed' },
      { status: 400 }, // Use 400 for bad requests, like signature errors
    );
  }
}