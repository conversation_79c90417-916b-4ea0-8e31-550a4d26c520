{"name": "customer-api", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "check": "next lint && tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "dev": "next dev --port 3001 --turbo", "format:check": "prettier --check \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "format:write": "prettier --write \"**/*.{ts,tsx,js,jsx,mdx}\" --cache", "lint": "next lint", "lint:fix": "next lint --fix", "preview": "next build && next start", "start": "next start", "typecheck": "tsc --noEmit"}, "dependencies": {"@repo/customer-api": "workspace:*", "@t3-oss/env-nextjs": "^0.12.0", "@tanstack/react-query": "^5.69.0", "@trpc/client": "catalog:", "@trpc/react-query": "catalog:", "@trpc/server": "catalog:", "drizzle-orm": "catalog:", "next": "^15.2.3", "postgres": "^3.4.4", "react": "catalog:react19", "react-dom": "catalog:react19", "server-only": "catalog:", "superjson": "catalog:", "zod": "catalog:"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@types/node": "catalog:", "@types/react": "catalog:react19", "@types/react-dom": "catalog:react19", "eslint": "catalog:", "eslint-config-next": "catalog:", "eslint-plugin-drizzle": "catalog:", "prettier": "catalog:", "typescript": "catalog:", "typescript-eslint": "catalog:"}, "ct3aMetadata": {"initVersion": "7.39.3"}, "packageManager": "pnpm@10.13.1"}