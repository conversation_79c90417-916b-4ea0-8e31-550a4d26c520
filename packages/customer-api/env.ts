import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";


export const env = createEnv({
  shared: {
    NODE_ENV: z
      .enum(["development", "production", "test"])
      .default("development"),
  },
  /**
   * Specify your server-side environment variables schema here.
   * This way you can ensure the app isn't built with invalid env vars.
   */
  server: {
    APPWRITE_API_KEY: z.string().nonempty(),
    APPWRITE_ENDPOINT: z.string().nonempty(),
    APPWRITE_PROJECT_ID: z.string().nonempty(),
    STRIPE_SECRET_KEY: z.string().nonempty(),
    STRIPE_WEBHOOK_SECRET: z.string().nonempty(),
    // GetStream Chat Configuration
    STREAM_API_KEY: z.string().nonempty(),
    STREAM_API_SECRET: z.string().nonempty(),
    STREAM_APP_ID: z.string().nonempty(),
  },

  client: {
  },

  /**
   * Destructure all variables from `process.env` to make sure they aren't tree-shaken away.
   */
  experimental__runtimeEnv: {
    NODE_ENV: process.env.NODE_ENV,
  },
  skipValidation:
    !!process.env.CI || process.env.npm_lifecycle_event === "lint",
});
