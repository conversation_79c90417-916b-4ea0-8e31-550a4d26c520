"use server";

import { createSessionClient } from "./appwrite/config";

export interface AuthContext {
  user: Record<string, unknown> | null;
  session: string | null;
}

export const getAuthFromHeaders = async (headers: Headers): Promise<AuthContext> => {
  const session = headers.get("x-appwrite-session") ?? headers.get("authorization")?.replace("Bearer ", "") ?? null;
  
  if (!session) {
    return { user: null, session: null };
  }

  try {
    const { account } = createSessionClient(session);
    const user = await account.get();
    return { user, session };
  } catch (error) {
    console.error("Auth error:", error);
    return { user: null, session: null };
  }
};

// Legacy auth function for backward compatibility
const auth: {
  user: Record<string, unknown> | null;
  sessionCookie: { value: string } | null;
  getUser: () => Promise<Record<string, unknown> | null>;
} = {
  user: null,
  sessionCookie: null,
  getUser: async () => {
    // This is kept for backward compatibility but should not be used
    await Promise.resolve();
    return null;
  },
};

export default async () => {
  await Promise.resolve();
  return auth;
};