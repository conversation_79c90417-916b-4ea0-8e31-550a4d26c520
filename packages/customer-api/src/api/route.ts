import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getAuthFromHeaders } from "../auth";

export async function GET(request: NextRequest) {
  try {
    const auth = await getAuthFromHeaders(request.headers);
    
    if (!auth.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const user = auth.user as { $id: string; email: string; name?: string };

    return NextResponse.json({
      message: "Authenticated successfully",
      user: {
        id: user.$id,
        email: user.email,
        name: user.name,
      },
    });
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const auth = await getAuthFromHeaders(request.headers);
    
    if (!auth.user) {
      return NextResponse.json(
        { error: "Unauthorized" },
        { status: 401 }
      );
    }

    const user = auth.user as { $id: string; email: string };
    const body = await request.json() as Record<string, unknown>;
    
    return NextResponse.json({
      message: "Request processed successfully",
      user: {
        id: user.$id,
        email: user.email,
      },
      data: body,
    });
  } catch (error) {
    console.error("API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
} 