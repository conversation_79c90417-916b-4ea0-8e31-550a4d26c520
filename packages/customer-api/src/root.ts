import { createTRPCRouter } from "./trpc";
import { airportRouter } from "./router/airports";
import { languageRouter } from "./router/languages";
import { bookingCompanionsRouter } from "./router/booking-companions";
import { bookingTravelersRouter } from "./router/booking-travelers";
import { connectionRequestsRouter } from "./router/connection-requests";
import { stripeTravelerRouter } from "./router/stripe-traveler";
import { stripeCompanionRouter } from "./router/stripe-companion";
import { stripeWebhookRouter } from "./router/stripe-webhook";
import { userProfileRouter } from "./router/user-profiles";
import { chatRouter } from "./router/chat";
import { promotionRouter } from "./router/promotions";

export const appRouter = createTRPCRouter({
  airports: airportRouter,
  languages: languageRouter,
  bookingCompanions: bookingCompanionsRouter,
  bookingTravelers: bookingTravelersRouter,
  connectionRequests: connectionRequestsRouter,
  stripeTraveler: stripeTravelerRouter,
  stripeCompanion: stripeCompanionRouter,
  stripeWebhook: stripeWebhookRouter,
  userProfiles: userProfileRouter,
  chat: chatRouter,
  promotions: promotionRouter,
});

export type AppRouter = typeof appRouter;
