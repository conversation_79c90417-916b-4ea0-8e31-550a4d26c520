import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { userProfiles, userProfileLanguages, languages } from "@repo/db";
import { eq } from "drizzle-orm";
import { streamChatService } from "../services/stream-chat.service";

// Schema for updating user profile
const updateUserProfileSchema = z.object({
  name: z.string().min(1, "Name is required").min(3, "Name must be at least 3 characters"),
  lastName: z.string().optional(),
  email: z.string().email("Invalid email").optional(),
  phone: z.string().optional(),
  about: z.string().min(1, "About is required").min(5, "About must be at least 5 characters"),
  typeOfTraveler: z.enum(["SOLO", "FAMILY", "GROUP"]),
  gender: z.enum(["MALE", "FEMALE", "OTHERS"]),
  genderPreference: z.enum(["MALE", "FEMALE", "OTHERS"]).optional(),
  openToAllGenders: z.boolean().default(false),
  bookingFor: z.enum(["SELF", "FATHER", "MOTHER", "RELATIVE"]).default("SELF"),
  // typeOfUser: z.enum(["TRAVELLER", "COMPANION"]),
  languages: z.array(z.string().uuid()).optional(),
  userProfile: z.string().optional(),
  userProfileUrl: z.string().optional(),
});

// Schema for updating just the profile picture
const updateProfilePictureSchema = z.object({
  userProfile: z.string().min(1, "Profile file ID is required"),
  userProfileUrl: z.string().url("Profile URL must be a valid URL"),
});

export const userProfileRouter = createTRPCRouter({
  // Get user profile - creates empty profile if not exists
  get: protectedProcedure.query(async ({ ctx }) => {
    const appwriteUserId = ctx.session.user.$id as string;
    
    // Try to get existing profile
    let profile = await ctx.db
      .select()
      .from(userProfiles)
      .where(eq(userProfiles.appwriteUserId, appwriteUserId))
      .limit(1);

    if (profile.length === 0) {
      // Create empty profile if not exists
      const [newProfile] = await ctx.db
        .insert(userProfiles)
        .values({
          appwriteUserId,
          name: (ctx.session.user.name as string) || "",
          email: (ctx.session.user.email as string) || "",
          phone: (ctx.session.user.phone as string) || "",
          // typeOfUser: "TRAVELLER", // Default value
          typeOfTraveller: "SOLO", // Default value
          gender: "MALE", // Default value
          openToAllGenders: false,
          bookingFor: "SELF",
          about: "",
        })
        .returning();

      if (!newProfile) {
        throw new Error("Failed to create user profile");
      }

      profile = [newProfile];
    }

    // Get languages for the profile
    const profileLanguages = await ctx.db
      .select({
        languageId: userProfileLanguages.languageId,
        languageName: languages.name,
      })
      .from(userProfileLanguages)
      .innerJoin(languages, eq(userProfileLanguages.languageId, languages.id))
      .where(eq(userProfileLanguages.userProfileId, profile[0]!.id));

    return {
      ...profile[0]!,
      languages: profileLanguages.map(lang => ({
        id: lang.languageId,
        name: lang.languageName,
      })),
    };
  }),

  // Update user profile
  update: protectedProcedure
    .input(updateUserProfileSchema)
    .mutation(async ({ ctx, input }) => {
      const appwriteUserId = ctx.session.user.$id as string;
      
      // Check if profile exists
      const existingProfile = await ctx.db
        .select()
        .from(userProfiles)
        .where(eq(userProfiles.appwriteUserId, appwriteUserId))
        .limit(1);

      let profileId: string;

      if (existingProfile.length === 0) {
        // Create new profile
        const [newProfile] = await ctx.db
          .insert(userProfiles)
          .values({
            appwriteUserId,
            name: input.name,
            lastName: input.lastName,
            email: input.email,
            phone: input.phone,
            about: input.about,
            typeOfTraveller: input.typeOfTraveler,
            gender: input.gender,
            genderPreference: input.genderPreference,
            openToAllGenders: input.openToAllGenders,
            bookingFor: input.bookingFor,
            // typeOfUser: input.typeOfUser,
            userProfile: input.userProfile,
            userProfileUrl: input.userProfileUrl,
          })
          .returning();
        
        if (!newProfile) {
          throw new Error("Failed to create user profile");
        }
        
        profileId = newProfile.id;
      } else {
        // Update existing profile
        const [updatedProfile] = await ctx.db
          .update(userProfiles)
          .set({
            name: input.name,
            lastName: input.lastName,
            email: input.email,
            phone: input.phone,
            about: input.about,
            typeOfTraveller: input.typeOfTraveler,
            gender: input.gender,
            genderPreference: input.genderPreference,
            openToAllGenders: input.openToAllGenders,
            bookingFor: input.bookingFor,
            // typeOfUser: input.typeOfUser,
            userProfile: input.userProfile,
            userProfileUrl: input.userProfileUrl,
            updatedAt: new Date(),
          })
          .where(eq(userProfiles.appwriteUserId, appwriteUserId))
          .returning();
        
        if (!updatedProfile) {
          throw new Error("Failed to update user profile");
        }
        
        profileId = updatedProfile.id;
      }

      // Handle languages
      if (input.languages) {
        // Delete existing languages
        await ctx.db
          .delete(userProfileLanguages)
          .where(eq(userProfileLanguages.userProfileId, profileId));

        // Insert new languages
        if (input.languages.length > 0) {
          await ctx.db
            .insert(userProfileLanguages)
            .values(
              input.languages.map(languageId => ({
                userProfileId: profileId,
                languageId,
              }))
            );
        }
      }

      // Return updated profile with languages
      const updatedProfile = await ctx.db
        .select()
        .from(userProfiles)
        .where(eq(userProfiles.id, profileId))
        .limit(1);

      const profileLanguages = await ctx.db
        .select({
          languageId: userProfileLanguages.languageId,
          languageName: languages.name,
        })
        .from(userProfileLanguages)
        .innerJoin(languages, eq(userProfileLanguages.languageId, languages.id))
        .where(eq(userProfileLanguages.userProfileId, profileId));

      // Upsert Stream chat user if name or image changed
      try {
        await streamChatService.updateUserProfile(
          appwriteUserId,
          input.name,
          input.userProfileUrl
        );
      } catch (error) {
        console.error('Error upserting Stream chat user:', error);
        // Don't throw error here as profile update was successful
        // Stream user update failure shouldn't break the profile update
      }

      return {
        ...updatedProfile[0]!,
        languages: profileLanguages.map(lang => ({
          id: lang.languageId,
          name: lang.languageName,
        })),
      };
    }),

  // Update just the profile picture
  updateProfilePicture: protectedProcedure
    .input(updateProfilePictureSchema)
    .mutation(async ({ ctx, input }) => {
      const appwriteUserId = ctx.session.user.$id as string;
      
      // Check if profile exists
      const existingProfile = await ctx.db
        .select()
        .from(userProfiles)
        .where(eq(userProfiles.appwriteUserId, appwriteUserId))
        .limit(1);

      let profileId: string;

      if (existingProfile.length === 0) {
        // Create new profile with just the profile picture
        const [newProfile] = await ctx.db
          .insert(userProfiles)
          .values({
            appwriteUserId,
            name: (ctx.session.user.name as string) || "",
            email: (ctx.session.user.email as string) || "",
            phone: (ctx.session.user.phone as string) || "",
            typeOfTraveller: "SOLO", // Default value
            gender: "MALE", // Default value
            openToAllGenders: false,
            bookingFor: "SELF",
            about: "",
            userProfile: input.userProfile,
            userProfileUrl: input.userProfileUrl,
          })
          .returning();
        
        if (!newProfile) {
          throw new Error("Failed to create user profile");
        }
        
        profileId = newProfile.id;
      } else {
        // Update existing profile with just the profile picture
        const [updatedProfile] = await ctx.db
          .update(userProfiles)
          .set({
            userProfile: input.userProfile,
            userProfileUrl: input.userProfileUrl,
            updatedAt: new Date(),
          })
          .where(eq(userProfiles.appwriteUserId, appwriteUserId))
          .returning();
        
        if (!updatedProfile) {
          throw new Error("Failed to update user profile picture");
        }
        
        profileId = updatedProfile.id;
      }

      // Return updated profile with languages
      const updatedProfile = await ctx.db
        .select()
        .from(userProfiles)
        .where(eq(userProfiles.id, profileId))
        .limit(1);

      const profileLanguages = await ctx.db
        .select({
          languageId: userProfileLanguages.languageId,
          languageName: languages.name,
        })
        .from(userProfileLanguages)
        .innerJoin(languages, eq(userProfileLanguages.languageId, languages.id))
        .where(eq(userProfileLanguages.userProfileId, profileId));

      // Upsert Stream chat user with updated image
      try {
        await streamChatService.updateUserProfile(
          appwriteUserId,
          updatedProfile[0]!.name,
          input.userProfileUrl
        );
      } catch (error) {
        console.error('Error upserting Stream chat user:', error);
        // Don't throw error here as profile update was successful
        // Stream user update failure shouldn't break the profile update
      }

      return {
        ...updatedProfile[0]!,
        languages: profileLanguages.map(lang => ({
          id: lang.languageId,
          name: lang.languageName,
        })),
      };
    }),
}); 