import { z } from "zod";
import { eq, and, ne, desc, inArray } from "drizzle-orm";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { 
  bookingCompanions, 
  bookingCompanionDestinations,
  bookingCompanionLanguages, 
  bookingTravelers,
  languages,
  connectionRequests, 
} from "@repo/db";

// Input schemas for validation
const createCompanionSchema = z.object({
  name: z.string().min(1, "Name is required").min(3, "Name must be at least 3 characters long"),
  lastName: z.string().optional(),
  about: z.string().min(1, "About is required").min(5, "About must be at least 5 characters long"),
  gender: z.enum(["MALE", "FEMALE", "OTHERS"]),
  typeOfTraveler: z.enum(["SOLO", "FAMILY", "GROUP"]),
  genderPreference: z.enum(["MALE", "FEMALE", "OTHERS"]),
  openToAllGender: z.boolean().default(false),
  languageIds: z.array(z.string().uuid()).optional(),
});

const createFlightDetailsSchema = z.object({
  flightPNR: z.string().min(1, "Flight PNR is required"),
  flightTime: z.date(),
  flightEndTime: z.date(),
  timezone: z.string().min(1, "Timezone is required"),
  bookingDestinations: z.array(z.object({
    order: z.number(),
    airports: z.string(),
  })),
  searchField: z.string().min(1, "Search field is required"),
});

const createPhotoUploadsSchema = z.object({
  travelersPhoto: z.string().min(1, "Travelers photo is required"),
  passportPhoto: z.string().optional().nullable(),
  compensationValue: z.number().min(1, "Compensation value is required"),
});

const updateCompanionSchema = createCompanionSchema.partial().extend({
  id: z.string().uuid(),
});

const getCompanionSchema = z.object({
  id: z.string().uuid(),
});

export const bookingCompanionsRouter = createTRPCRouter({
  // Create a new companion booking
  create: protectedProcedure
    .input(createCompanionSchema)
    .mutation(async ({ ctx, input }) => {
      const { languageIds, ...companionData } = input;
      
      // Create the companion booking
      const result = await ctx.db
        .insert(bookingCompanions)
        .values({
          appwriteUserId: ctx.session.user.$id as string,
          ...companionData,
        })
        .returning();

      const newCompanion = result[0];
      if (!newCompanion) {
        throw new Error("Failed to create companion booking");
      }

      // Associate languages if provided
      if (languageIds && languageIds.length > 0) {
        await ctx.db.insert(bookingCompanionLanguages).values(
          languageIds.map((languageId) => ({
            bookingCompanionId: newCompanion.id,
            languageId,
          }))
        );
      }

      // Fetch the created companion with languages
      const companionWithLanguages = await ctx.db
        .select({
          id: bookingCompanions.id,
          appwriteUserId: bookingCompanions.appwriteUserId,
          name: bookingCompanions.name,
          lastName: bookingCompanions.lastName,
          about: bookingCompanions.about,
          gender: bookingCompanions.gender,
          typeOfTraveler: bookingCompanions.typeOfTraveler,
          genderPreference: bookingCompanions.genderPreference,
          openToAllGenders: bookingCompanions.openToAllGenders,
          flightPNR: bookingCompanions.flightPNR,
          flightTime: bookingCompanions.flightTime,
          flightEndTime: bookingCompanions.flightEndTime,
          timezone: bookingCompanions.timezone,
          searchField: bookingCompanions.searchField,
          travelersPhoto: bookingCompanions.travelersPhoto,
          passportPhoto: bookingCompanions.passportPhoto,
          compensationValue: bookingCompanions.compensationValue,
          status: bookingCompanions.status,
          createdAt: bookingCompanions.createdAt,
          updatedAt: bookingCompanions.updatedAt,
          languages: languages,
        })
        .from(bookingCompanions)
        .leftJoin(bookingCompanionLanguages, eq(bookingCompanions.id, bookingCompanionLanguages.bookingCompanionId))
        .leftJoin(languages, eq(bookingCompanionLanguages.languageId, languages.id))
        .where(eq(bookingCompanions.id, newCompanion.id));

      return {
        ...newCompanion,
        languages: companionWithLanguages
          .filter((row) => row.languages)
          .map((row) => row.languages),
      };
    }),

  // Update an existing companion booking
  update: protectedProcedure
    .input(updateCompanionSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, languageIds, ...updateData } = input;

      // Verify ownership
      const existingCompanion = await ctx.db
        .select()
        .from(bookingCompanions)
        .where(
          and(
            eq(bookingCompanions.id, id),
            eq(bookingCompanions.appwriteUserId, ctx.session.user.$id as string)
          )
        )
        .limit(1);

      if (!existingCompanion.length) {
        throw new Error("Companion booking not found or access denied");
      }

      // Update the companion booking
      const [updatedCompanion] = await ctx.db
        .update(bookingCompanions)
        .set({
          ...updateData,
          updatedAt: new Date(),
        })
        .where(eq(bookingCompanions.id, id))
        .returning();

      // Update language associations if provided
      if (languageIds !== undefined) {
        // Remove existing language associations
        await ctx.db
          .delete(bookingCompanionLanguages)
          .where(eq(bookingCompanionLanguages.bookingCompanionId, id));

        // Add new language associations
        if (languageIds.length > 0) {
          await ctx.db.insert(bookingCompanionLanguages).values(
            languageIds.map((languageId) => ({
              bookingCompanionId: id,
              languageId,
            }))
          );
        }
      }

      // Fetch the updated companion with languages
      const companionWithLanguages = await ctx.db
        .select({
          id: bookingCompanions.id,
          appwriteUserId: bookingCompanions.appwriteUserId,
          name: bookingCompanions.name,
          lastName: bookingCompanions.lastName,
          about: bookingCompanions.about,
          gender: bookingCompanions.gender,
          typeOfTraveler: bookingCompanions.typeOfTraveler,
          genderPreference: bookingCompanions.genderPreference,
          openToAllGenders: bookingCompanions.openToAllGenders,
          flightPNR: bookingCompanions.flightPNR,
          flightTime: bookingCompanions.flightTime,
          flightEndTime: bookingCompanions.flightEndTime,
          timezone: bookingCompanions.timezone,
          searchField: bookingCompanions.searchField,
          travelersPhoto: bookingCompanions.travelersPhoto,
          passportPhoto: bookingCompanions.passportPhoto,
          compensationValue: bookingCompanions.compensationValue,
          status: bookingCompanions.status,
          createdAt: bookingCompanions.createdAt,
          updatedAt: bookingCompanions.updatedAt,
          languages: languages,
        })
        .from(bookingCompanions)
        .leftJoin(bookingCompanionLanguages, eq(bookingCompanions.id, bookingCompanionLanguages.bookingCompanionId))
        .leftJoin(languages, eq(bookingCompanionLanguages.languageId, languages.id))
        .where(eq(bookingCompanions.id, id));

      return {
        ...updatedCompanion,
        languages: companionWithLanguages
          .filter((row) => row.languages)
          .map((row) => row.languages),
      };
    }),

  // Create or update companion booking (upsert functionality)
  upsert: protectedProcedure
    .input(createCompanionSchema.extend({
      id: z.string().uuid().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, languageIds, ...companionData } = input;

      if (id) {
        // Update existing companion
        return await ctx.db.transaction(async (tx) => {
          // Verify ownership
          const existingCompanion = await tx
            .select()
            .from(bookingCompanions)
            .where(
              and(
                eq(bookingCompanions.id, id),
                eq(bookingCompanions.appwriteUserId, ctx.session.user.$id as string)
              )
            )
            .limit(1);

          if (!existingCompanion.length) {
            throw new Error("Companion booking not found or access denied");
          }

          // Update the companion booking
          const result = await tx
            .update(bookingCompanions)
            .set({
              ...companionData,
              updatedAt: new Date(),
            })
            .where(eq(bookingCompanions.id, id))
            .returning();

          const updatedCompanion = result[0];
          if (!updatedCompanion) {
            throw new Error("Failed to update companion booking");
          }

          // Update language associations
          await tx
            .delete(bookingCompanionLanguages)
            .where(eq(bookingCompanionLanguages.bookingCompanionId, id));

          if (languageIds && languageIds.length > 0) {
            await tx.insert(bookingCompanionLanguages).values(
              languageIds.map((languageId) => ({
                bookingCompanionId: id,
                languageId,
              }))
            );
          }

          return updatedCompanion;
        });
      } else {
        // Create new companion
        return await ctx.db.transaction(async (tx) => {
          const result = await tx
            .insert(bookingCompanions)
            .values({
              appwriteUserId: ctx.session.user.$id as string,
              ...companionData,
            })
            .returning();

          const newCompanion = result[0];
          if (!newCompanion) {
            throw new Error("Failed to create companion booking");
          }

          // Associate languages if provided
          if (languageIds && languageIds.length > 0) {
            await tx.insert(bookingCompanionLanguages).values(
              languageIds.map((languageId) => ({
                bookingCompanionId: newCompanion.id,
                languageId,
              }))
            );
          }

          return newCompanion;
        });
      }
    }),

  upsertFlightDetails: protectedProcedure
    .input(createFlightDetailsSchema.extend({
      id: z.string(),
      flightPNR: z.string().min(1, "Flight PNR is required"),
      flightTime: z.date(),
      flightEndTime: z.date(),
      timezone: z.string().min(1, "Timezone is required"),
      bookingDestinations: z.array(z.object({
        order: z.number(),
        airports: z.string(),
      })),
      searchField: z.string().min(1, "Search field is required"),
    }))
    .mutation(async ({ ctx, input }) => {
      console.log('upsertFlightDetails', input);
      const { id, flightPNR, flightTime, flightEndTime, timezone, bookingDestinations, searchField } = input;

      if (!id) {
        throw new Error("Companion booking ID is required");
      }

      const result = await ctx.db
        .update(bookingCompanions)
        .set({
          flightPNR,
          flightTime,
          flightEndTime,
          timezone,
          searchField,
        })
        .where(eq(bookingCompanions.id, id))
        .returning();

        // Now handle the relation: create destination records separately
        // First, remove existing destinations for this companion booking
        await ctx.db
          .delete(bookingCompanionDestinations)
          .where(eq(bookingCompanionDestinations.bookingCompanionId, id));

        // Then, insert the new destinations
        if (bookingDestinations && bookingDestinations.length > 0) {
          await ctx.db.insert(bookingCompanionDestinations).values(
            bookingDestinations.map((dest) => ({
              bookingCompanionId: id,
              order: dest.order,
              airportId: dest.airports,
            }))
          );
        }

      return result[0];
    }),

  upsertPhotoUploads: protectedProcedure
    .input(createPhotoUploadsSchema.extend({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...photoUploadsData } = input;

      if (!id) {
        throw new Error("Companion booking ID is required");
      }

      const result = await ctx.db
        .update(bookingCompanions)
        .set({
          ...photoUploadsData,
          status: 'ACTIVE',
          updatedAt: new Date(),
        })
        .where(eq(bookingCompanions.id, id))
        .returning();

      return result[0];
    }),

  // Get companion booking by ID
  getById: protectedProcedure
    .input(getCompanionSchema)
    .query(async ({ ctx, input }) => {
      const bookingCompanion = await ctx.db
        .query.bookingCompanions.findFirst({
          where: eq(bookingCompanions.id, input.id),
          with: {
            languages: {
              with: {
                language: true,
              },
            },
            destinations: {
              with: {
                airport: true,
              },
            },
            connectionRequestsAsCompanion: true,
          },
        })

      return bookingCompanion ? {
        ...bookingCompanion,
        isEditable: bookingCompanion.status === 'ACTIVE' || bookingCompanion.status === 'DRAFT',
      } : null;
    }),

  // Get all companion bookings for current user
  getAllByUser: protectedProcedure
    .query(async ({ ctx }) => {
      console.log('getAllByUser', ctx);
      const companionsWithLanguages = await ctx.db
        .select({
          id: bookingCompanions.id,
          appwriteUserId: bookingCompanions.appwriteUserId,
          name: bookingCompanions.name,
          lastName: bookingCompanions.lastName,
          about: bookingCompanions.about,
          gender: bookingCompanions.gender,
          typeOfTraveler: bookingCompanions.typeOfTraveler,
          genderPreference: bookingCompanions.genderPreference,
          openToAllGenders: bookingCompanions.openToAllGenders,
          flightPNR: bookingCompanions.flightPNR,
          flightTime: bookingCompanions.flightTime,
          flightEndTime: bookingCompanions.flightEndTime,
          timezone: bookingCompanions.timezone,
          searchField: bookingCompanions.searchField,
          travelersPhoto: bookingCompanions.travelersPhoto,
          passportPhoto: bookingCompanions.passportPhoto,
          compensationValue: bookingCompanions.compensationValue,
          status: bookingCompanions.status,
          createdAt: bookingCompanions.createdAt,
          updatedAt: bookingCompanions.updatedAt,
          // languages: languages,
        })
        .from(bookingCompanions)
        // .leftJoin(bookingLanguages, eq(bookingCompanions.id, bookingLanguages.bookingCompanionId))
        // .leftJoin(languages, eq(bookingLanguages.languageId, languages.id))
        .where(
          and(
            eq(bookingCompanions.appwriteUserId, ctx.session.user.$id as string),
            inArray(bookingCompanions.status, ["ACTIVE", "DRAFT", "CONFIRMED"])
          )
        )
        .orderBy(bookingCompanions.createdAt);

      // Group by companion ID and aggregate languages
      console.log('companionsWithLanguages', companionsWithLanguages);
      return companionsWithLanguages;
    }),

  // Delete companion booking
  delete: protectedProcedure
    .input(getCompanionSchema)
    .mutation(async ({ ctx, input }) => {
      // Verify ownership
      const existingCompanion = await ctx.db
        .select()
        .from(bookingCompanions)
        .where(
          and(
            eq(bookingCompanions.id, input.id),
            eq(bookingCompanions.appwriteUserId, ctx.session.user.$id as string)
          )
        )
        .limit(1);

      if (!existingCompanion.length) {
        throw new Error("Companion booking not found or access denied");
      }

      // Delete the companion booking (cascade will handle related records)
      await ctx.db
        .delete(bookingCompanions)
        .where(eq(bookingCompanions.id, input.id));

      return { success: true };
    }),


  // Companion searches for matching travelers based on their booking criteria
  companionsAvailableForBooking: protectedProcedure
    .input(z.object({
      bookingCompanionId: z.string(),
    }))
    .query(async ({ ctx, input }) => {
      const { bookingCompanionId } = input;

      // First, get the companion's booking details
      const bookingCompanion = await ctx.db
        .select({
          id: bookingCompanions.id,
          appwriteUserId: bookingCompanions.appwriteUserId,
          name: bookingCompanions.name,
          lastName: bookingCompanions.lastName,
          about: bookingCompanions.about,
          gender: bookingCompanions.gender,
          genderPreference: bookingCompanions.genderPreference,
          openToAllGenders: bookingCompanions.openToAllGenders,
          flightPNR: bookingCompanions.flightPNR,
          flightTime: bookingCompanions.flightTime,
          flightEndTime: bookingCompanions.flightEndTime,
          timezone: bookingCompanions.timezone,
          searchField: bookingCompanions.searchField,
          travelersPhoto: bookingCompanions.travelersPhoto,
          passportPhoto: bookingCompanions.passportPhoto,
          compensationValue: bookingCompanions.compensationValue,
          status: bookingCompanions.status,
        })
        .from(bookingCompanions)
        .where(and(
          eq(bookingCompanions.id, bookingCompanionId),
          eq(bookingCompanions.appwriteUserId, ctx.session.user.$id as string)
        ))
        .limit(1);

      console.log('bookingCompanion found', bookingCompanion);

      if (!bookingCompanion.length || !bookingCompanion[0]) {
        throw new Error("Companion booking not found");
      }

      const bookingCompanionData = bookingCompanion[0];
      console.log('bookingCompanionData', bookingCompanionData);

      if (bookingCompanionData.status === 'CONFIRMED') {
        const connectionRequestsAsCompanion = await ctx.db.query.connectionRequests.findFirst({
          with: {
            bookingTraveler: true,
          },
          where: and(
            eq(connectionRequests.bookingCompanionId, bookingCompanionData.id),
            eq(connectionRequests.status, "ACCEPTED")
          )
        })

        if (!connectionRequestsAsCompanion?.bookingTravelerId) {
          return {
            companions: [],
            total: 0,
          };
        }
        const baseResults = await ctx.db.query.bookingTravelers.findMany({
          with: {
            languages: {
              with: {
                language: true,
              },
            },
            destinations: {
              with: {
                airport: true,
              },
            },
            connectionRequestsAsTraveler: {
              where: eq(connectionRequests.status, "ACCEPTED"),
            },
          },
          where: and(
            eq(bookingTravelers.id, connectionRequestsAsCompanion.bookingTravelerId),
            ne(bookingTravelers.appwriteUserId, ctx.session.user.$id as string),
            eq(bookingTravelers.status, "CONFIRMED")
          ),
          orderBy: desc(bookingTravelers.createdAt)
        })

        return {
          companions: baseResults,
          total: baseResults.length,
        };
      }

      if (bookingCompanionData.status !== 'ACTIVE') {
        return {
          companions: [],
          total: 0,
        };
      }

      // // Get companion's languages
      // const languages = await ctx.db
      //   .select({
      //     languageId: bookingTravelerLanguages.languageId,
      //   })
      //   .from(bookingCompanionLanguages)
      //   .where(eq(bookingCompanionLanguages.bookingCompanionId, bookingTravelerId));

      // const companionLanguageIds = languages.map(l => l.languageId);

      

      // // Build matching conditions
      // const conditions = [];

      // // Exclude current user's traveler bookings
      // conditions.push(ne(bookingCompanions.appwriteUserId, ctx.session.user.$id as string));

      // // Gender matching: companion's gender should match traveler's gender preference
      // conditions.push(
      //   or(
      //     eq(bookingCompanions.genderPreference, bookingTravelerData.gender),
      //     eq(bookingCompanions.openToAllGenders, true)
      //   )
      // );

      // // Gender preference matching: companion's gender preference should match traveler's gender
      // conditions.push(
      //   or(
      //     eq(bookingCompanions.genderPreference, bookingTravelers.gender),
      //     eq(bookingCompanions.openToAllGenders, true)
      //   )
      // );

      // // Flight time matching (within ±2 hours)
      // if (bookingCompanions.searchField) {
        
      //   conditions.push(
      //     eq(bookingCompanions.searchField, bookingCompanions.searchField)
      //   );
      // }

      // // Compensation matching (companion's compensation should be within traveler's range)
      // if (bookingCompanions.compensationValue) {
      //   // Assuming traveler's compensation is the maximum they're willing to pay
      //   // and companion's compensation is what they're asking for
      //   conditions.push(gte(bookingCompanions.compensationValue, bookingCompanions.compensationValue));
      // }

      // // Only active traveler bookings
      // conditions.push(eq(bookingCompanions.status, "ACTIVE"));

      // Apply conditions
      // Build the base query for matching travelers
      const baseResults = await ctx.db.query.bookingTravelers.findMany({
          with: {
            languages: {
              with: {
                language: true,
              },
            },
            destinations: {
              with: {
                airport: true,
              },
            },
            connectionRequestsAsTraveler: true,
          },
          where: and(
            eq(bookingTravelers.searchField, bookingCompanionData.searchField || ''),
            ne(bookingTravelers.appwriteUserId, ctx.session.user.$id as string),
            eq(bookingTravelers.status, "ACTIVE")
          ),
          orderBy: desc(bookingTravelers.createdAt)
        })
        // .where(and(...conditions));

      // Filter by matching destinations if companion has destinations
      let filteredResults = baseResults;

      // // Filter by matching languages if companion has languages
      // if (companionLanguageIds.length > 0) {
      //   const travelersWithMatchingLanguages = await ctx.db
      //     .select({
      //       bookingCompanionId: bookingCompanionLanguages.bookingCompanionId,
      //     })
      //     .from(bookingCompanionLanguages)
      //     .where(inArray(bookingCompanionLanguages.languageId, companionLanguageIds))
      //     .groupBy(bookingCompanionLanguages.bookingCompanionId);

      //   const matchingTravelerIds = travelersWithMatchingLanguages.map(t => t.bookingCompanionId);
      //   filteredResults = filteredResults.filter(companion => 
      //     matchingTravelerIds.includes(companion.id)
      //   );
      // }

      // Get detailed information for matching travelers
      // const detailedResults = await Promise.all(
      //   filteredResults.map(async (traveler) => {
      //     // Get traveler's languages
      //     const travelerLanguages = await ctx.db
      //       .select({
      //         language: languages,
      //       })
      //       .from(bookingTravelerLanguages)
      //       .leftJoin(languages, eq(bookingTravelerLanguages.languageId, languages.id))
      //       .where(eq(bookingTravelerLanguages.bookingTravelerId, traveler.id));

      //     // Get traveler's destinations
      //     const travelerDestinations = await ctx.db
      //       .select({
      //         airport: airports,
      //         order: bookingTravelerDestinations.order,
      //       })
      //       .from(bookingTravelerDestinations)
      //       .leftJoin(airports, eq(bookingTravelerDestinations.airportId, airports.id))
      //       .where(eq(bookingTravelerDestinations.bookingTravelerId, traveler.id))
      //       .orderBy(bookingTravelerDestinations.order);

      //     // Calculate match score based on various factors
      //     let matchScore = 0;
          
      //     // Gender match (20 points)
      //     if (traveler.genderPreference === companionData.gender || traveler.openToAllGenders) {
      //       matchScore += 20;
      //     }
      //     if (companionData.genderPreference === traveler.gender || companionData.openToAllGenders) {
      //       matchScore += 20;
      //     }

      //     // Flight time match (30 points)
      //     if (companionData.flightTime && traveler.flightTime) {
      //       const timeDiff = Math.abs(companionData.flightTime.getTime() - traveler.flightTime.getTime());
      //       const hoursDiff = timeDiff / (1000 * 60 * 60);
      //       if (hoursDiff <= 1) matchScore += 30;
      //       else if (hoursDiff <= 2) matchScore += 20;
      //       else if (hoursDiff <= 4) matchScore += 10;
      //     }

      //     // Destination match (25 points)
      //     const travelerAirportIds = travelerDestinations.map(d => d.airport?.id).filter(Boolean);
      //     const destinationMatches = companionAirportIds.filter(id => 
      //       travelerAirportIds.includes(id)
      //     ).length;
      //     if (destinationMatches > 0) {
      //       matchScore += Math.min(25, (destinationMatches / companionAirportIds.length) * 25);
      //     }

      //     // Language match (15 points)
      //     const travelerLanguageIds = travelerLanguages.map(l => l.language?.id).filter(Boolean);
      //     const languageMatches = companionLanguageIds.filter(id => 
      //       travelerLanguageIds.includes(id)
      //     ).length;
      //     if (languageMatches > 0) {
      //       matchScore += Math.min(15, (languageMatches / companionLanguageIds.length) * 15);
      //     }

      //     // Compensation match (10 points)
      //     if (companionData.compensationValue && traveler.compensationValue) {
      //       if (traveler.compensationValue >= companionData.compensationValue) {
      //         matchScore += 10;
      //       }
      //     }

      //     return {
      //       ...traveler,
      //       languages: travelerLanguages.map(tl => tl.language).filter(Boolean),
      //       destinations: travelerDestinations.map(td => ({
      //         airport: td.airport,
      //         order: td.order,
      //       })).filter(td => td.airport),
      //       matchScore: Math.round(matchScore),
      //       matchPercentage: Math.round((matchScore / 100) * 100),
      //     };
      //   })
      // );

      // // Sort by match score (highest first)
      // detailedResults.sort((a, b) => b.matchScore - a.matchScore);

      return {
        companions: baseResults, // filteredResults,
        total: baseResults.length,
      };
    }),
}); 