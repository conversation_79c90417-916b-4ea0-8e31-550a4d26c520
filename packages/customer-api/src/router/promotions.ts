import { z } from "zod";
import { createTRPCRouter, protectedProcedure, publicProcedure } from "../trpc";
import { 
  promotions, 
  promotionAnalytics, 
  promotionRedemptions 
} from "@repo/db";
import { eq, and, gte, lte, asc, desc, sql } from "@repo/db";

export const promotionRouter = createTRPCRouter({
  // Get active promotions for mobile app
  getActivePromotions: protectedProcedure
    .input(z.object({
      userType: z.enum(["TRAVELER", "COMPANION"]).optional(),
      limit: z.number().min(1).max(20).default(10),
      offset: z.number().min(0).default(0),
    }).optional())
    .query(async ({ ctx, input = {} }) => {
      const { userType, limit = 10, offset = 0 } = input;
      const now = new Date();
      
      let whereConditions = [
        eq(promotions.isActive, true),
        lte(promotions.startDate, now),
        gte(promotions.endDate, now),
      ];

      // Filter by user type if specified
      if (userType) {
        whereConditions.push(
          sql`(${promotions.targetUserType} = 'ALL' OR ${promotions.targetUserType} = ${userType === "TRAVELER" ? "'TRAVELERS'" : "'COMPANIONS'"})`
        );
      } else {
        whereConditions.push(
          sql`${promotions.targetUserType} IN ('ALL', 'TRAVELERS', 'COMPANIONS')`
        );
      }

      const activePromotions = await ctx.db
        .select({
          id: promotions.id,
          title: promotions.title,
          description: promotions.description,
          imageUrl: promotions.imageUrl,
          promotionType: promotions.promotionType,
          discountPercentage: promotions.discountPercentage,
          discountAmount: promotions.discountAmount,
          promoCode: promotions.promoCode,
          minBookingAmount: promotions.minBookingAmount,
          maxDiscountAmount: promotions.maxDiscountAmount,
          usageLimit: promotions.usageLimit,
          usageCount: promotions.usageCount,
          startDate: promotions.startDate,
          endDate: promotions.endDate,
          targetUserType: promotions.targetUserType,
          termsAndConditions: promotions.termsAndConditions,
          priority: promotions.priority,
        })
        .from(promotions)
        .where(and(...whereConditions))
        .orderBy(desc(promotions.priority), desc(promotions.createdAt))
        .limit(limit)
        .offset(offset);

      return activePromotions;
    }),

  // Get promotion by ID
  getById: protectedProcedure
    .input(z.object({ 
      id: z.string(),
      trackView: z.boolean().default(true),
    }))
    .query(async ({ ctx, input }) => {
      const promotion = await ctx.db
        .select()
        .from(promotions)
        .where(eq(promotions.id, input.id))
        .limit(1);

      if (!promotion[0]) {
        throw new Error("Promotion not found");
      }

      // Track view if requested
      if (input.trackView && ctx.appwriteUserId) {
        await ctx.db.insert(promotionAnalytics).values({
          promotionId: input.id,
          appwriteUserId: ctx.appwriteUserId,
          userType: "TRAVELER", // You might want to determine this from user profile
          action: "VIEW",
          deviceInfo: {}, // You can add device info from headers
        }).catch(() => {
          // Ignore analytics errors
        });
      }

      return promotion[0];
    }),

  // Track promotion click
  trackClick: protectedProcedure
    .input(z.object({ 
      promotionId: z.string(),
      deviceInfo: z.object({
        platform: z.string().optional(),
        deviceType: z.string().optional(),
        userAgent: z.string().optional(),
      }).optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      // Verify promotion exists and is active
      const promotion = await ctx.db
        .select()
        .from(promotions)
        .where(eq(promotions.id, input.promotionId))
        .limit(1);

      if (!promotion[0]) {
        throw new Error("Promotion not found");
      }

      // Track the click
      await ctx.db.insert(promotionAnalytics).values({
        promotionId: input.promotionId,
        appwriteUserId: ctx.appwriteUserId,
        userType: "TRAVELER", // You might want to determine this from user profile
        action: "CLICK",
        deviceInfo: input.deviceInfo || {},
      });

      return { success: true };
    }),

  // Validate and apply promo code
  validatePromoCode: protectedProcedure
    .input(z.object({
      promoCode: z.string(),
      bookingAmount: z.number().min(0), // in cents
      userType: z.enum(["TRAVELER", "COMPANION"]).optional(),
    }))
    .query(async ({ ctx, input }) => {
      const now = new Date();
      
      // Find promotion by promo code
      const promotion = await ctx.db
        .select()
        .from(promotions)
        .where(
          and(
            eq(promotions.promoCode, input.promoCode),
            eq(promotions.isActive, true),
            lte(promotions.startDate, now),
            gte(promotions.endDate, now)
          )
        )
        .limit(1);

      if (!promotion[0]) {
        return {
          valid: false,
          error: "Invalid or expired promo code",
        };
      }

      const promo = promotion[0];

      // Check usage limit
      if (promo.usageLimit && promo.usageCount >= promo.usageLimit) {
        return {
          valid: false,
          error: "Promo code usage limit exceeded",
        };
      }

      // Check minimum booking amount
      if (promo.minBookingAmount && input.bookingAmount < promo.minBookingAmount) {
        return {
          valid: false,
          error: `Minimum booking amount is $${(promo.minBookingAmount / 100).toFixed(2)}`,
        };
      }

      // Check target user type
      if (promo.targetUserType !== "ALL" && input.userType) {
        const targetMatches = 
          (promo.targetUserType === "TRAVELERS" && input.userType === "TRAVELER") ||
          (promo.targetUserType === "COMPANIONS" && input.userType === "COMPANION");
        
        if (!targetMatches) {
          return {
            valid: false,
            error: "This promo code is not applicable for your user type",
          };
        }
      }

      // Calculate discount
      let discountAmount = 0;
      if (promo.discountPercentage) {
        discountAmount = Math.floor((input.bookingAmount * promo.discountPercentage) / 100);
      } else if (promo.discountAmount) {
        discountAmount = promo.discountAmount;
      }

      // Apply max discount cap
      if (promo.maxDiscountAmount && discountAmount > promo.maxDiscountAmount) {
        discountAmount = promo.maxDiscountAmount;
      }

      const finalAmount = input.bookingAmount - discountAmount;

      return {
        valid: true,
        promotion: {
          id: promo.id,
          title: promo.title,
          promotionType: promo.promotionType,
          discountPercentage: promo.discountPercentage,
          discountAmount: promo.discountAmount,
        },
        calculation: {
          originalAmount: input.bookingAmount,
          discountAmount,
          finalAmount,
          savings: discountAmount,
        },
      };
    }),

  // Apply promotion (when booking is confirmed)
  applyPromotion: protectedProcedure
    .input(z.object({
      promotionId: z.string(),
      bookingId: z.string(),
      bookingType: z.enum(["TRAVELER", "COMPANION"]),
      originalAmount: z.number().min(0), // in cents
      discountAmount: z.number().min(0), // in cents
      finalAmount: z.number().min(0), // in cents
      promoCode: z.string().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      // Verify promotion is still valid
      const promotion = await ctx.db
        .select()
        .from(promotions)
        .where(eq(promotions.id, input.promotionId))
        .limit(1);

      if (!promotion[0] || !promotion[0].isActive) {
        throw new Error("Promotion is no longer valid");
      }

      // Check if user already used this promotion (optional business rule)
      const existingRedemption = await ctx.db
        .select()
        .from(promotionRedemptions)
        .where(
          and(
            eq(promotionRedemptions.promotionId, input.promotionId),
            eq(promotionRedemptions.appwriteUserId, ctx.appwriteUserId),
            eq(promotionRedemptions.status, "APPLIED")
          )
        )
        .limit(1);

      // For single-use promotions, prevent duplicate usage
      if (existingRedemption[0]) {
        throw new Error("You have already used this promotion");
      }

      // Record the redemption
      const redemption = await ctx.db.insert(promotionRedemptions).values({
        promotionId: input.promotionId,
        appwriteUserId: ctx.appwriteUserId,
        bookingId: input.bookingId,
        bookingType: input.bookingType,
        originalAmount: input.originalAmount,
        discountAmount: input.discountAmount,
        finalAmount: input.finalAmount,
        promoCode: input.promoCode,
        status: "APPLIED",
      }).returning();

      // Update promotion usage count
      await ctx.db
        .update(promotions)
        .set({
          usageCount: sql`${promotions.usageCount} + 1`,
          updatedAt: new Date(),
        })
        .where(eq(promotions.id, input.promotionId));

      // Track redemption analytics
      await ctx.db.insert(promotionAnalytics).values({
        promotionId: input.promotionId,
        appwriteUserId: ctx.appwriteUserId,
        userType: input.bookingType,
        action: "REDEEM",
        deviceInfo: {},
      }).catch(() => {
        // Ignore analytics errors
      });

      return redemption[0];
    }),

  // Get user's promotion redemptions
  getUserRedemptions: protectedProcedure
    .input(z.object({
      limit: z.number().min(1).max(50).default(20),
      offset: z.number().min(0).default(0),
    }).optional())
    .query(async ({ ctx, input = {} }) => {
      const { limit = 20, offset = 0 } = input;

      const redemptions = await ctx.db
        .select({
          redemption: promotionRedemptions,
          promotion: {
            id: promotions.id,
            title: promotions.title,
            promotionType: promotions.promotionType,
            imageUrl: promotions.imageUrl,
          },
        })
        .from(promotionRedemptions)
        .innerJoin(promotions, eq(promotionRedemptions.promotionId, promotions.id))
        .where(eq(promotionRedemptions.appwriteUserId, ctx.appwriteUserId))
        .orderBy(desc(promotionRedemptions.createdAt))
        .limit(limit)
        .offset(offset);

      return redemptions.map(row => ({
        ...row.redemption,
        promotion: row.promotion,
      }));
    }),
});
