import { z } from "zod";
import { eq, and, or, desc } from "drizzle-orm";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { 
  connectionRequests,
  bookingTravelers,
  bookingCompanions,
  bookingTravelerLanguages,
  bookingCompanionLanguages,
  languages,
  airports,
  bookingTravelerDestinations,
  bookingCompanionDestinations,
  stripeTransfers,
  stripeConnectAccounts,
  walletTransactions,
  userProfiles
  
} from "@repo/db";
import type { DrizzleTransaction } from "@repo/db/client";
import { streamChatService } from "../services/stream-chat.service";
import Stripe from "stripe";
import { TRPCError } from "@trpc/server";
import { addDays } from "date-fns";

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2025-06-30.basil",
});

const isCancelableWithoutFeesStatus = (status: string, date: Date) => {
  return status === 'PENDING' || status === 'PENDING_NEGOTIATION' || (status === 'ACCEPTED' && date > addDays(new Date(), 3));
}

// Helper function to update user wallet balance within a transaction
async function updateUserWalletBalanceInTransaction(tx: DrizzleTransaction, appwriteUserId: string, amount: number, type: 'credit' | 'debit') {
  
  // Get current user profile
  let userProfile = await tx
    .select({ walletBalance: userProfiles.walletBalance })
    .from(userProfiles)
    .where(eq(userProfiles.appwriteUserId, appwriteUserId))
    .limit(1);

  if (userProfile.length === 0) {
    // If user profile does not exist, create it with the initial wallet balance
    await tx
      .insert(userProfiles)
      .values({
        name: "",
        appwriteUserId,
        walletBalance: type === 'credit' ? amount : -amount,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    return type === 'credit' ? amount : -amount;
  }

  const currentBalance = userProfile[0]?.walletBalance || 0;
  const newBalance = type === 'credit' ? currentBalance + amount : currentBalance - amount;

  // Update user profile wallet balance
  await tx
    .update(userProfiles)
    .set({
      walletBalance: newBalance,
      updatedAt: new Date(),
    })
    .where(eq(userProfiles.appwriteUserId, appwriteUserId));

  return newBalance;
}

// Input schemas for validation
const createConnectionRequestSchema = z.object({
  bookingTravelerId: z.string().uuid("Invalid traveler ID"),
  bookingCompanionId: z.string().uuid("Invalid companion ID"),
  initiator: z.enum(["TRAVELER", "COMPANION"]).default("TRAVELER"),
});

const updateConnectionRequestStatusSchema = z.object({
  id: z.string().uuid("Invalid connection request ID"),
  status: z.enum(["ACCEPTED", "REJECTED"]),
});

const getConnectionRequestByCompanionAndTravelerSchema = z.object({
  bookingCompanionId: z.string().uuid("Invalid companion ID"),
  bookingTravelerId: z.string().uuid("Invalid traveler ID"),
});

const getConnectionRequestsByUserSchema = z.object({
  status: z.enum(["PENDING", "ACCEPTED", "REJECTED", "CANCELLED_BY_TRAVELER", "CANCELLED_BY_COMPANION"]).default("PENDING"),
  initiator: z.enum(["TRAVELER", "COMPANION"]).default("TRAVELER"),
});

const deleteConnectionRequestSchema = z.object({
  id: z.string().uuid("Invalid connection request ID"),
});

const releasePaymentSchema = z.object({
  connectionRequestId: z.string().uuid("Invalid connection request ID"),
  amount: z.number().positive("Amount must be positive"), // in cents
  currency: z.string().default("usd"),
  description: z.string().optional(),
});

const cancelConnectionRequestSchema = z.object({
  id: z.string().uuid("Invalid connection request ID"),
  reason: z.string().optional(),
});

const proposePriceSchema = z.object({
  connectionRequestId: z.string().uuid("Invalid connection request ID"),
  proposedPrice: z.number().positive("Proposed price must be positive"),
  message: z.string().optional(),
});

const acceptNegotiatedPriceSchema = z.object({
  connectionRequestId: z.string().uuid("Invalid connection request ID"),
  confirmPrice: z.number().positive("Confirmed price must be positive"),
});

export const connectionRequestsRouter = createTRPCRouter({
  // Create a new connection request
  create: protectedProcedure
    .input(createConnectionRequestSchema)
    .mutation(async ({ ctx, input }) => {
      const { bookingTravelerId, bookingCompanionId } = input;

      // Verify that the current user owns the traveler booking
      const travelerBooking = await ctx.db
        .select()
        .from(bookingTravelers)
        .where(
          and(
            eq(bookingTravelers.id, bookingTravelerId),
            eq(bookingTravelers.status, "ACTIVE")
            // eq(bookingTravelers.appwriteUserId, ctx.session.user.$id as string)
          )
        )
        .limit(1);

      if (!travelerBooking.length) {
        throw new Error("Traveler booking not found or access denied");
      }

      // Verify that the companion booking exists and is active
      const companionBooking = await ctx.db
        .select()
        .from(bookingCompanions)
        .where(
          and(
            eq(bookingCompanions.id, bookingCompanionId),
            eq(bookingCompanions.status, "ACTIVE")
          )
        )
        .limit(1);

      if (!companionBooking.length) {
        throw new Error("Companion booking not found or not active");
      }

      // Check if a connection request already exists
      const existingRequest = await ctx.db
        .select()
        .from(connectionRequests)
        .where(
          and(
            eq(connectionRequests.bookingTravelerId, bookingTravelerId),
          )
        )
        .limit(1);

      if (existingRequest.length > 0) {
        throw new Error("Connection request already exists");
      }

      if(!travelerBooking[0] || !companionBooking[0]) {
        throw new Error("Traveler or companion booking not found");
      }

      // NEW: Add price mismatch validation
      const travelerCompensation = travelerBooking[0].compensationValue;
      const companionCompensation = companionBooking[0].compensationValue;
      
      if (travelerCompensation && companionCompensation) {
        const priceDifference = Math.abs(travelerCompensation - companionCompensation);
        const tolerance = 100; // $1 tolerance in cents
        
        if (priceDifference > tolerance) {
          // Create request with PENDING_NEGOTIATION status
          const [newRequest] = await ctx.db
            .insert(connectionRequests)
            .values({
              bookingTravelerId,
              bookingCompanionId,
              initiator: input.initiator,
              status: "PENDING_NEGOTIATION",
              originalTravelerPrice: travelerCompensation,
              originalCompanionPrice: companionCompensation,
              proposedPrice: Math.min(travelerCompensation, companionCompensation), // Start with lower price
              priceNegotiationHistory: [{
                userId: ctx.session.user.$id as string,
                proposedPrice: Math.min(travelerCompensation, companionCompensation),
                timestamp: new Date().toISOString(),
                message: "Initial proposal based on price mismatch"
              }]
            })
            .returning();

          return {
            ...newRequest,
            requiresNegotiation: true,
            priceDifference: priceDifference / 100,
            originalTravelerPrice: travelerCompensation / 100,
            originalCompanionPrice: companionCompensation / 100,
          };
        }
      }

      // Create normal request if prices match
      const [newRequest] = await ctx.db
        .insert(connectionRequests)
        .values({
          bookingTravelerId,
          bookingCompanionId,
          initiator: input.initiator,
          status: "PENDING",
          originalTravelerPrice: travelerCompensation,
          originalCompanionPrice: companionCompensation,
          proposedPrice: travelerCompensation, // Use traveler's price as default
        })
        .returning();

      return {
        ...newRequest,
        requiresNegotiation: false,
        priceDifference: 0,
        originalTravelerPrice: travelerCompensation ? travelerCompensation / 100 : 0,
        originalCompanionPrice: companionCompensation ? companionCompensation / 100 : 0,
      };
    }),

  // Get connection request by ID
  getByCompanionAndTraveler: protectedProcedure
    .input(getConnectionRequestByCompanionAndTravelerSchema)
    .query(async ({ ctx, input }) => {
      const request = await ctx.db
        .select({
          id: connectionRequests.id,
          status: connectionRequests.status,
          initiator: connectionRequests.initiator,
          originalTravelerPrice: connectionRequests.originalTravelerPrice,
          originalCompanionPrice: connectionRequests.originalCompanionPrice,
          proposedPrice: connectionRequests.proposedPrice,
          priceNegotiationHistory: connectionRequests.priceNegotiationHistory,
          createdAt: connectionRequests.createdAt,
          updatedAt: connectionRequests.updatedAt,
          bookingTraveler: {
            id: bookingTravelers.id,
            name: bookingTravelers.name,
            lastName: bookingTravelers.lastName,
            about: bookingTravelers.about,
            gender: bookingTravelers.gender,
            genderPreference: bookingTravelers.genderPreference,
            openToAllGenders: bookingTravelers.openToAllGenders,
            flightPNR: bookingTravelers.flightPNR,
            flightTime: bookingTravelers.flightTime,
            flightEndTime: bookingTravelers.flightEndTime,
            timezone: bookingTravelers.timezone,
            searchField: bookingTravelers.searchField,
            companionPhoto: bookingTravelers.companionPhoto,
            compensationValue: bookingTravelers.compensationValue,
            status: bookingTravelers.status,
            appwriteUserId: bookingTravelers.appwriteUserId,
          },
          bookingCompanion: {
            id: bookingCompanions.id,
            name: bookingCompanions.name,
            lastName: bookingCompanions.lastName,
            about: bookingCompanions.about,
            gender: bookingCompanions.gender,
            typeOfTraveler: bookingCompanions.typeOfTraveler,
            genderPreference: bookingCompanions.genderPreference,
            openToAllGenders: bookingCompanions.openToAllGenders,
            flightPNR: bookingCompanions.flightPNR,
            flightTime: bookingCompanions.flightTime,
            flightEndTime: bookingCompanions.flightEndTime,
            timezone: bookingCompanions.timezone,
            searchField: bookingCompanions.searchField,
            travelersPhoto: bookingCompanions.travelersPhoto,
            compensationValue: bookingCompanions.compensationValue,
            status: bookingCompanions.status,
            appwriteUserId: bookingCompanions.appwriteUserId,
          },
        })
        .from(connectionRequests)
        .leftJoin(bookingTravelers, eq(connectionRequests.bookingTravelerId, bookingTravelers.id))
        .leftJoin(bookingCompanions, eq(connectionRequests.bookingCompanionId, bookingCompanions.id))
        .where(
            and(
                eq(connectionRequests.bookingCompanionId, input.bookingCompanionId), 
                eq(connectionRequests.bookingTravelerId, input.bookingTravelerId),
                or(
                    eq(bookingTravelers.appwriteUserId, ctx.session.user.$id as string),
                    eq(bookingCompanions.appwriteUserId, ctx.session.user.$id as string)
                )
            )
        )
        .limit(1);

      return request[0] ? {
        ...request[0],
        lastPriceUpdatedByUser: request[0].priceNegotiationHistory ? request[0].priceNegotiationHistory[request[0].priceNegotiationHistory.length - 1]?.userId === ctx.session.user.$id as string : null,
      } : null;
    }),

  // Get connection request by ID
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ ctx, input }) => {
      const request = await ctx.db
        .select({
          id: connectionRequests.id,
          status: connectionRequests.status,
          initiator: connectionRequests.initiator,
          createdAt: connectionRequests.createdAt,
          updatedAt: connectionRequests.updatedAt,
          bookingTraveler: {
            id: bookingTravelers.id,
            name: bookingTravelers.name,
            lastName: bookingTravelers.lastName,
            about: bookingTravelers.about,
            gender: bookingTravelers.gender,
            genderPreference: bookingTravelers.genderPreference,
            openToAllGenders: bookingTravelers.openToAllGenders,
            flightPNR: bookingTravelers.flightPNR,
            flightTime: bookingTravelers.flightTime,
            flightEndTime: bookingTravelers.flightEndTime,
            timezone: bookingTravelers.timezone,
            searchField: bookingTravelers.searchField,
            companionPhoto: bookingTravelers.companionPhoto,
            compensationValue: bookingTravelers.compensationValue,
            status: bookingTravelers.status,
            appwriteUserId: bookingTravelers.appwriteUserId,
          },
          bookingCompanion: {
            id: bookingCompanions.id,
            name: bookingCompanions.name,
            lastName: bookingCompanions.lastName,
            about: bookingCompanions.about,
            gender: bookingCompanions.gender,
            typeOfTraveler: bookingCompanions.typeOfTraveler,
            genderPreference: bookingCompanions.genderPreference,
            openToAllGenders: bookingCompanions.openToAllGenders,
            flightPNR: bookingCompanions.flightPNR,
            flightTime: bookingCompanions.flightTime,
            flightEndTime: bookingCompanions.flightEndTime,
            timezone: bookingCompanions.timezone,
            searchField: bookingCompanions.searchField,
            travelersPhoto: bookingCompanions.travelersPhoto,
            compensationValue: bookingCompanions.compensationValue,
            status: bookingCompanions.status,
            appwriteUserId: bookingCompanions.appwriteUserId,
          },
        })
        .from(connectionRequests)
        .leftJoin(bookingTravelers, eq(connectionRequests.bookingTravelerId, bookingTravelers.id))
        .leftJoin(bookingCompanions, eq(connectionRequests.bookingCompanionId, bookingCompanions.id))
        .where(eq(connectionRequests.id, input.id))
        .limit(1);

      return request[0] ? {
        ...request[0],
        userId: ctx.session.user.$id as string,
      } : null;
    }),

  // Get connection requests for the current user
  getByUser: protectedProcedure
    .input(getConnectionRequestsByUserSchema)
    .query(async ({ ctx, input }) => {
      const { status, initiator } = input;
      const userId = ctx.session.user.$id as string;

      // First, get the basic connection requests
      let baseQuery = ctx.db
        .select({
          id: connectionRequests.id,
          status: connectionRequests.status,
          createdAt: connectionRequests.createdAt,
          updatedAt: connectionRequests.updatedAt,
          bookingTraveler: {
            id: bookingTravelers.id,
            name: bookingTravelers.name,
            // lastName: bookingTravelers.lastName,
            about: bookingTravelers.about,
            gender: bookingTravelers.gender,
            genderPreference: bookingTravelers.genderPreference,
            openToAllGenders: bookingTravelers.openToAllGenders,
            flightPNR: bookingTravelers.flightPNR,
            flightTime: bookingTravelers.flightTime,
            flightEndTime: bookingTravelers.flightEndTime,
            timezone: bookingTravelers.timezone,
            searchField: bookingTravelers.searchField,
            companionPhoto: bookingTravelers.companionPhoto,
            compensationValue: bookingTravelers.compensationValue,
            status: bookingTravelers.status,
            typeOfTraveler: bookingTravelers.typeOfTraveler,
            appwriteUserId: bookingTravelers.appwriteUserId,
          },
          bookingCompanion: {
            id: bookingCompanions.id,
            name: bookingCompanions.name,
            // lastName: bookingCompanions.lastName,
            about: bookingCompanions.about,
            gender: bookingCompanions.gender,
            typeOfTraveler: bookingCompanions.typeOfTraveler,
            genderPreference: bookingCompanions.genderPreference,
            openToAllGenders: bookingCompanions.openToAllGenders,
            flightPNR: bookingCompanions.flightPNR,
            flightTime: bookingCompanions.flightTime,
            flightEndTime: bookingCompanions.flightEndTime,
            timezone: bookingCompanions.timezone,
            searchField: bookingCompanions.searchField,
            travelersPhoto: bookingCompanions.travelersPhoto,
            compensationValue: bookingCompanions.compensationValue,
            status: bookingCompanions.status,
            appwriteUserId: bookingCompanions.appwriteUserId,
          },
        })
        .from(connectionRequests)
        .leftJoin(bookingTravelers, eq(connectionRequests.bookingTravelerId, bookingTravelers.id))
        .leftJoin(bookingCompanions, eq(connectionRequests.bookingCompanionId, bookingCompanions.id))
        .where(
          status === "ACCEPTED" ?
            and(
                eq(connectionRequests.status, "ACCEPTED"),
                or(
                  eq(bookingTravelers.appwriteUserId, userId),
                  eq(bookingCompanions.appwriteUserId, userId)
                )
            ) 
          :
            and(
                // initiator ? eq(connectionRequests.initiator, initiator) : undefined,
                eq(connectionRequests.status, status),
                or(
                  eq(bookingTravelers.appwriteUserId, userId),
                  eq(bookingCompanions.appwriteUserId, userId)
                )
            )
        )
        .orderBy(desc(connectionRequests.createdAt));

      const requests = await baseQuery;

      // Now enrich each request with destinations and languages
      const enrichedRequests = await Promise.all(
        requests.map(async (request) => {
          // Get traveler destinations
          const travelerDestinations = await ctx.db
            .select({
              id: bookingTravelerDestinations.id,
              order: bookingTravelerDestinations.order,
              airport: {
                id: airports.id,
                name: airports.name,
                shortCode: airports.shortCode,
                city: airports.city,
                country: airports.country,
              },
            })
            .from(bookingTravelerDestinations)
            .leftJoin(airports, eq(bookingTravelerDestinations.airportId, airports.id))
            .where(eq(bookingTravelerDestinations.bookingTravelerId, request.bookingTraveler!.id))
            .orderBy(bookingTravelerDestinations.order);

          // Get traveler languages
          const travelerLanguages = await ctx.db
            .select({
              id: bookingTravelerLanguages.id,
              languageId: bookingTravelerLanguages.languageId,
              language: {
                id: languages.id,
                name: languages.name,
              },
            })
            .from(bookingTravelerLanguages)
            .leftJoin(languages, eq(bookingTravelerLanguages.languageId, languages.id))
            .where(eq(bookingTravelerLanguages.bookingTravelerId, request.bookingTraveler!.id));

          // Get companion destinations
          const companionDestinations = await ctx.db
            .select({
              id: bookingCompanionDestinations.id,
              order: bookingCompanionDestinations.order,
              airport: {
                id: airports.id,
                name: airports.name,
                shortCode: airports.shortCode,
                city: airports.city,
                country: airports.country,
              },
            })
            .from(bookingCompanionDestinations)
            .leftJoin(airports, eq(bookingCompanionDestinations.airportId, airports.id))
            .where(eq(bookingCompanionDestinations.bookingCompanionId, request.bookingCompanion!.id))
            .orderBy(bookingCompanionDestinations.order);

          // Get companion languages
          const companionLanguages = await ctx.db
            .select({
              id: bookingCompanionLanguages.id,
              languageId: bookingCompanionLanguages.languageId,
              language: {
                id: languages.id,
                name: languages.name,
              },
            })
            .from(bookingCompanionLanguages)
            .leftJoin(languages, eq(bookingCompanionLanguages.languageId, languages.id))
            .where(eq(bookingCompanionLanguages.bookingCompanionId, request.bookingCompanion!.id));

          return {
            ...request,
            bookingTraveler: {
              ...request.bookingTraveler,
              destinations: travelerDestinations,
              languages: travelerLanguages,
            },
            bookingCompanion: {
              ...request.bookingCompanion,
              destinations: companionDestinations,
              languages: companionLanguages,
            },
          };
        })
      );

      console.log('enriched requests by user', enrichedRequests);

      return {
        requests: enrichedRequests,
        userId: ctx.session.user.$id as string,
      };
    }),

  // Update connection request status (accept/reject)
  updateStatus: protectedProcedure
    .input(updateConnectionRequestStatusSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, status } = input;

      // Get the connection request with related data
      const request = await ctx.db
        .select({
          id: connectionRequests.id,
          status: connectionRequests.status,
          bookingTraveler: {
            id: bookingTravelers.id,
            appwriteUserId: bookingTravelers.appwriteUserId,
          },
          bookingCompanion: {
            id: bookingCompanions.id,
            appwriteUserId: bookingCompanions.appwriteUserId,
          },
        })
        .from(connectionRequests)
        .leftJoin(bookingTravelers, eq(connectionRequests.bookingTravelerId, bookingTravelers.id))
        .leftJoin(bookingCompanions, eq(connectionRequests.bookingCompanionId, bookingCompanions.id))
        .where(eq(connectionRequests.id, id))
        .limit(1);

      if (!request.length || !request[0]) {
        throw new Error("Connection request not found");
      }

      const requestData = request[0];
      
      console.log('requestData bookingCompanion, bookingTraveler', requestData.bookingCompanion?.appwriteUserId, requestData.bookingTraveler?.appwriteUserId, ctx.session.user.$id);
      // Verify that the current user owns the companion booking or traveler booking (only companions or travelers can accept/reject)
      if (requestData.bookingCompanion?.appwriteUserId !== ctx.session.user.$id && requestData.bookingTraveler?.appwriteUserId !== ctx.session.user.$id) {
        throw new Error("Only the companion or traveler can accept or reject connection requests");
      }

      // Verify that the request is still pending
      if (requestData.status !== "PENDING") {
        throw new Error("Connection request has already been accepted / rejected");
      }

      return await ctx.db.transaction(async (tx) => {
        // Update the status
        const [updatedRequest] = await tx
          .update(connectionRequests)
          .set({
            status,
            updatedAt: new Date(),
          })
          .where(eq(connectionRequests.id, id))
          .returning();

        if (status === "ACCEPTED") {
          if (!requestData.bookingTraveler?.id) {
            throw new Error("Traveler not found");
          }

          if (!requestData.bookingCompanion?.id) {
            throw new Error("Companion not found");
          }

          await tx.update(bookingTravelers).set({
            status: "CONFIRMED",
          }).where(eq(bookingTravelers.id, requestData.bookingTraveler?.id));

          await tx.update(bookingCompanions).set({
            status: "CONFIRMED",
          }).where(eq(bookingCompanions.id, requestData.bookingCompanion?.id));
        }

        return updatedRequest;
      }).then(async (updatedRequest) => {
        // Create GetStream chat channel for the accepted connection (outside transaction)
        if (status === "ACCEPTED") {
          try {
            const travelerUserId = requestData.bookingTraveler?.appwriteUserId;
            const companionUserId = requestData.bookingCompanion?.appwriteUserId;
            
            if (travelerUserId && companionUserId) {
              await streamChatService.createChannel(
                travelerUserId,
                companionUserId,
                `connection-${id}`
              );
            }
          } catch (error) {
            console.error('Failed to create chat channel for accepted connection:', error);
            // Don't fail the connection acceptance if chat creation fails
          }
        }

        return updatedRequest;
      });
    }),
  cancel: protectedProcedure
    .input(cancelConnectionRequestSchema)
    .mutation(async ({ ctx, input }) => {
      const { id } = input;
      
      const request = await ctx.db
        .select({
          id: connectionRequests.id,
          status: connectionRequests.status,
          initiator: connectionRequests.initiator,
          originalTravelerPrice: connectionRequests.originalTravelerPrice,
          originalCompanionPrice: connectionRequests.originalCompanionPrice,
          proposedPrice: connectionRequests.proposedPrice,
          bookingCompanion: {
            id: bookingCompanions.id,
            appwriteUserId: bookingCompanions.appwriteUserId,
            flightTime: bookingCompanions.flightTime,
            compensationValue: bookingCompanions.compensationValue,
          },
          bookingTraveler: {
            id: bookingTravelers.id,
            appwriteUserId: bookingTravelers.appwriteUserId,
            flightTime: bookingTravelers.flightTime,
            compensationValue: bookingTravelers.compensationValue,
          },
        })
        .from(connectionRequests)
        .where(eq(connectionRequests.id, id))
        .leftJoin(bookingCompanions, eq(connectionRequests.bookingCompanionId, bookingCompanions.id))
        .leftJoin(bookingTravelers, eq(connectionRequests.bookingTravelerId, bookingTravelers.id))
        .limit(1);

      if (!request.length || !request[0]) {
        throw new Error("Connection request not found");
      }

      const requestData = request[0];
      // Only the party who didn't initiate can cancel
      const isTraveler = requestData.bookingCompanion?.appwriteUserId === ctx.session.user.$id;
      const isCompanion = requestData.bookingTraveler?.appwriteUserId === ctx.session.user.$id;
      
      if (!isTraveler && !isCompanion) {
        throw new Error("You are not authorized to cancel this connection request");
      }

      // Calculate cancellation fee based on flight time
      const now = new Date();
      const flightTime = isTraveler ? requestData.bookingCompanion?.flightTime : requestData.bookingTraveler?.flightTime;
      
      if (!flightTime) {
        throw new Error("Flight time not found for this booking");
      }

      const flightDate = new Date(flightTime);
      const daysUntilFlight = Math.ceil((flightDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      let cancellationFee = 0;
      let feePercentage = 0;
      
      if (!isCancelableWithoutFeesStatus(requestData.status, requestData.bookingTraveler?.flightTime!)) {
        // Apply 10% cancellation fee if within 3 days of flight
        const compensationValue = isTraveler ? requestData.bookingTraveler?.compensationValue : requestData.bookingCompanion?.compensationValue;
        if (compensationValue) {
          cancellationFee = Math.round(compensationValue * 0.1); // 10% fee
          feePercentage = 10;
        }
      }

      return await ctx.db.transaction(async (tx) => {
        // Update the status of the connection request
        const updatedStatus = isTraveler ? 'CANCELLED_BY_TRAVELER' : 'CANCELLED_BY_COMPANION';

        await tx.update(connectionRequests).set({
          status: updatedStatus,
          updatedAt: new Date(),
        }).where(eq(connectionRequests.id, id));

        // Apply cancellation fee if applicable
        if (cancellationFee > 0) {
          const userAppwriteId = ctx.session.user.$id as string;
          
          // Deduct from user's wallet
          const newBalance = await updateUserWalletBalanceInTransaction(tx, userAppwriteId, cancellationFee, 'debit');
          
          // Create wallet transaction record
          await tx
            .insert(walletTransactions)
            .values({
              appwriteUserId: userAppwriteId,
              type: 'debit',
              amount: cancellationFee,
              currency: 'usd',
              balance: newBalance,
              description: `Cancellation fee (${feePercentage}%) for connection request ${id}`,
              status: "completed",
            });
        }

        // Update booking statuses
        // Cancel the booking of the user who cancelled
        // Reactivate the other user's booking so they can find new connections
        if (isTraveler) {
          // Traveler cancelled, so cancel traveler booking and reactivate companion booking
          await tx.update(bookingCompanions).set({
            status: "CANCELLED",
            updatedAt: new Date(),
          }).where(eq(bookingCompanions.id, requestData.bookingCompanion?.id as string));

          await tx.update(bookingTravelers).set({
            status: "ACTIVE",
            updatedAt: new Date(),
          }).where(eq(bookingTravelers.id, requestData.bookingTraveler?.id as string));
        } else {
          // Companion cancelled, so cancel companion booking and reactivate traveler booking
          await tx.update(bookingTravelers).set({
            status: "CANCELLED",
            updatedAt: new Date(),
          }).where(eq(bookingTravelers.id, requestData.bookingTraveler?.id as string));

          await tx.update(bookingCompanions).set({
            status: "ACTIVE",
            updatedAt: new Date(),
          }).where(eq(bookingCompanions.id, requestData.bookingCompanion?.id as string));
        }

        return { 
          success: true, 
          message: cancellationFee > 0 
            ? `Connection request cancelled successfully. A ${feePercentage}% cancellation fee of $${(cancellationFee / 100).toFixed(2)} has been deducted from your wallet.`
            : "Connection request cancelled successfully. No cancellation fee applied.",
          cancellationFee: cancellationFee / 100, // Return in dollars
          feePercentage,
          daysUntilFlight
        };
      });
    }),

  // Delete connection request (only by the traveler - now uses soft delete)
  delete: protectedProcedure
    .input(deleteConnectionRequestSchema)
    .mutation(async ({ ctx, input }) => {
      // Get the connection request with related data
      const request = await ctx.db
        .select({
          id: connectionRequests.id,
          status: connectionRequests.status,
          initiator: connectionRequests.initiator,
          bookingTraveler: {
            appwriteUserId: bookingTravelers.appwriteUserId,
          },
          bookingCompanion: {
            appwriteUserId: bookingCompanions.appwriteUserId,
          },
        })
        .from(connectionRequests)
        .leftJoin(bookingTravelers, eq(connectionRequests.bookingTravelerId, bookingTravelers.id))
        .leftJoin(bookingCompanions, eq(connectionRequests.bookingCompanionId, bookingCompanions.id))
        .where(eq(connectionRequests.id, input.id))
        .limit(1);

      if (!request.length) {
        throw new Error("Connection request not found");
      }

      if (!request[0]) {
        throw new Error("Connection request not found");
      }

      const requestData = request[0];

      // Verify that the current user owns the traveler booking (only sender can delete)
      if (
        (requestData.bookingTraveler?.appwriteUserId !== ctx.session.user.$id && requestData.initiator === 'COMPANION') || 
        (requestData.bookingCompanion?.appwriteUserId !== ctx.session.user.$id && requestData.initiator === 'TRAVELER')
      ) {
        throw new Error("Only the sender can delete connection requests");
      }

      // Verify that the request is still pending
      if (requestData.status !== "PENDING") {
        throw new Error("Cannot delete a processed connection request");
      }

      // Hard delete the connection request if not accepted by the other party
      const [updatedRequest] = await ctx.db
        .delete(connectionRequests)
        .where(eq(connectionRequests.id, input.id))
        .returning();

      return { success: true, request: updatedRequest };
    }),

  // Get pending connection requests count for notifications
  getPendingCount: protectedProcedure
    .query(async ({ ctx }) => {
      const userId = ctx.session.user.$id as string;

      const count = await ctx.db
        .select({ count: connectionRequests.id })
        .from(connectionRequests)
        .leftJoin(bookingCompanions, eq(connectionRequests.bookingCompanionId, bookingCompanions.id))
        .where(
          and(
            eq(bookingCompanions.appwriteUserId, userId),
            eq(connectionRequests.status, "PENDING")
          )
        );

      return { count: count.length };
    }),

  // Release payment for a companion
  releasePayment: protectedProcedure
    .input(releasePaymentSchema)
    .mutation(async ({ ctx, input }) => {
      const { connectionRequestId, amount, currency, description } = input;

      // Get the connection request
      const request = await ctx.db
        .select({
          id: connectionRequests.id,
          status: connectionRequests.status,
          bookingCompanion: {
            appwriteUserId: bookingCompanions.appwriteUserId,
          },
          bookingTraveler: {
            appwriteUserId: bookingTravelers.appwriteUserId,
          },
        })
        .from(connectionRequests)
        .leftJoin(bookingCompanions, eq(connectionRequests.bookingCompanionId, bookingCompanions.id))
        .leftJoin(bookingTravelers, eq(connectionRequests.bookingTravelerId, bookingTravelers.id))
        .where(eq(connectionRequests.id, connectionRequestId))
        .limit(1);

      if (!request.length || !request[0]) {
        throw new Error("Connection request not found");
      }

      const requestData = request[0];

      // Verify that the current user is the companion
      if (requestData.bookingCompanion?.appwriteUserId !== ctx.session.user.$id) {
        throw new Error("Only the companion can release payment");
      }

      // Verify that the connection request is accepted
      if (requestData.status !== "ACCEPTED") {
        throw new Error("Cannot release payment for a non-accepted connection request");
      }

      // Update companion's wallet balance
      const companionAppwriteUserId = requestData.bookingCompanion?.appwriteUserId;
      if (companionAppwriteUserId) {
        return await ctx.db.transaction(async (tx) => {
          const newBalance = await updateUserWalletBalanceInTransaction(tx, companionAppwriteUserId, amount, 'credit');

          // Create a wallet transaction for the companion
          await tx
            .insert(walletTransactions)
            .values({
              appwriteUserId: companionAppwriteUserId,
              type: 'credit',
              amount: amount,
              currency: currency,
              balance: newBalance,
              description: description || `Payment for connection request ${connectionRequestId}`,
              status: "completed",
            });

          return { success: true, message: "Payment released successfully" };
        });
      }

      // Create a Stripe transfer for the companion
      if (companionAppwriteUserId) {
        const account = await ctx.db
          .select()
          .from(stripeConnectAccounts)
          .where(eq(stripeConnectAccounts.appwriteUserId, companionAppwriteUserId))
          .limit(1);

        if (account.length === 0 || !account[0]) {
          throw new TRPCError({
            code: "INTERNAL_SERVER_ERROR",
            message: "Companion's Stripe account not found",
          });
        }

        const transfer = await stripe.transfers.create({
          amount: amount,
          currency: currency,
          destination: account[0].stripeAccountId,
          transfer_group: `connection-${connectionRequestId}`,
          description: description || `Payment for connection request ${connectionRequestId}`,
        });

        // Save transfer to database
        await ctx.db
          .insert(stripeTransfers)
          .values({
            appwriteUserId: companionAppwriteUserId,
            stripeTransferId: transfer.id,
            stripeConnectAccountId: account[0].stripeAccountId,
            amount: amount,
            currency: currency,
            status: "pending",
            metadata: {
              connectionRequestId,
              description: description || `Payment for connection request ${connectionRequestId}`,
            },
          });
      }

      return { success: true, message: "Payment released successfully" };
    }),

  // Cancel a connection request by companion
  cancelByCompanion: protectedProcedure
    .input(cancelConnectionRequestSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, reason } = input;

      // Get the connection request with related data
      const request = await ctx.db
        .select({
          id: connectionRequests.id,
          status: connectionRequests.status,
          bookingTraveler: {
            id: bookingTravelers.id,
            appwriteUserId: bookingTravelers.appwriteUserId,
            flightTime: bookingTravelers.flightTime,
            compensationValue: bookingTravelers.compensationValue,
          },
          bookingCompanion: {
            id: bookingCompanions.id,
            appwriteUserId: bookingCompanions.appwriteUserId,
            flightTime: bookingCompanions.flightTime,
            compensationValue: bookingCompanions.compensationValue,
          },
        })
        .from(connectionRequests)
        .leftJoin(bookingTravelers, eq(connectionRequests.bookingTravelerId, bookingTravelers.id))
        .leftJoin(bookingCompanions, eq(connectionRequests.bookingCompanionId, bookingCompanions.id))
        .where(eq(connectionRequests.id, id))
        .limit(1);

      if (!request.length || !request[0]) {
        throw new Error("Connection request not found");
      }

      const requestData = request[0];

      // Verify that the current user is the companion
      if (requestData.bookingCompanion?.appwriteUserId !== ctx.session.user.$id) {
        throw new Error("Only the companion can cancel connection requests");
      }

      // Verify that the connection request is still pending
      if (requestData.status !== "PENDING") {
        throw new Error("Cannot cancel a processed connection request");
      }

      // Calculate cancellation fee based on flight time
      const now = new Date();
      const flightTime = requestData.bookingCompanion?.flightTime;
      
      if (!flightTime) {
        throw new Error("Flight time not found for this booking");
      }

      const flightDate = new Date(flightTime);
      const daysUntilFlight = Math.ceil((flightDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      let cancellationFee = 0;
      let feePercentage = 0;
      
      if (!isCancelableWithoutFeesStatus(requestData.status, flightDate)) {
        // Apply 10% cancellation fee
        const compensationValue = requestData.bookingCompanion?.compensationValue;
        if (compensationValue) {
          cancellationFee = Math.round(compensationValue * 0.1); // 10% fee
          feePercentage = 10;
        }
      }

      // Update the status to CANCELLED_BY_COMPANION
      const [updatedRequest] = await ctx.db
        .update(connectionRequests)
        .set({
          status: "CANCELLED_BY_COMPANION",
          updatedAt: new Date(),
        })
        .where(eq(connectionRequests.id, id))
        .returning();

      if (!updatedRequest) {
        throw new Error("Failed to update connection request status");
      }

      // Apply cancellation fee if applicable
      if (cancellationFee > 0) {
        const companionAppwriteUserId = requestData.bookingCompanion?.appwriteUserId;
        if (companionAppwriteUserId) {
          return await ctx.db.transaction(async (tx) => {
            // Deduct from companion's wallet
            const newBalance = await updateUserWalletBalanceInTransaction(tx, companionAppwriteUserId, cancellationFee, 'debit');
            
            // Create wallet transaction record
            await tx
              .insert(walletTransactions)
              .values({
                appwriteUserId: companionAppwriteUserId,
                type: 'debit',
                amount: cancellationFee,
                currency: 'usd',
                balance: newBalance,
                description: `Cancellation fee (${feePercentage}%) for connection request ${id}`,
                status: "completed",
              });

            return {
              ...updatedRequest,
              cancellationFee: cancellationFee / 100, // Return in dollars
              feePercentage,
              daysUntilFlight,
              message: cancellationFee > 0 
                ? `Connection request cancelled successfully. A ${feePercentage}% cancellation fee of $${(cancellationFee / 100).toFixed(2)} has been deducted from your wallet.`
                : "Connection request cancelled successfully. No cancellation fee applied."
            };
          });
        }
      }

      // Update companion's wallet balance (if payment was already released)
      const companionAppwriteUserId = requestData.bookingCompanion?.appwriteUserId;
      if (companionAppwriteUserId) {
        const existingTransactions = await ctx.db
          .select()
          .from(walletTransactions)
          .where(
            and(
              eq(walletTransactions.appwriteUserId, companionAppwriteUserId),
              eq(walletTransactions.type, 'credit'),
              eq(walletTransactions.description, `Payment for connection request ${id}`),
            )
          );

        if (existingTransactions.length > 0 && existingTransactions[0] && companionAppwriteUserId && existingTransactions[0].amount) {
          await ctx.db.transaction(async (tx) => {
            await updateUserWalletBalanceInTransaction(tx, companionAppwriteUserId, existingTransactions[0].amount, 'debit');
          });
        }
      }

      // Update booking statuses
      // Cancel the companion booking and reactivate the traveler booking
      await ctx.db.update(bookingCompanions).set({
        status: "CANCELLED",
        updatedAt: new Date(),
      }).where(eq(bookingCompanions.id, requestData.bookingCompanion?.id as string));

      await ctx.db.update(bookingTravelers).set({
        status: "ACTIVE",
        updatedAt: new Date(),
      }).where(eq(bookingTravelers.id, requestData.bookingTraveler?.id as string));

      return {
        ...updatedRequest,
        cancellationFee: cancellationFee / 100, // Return in dollars
        feePercentage,
        daysUntilFlight,
        message: cancellationFee > 0 
          ? `Connection request cancelled successfully. A ${feePercentage}% cancellation fee of $${(cancellationFee / 100).toFixed(2)} has been deducted from your wallet.`
          : "Connection request cancelled successfully. No cancellation fee applied."
      };
    }),

  // Cancel a connection request by traveler
  cancelByTraveler: protectedProcedure
    .input(cancelConnectionRequestSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, reason } = input;

      // Get the connection request with related data
      const request = await ctx.db
        .select({
          id: connectionRequests.id,
          status: connectionRequests.status,
          bookingTraveler: {
            id: bookingTravelers.id,
            appwriteUserId: bookingTravelers.appwriteUserId,
            flightTime: bookingTravelers.flightTime,
            compensationValue: bookingTravelers.compensationValue,
          },
          bookingCompanion: {
            id: bookingCompanions.id,
            appwriteUserId: bookingCompanions.appwriteUserId,
            flightTime: bookingCompanions.flightTime,
            compensationValue: bookingCompanions.compensationValue,
          },
        })
        .from(connectionRequests)
        .leftJoin(bookingTravelers, eq(connectionRequests.bookingTravelerId, bookingTravelers.id))
        .leftJoin(bookingCompanions, eq(connectionRequests.bookingCompanionId, bookingCompanions.id))
        .where(eq(connectionRequests.id, id))
        .limit(1);

      if (!request.length || !request[0]) {
        throw new Error("Connection request not found");
      }

      const requestData = request[0];

      // Verify that the current user is the traveler
      if (requestData.bookingTraveler?.appwriteUserId !== ctx.session.user.$id) {
        throw new Error("Only the traveler can cancel their own connection requests");
      }

      // Verify that the connection request is still pending
      if (requestData.status !== "PENDING") {
        throw new Error("Cannot cancel a processed connection request");
      }

      // Calculate cancellation fee based on flight time
      const now = new Date();
      const flightTime = requestData.bookingTraveler?.flightTime;
      
      if (!flightTime) {
        throw new Error("Flight time not found for this booking");
      }

      const flightDate = new Date(flightTime);
      const daysUntilFlight = Math.ceil((flightDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
      
      let cancellationFee = 0;
      let feePercentage = 0;
      
      if (!isCancelableWithoutFeesStatus(requestData.status, flightDate)) {
        // Apply 10% cancellation fee if within 3 days of flight
        const compensationValue = requestData.bookingTraveler?.compensationValue;
        if (compensationValue) {
          cancellationFee = Math.round(compensationValue * 0.1); // 10% fee
          feePercentage = 10;
        }
      }

      // Update the status to CANCELLED_BY_TRAVELER
      const [updatedRequest] = await ctx.db
        .update(connectionRequests)
        .set({
          status: "CANCELLED_BY_TRAVELER",
          updatedAt: new Date(),
        })
        .where(eq(connectionRequests.id, id))
        .returning();

      if (!updatedRequest) {
        throw new Error("Failed to update connection request status");
      }

      // Apply cancellation fee if applicable
      if (cancellationFee > 0) {
        const travelerAppwriteUserId = requestData.bookingTraveler?.appwriteUserId;
        if (travelerAppwriteUserId) {
          return await ctx.db.transaction(async (tx) => {
            // Deduct from traveler's wallet
            const newBalance = await updateUserWalletBalanceInTransaction(tx, travelerAppwriteUserId, cancellationFee, 'debit');
            
            // Create wallet transaction record
            await tx
              .insert(walletTransactions)
              .values({
                appwriteUserId: travelerAppwriteUserId,
                type: 'debit',
                amount: cancellationFee,
                currency: 'usd',
                balance: newBalance,
                description: `Cancellation fee (${feePercentage}%) for connection request cancellation with 3 days notice.`,
                status: "completed",
              });

            return {
              ...updatedRequest,
              cancellationFee: cancellationFee / 100, // Return in dollars
              feePercentage,
              daysUntilFlight,
              message: cancellationFee > 0 
                ? `Connection request cancelled successfully. A ${feePercentage}% cancellation fee of $${(cancellationFee / 100).toFixed(2)} has been deducted from your wallet.`
                : "Connection request cancelled successfully. No cancellation fee applied."
            };
          });
        }
      }

      // Update booking statuses
      // Cancel the traveler booking and reactivate the companion booking
      await ctx.db.update(bookingTravelers).set({
        status: "CANCELLED",
        updatedAt: new Date(),
      }).where(eq(bookingTravelers.id, requestData.bookingTraveler?.id as string));

      await ctx.db.update(bookingCompanions).set({
        status: "ACTIVE",
        updatedAt: new Date(),
      }).where(eq(bookingCompanions.id, requestData.bookingCompanion?.id as string));

      return {
        ...updatedRequest,
        cancellationFee: cancellationFee / 100, // Return in dollars
        feePercentage,
        daysUntilFlight,
        message: cancellationFee > 0 
          ? `Connection request cancelled successfully. A ${feePercentage}% cancellation fee of $${(cancellationFee / 100).toFixed(2)} has been deducted from your wallet.`
          : "Connection request cancelled successfully. No cancellation fee applied."
      };
    }),

  // Propose a new price for a connection request
  proposePrice: protectedProcedure
    .input(proposePriceSchema)
    .mutation(async ({ ctx, input }) => {
      const { connectionRequestId, proposedPrice, message } = input;

      // Get the connection request
      const request = await ctx.db
        .select({
          id: connectionRequests.id,
          status: connectionRequests.status,
          bookingTraveler: {
            appwriteUserId: bookingTravelers.appwriteUserId,
          },
          bookingCompanion: {
            appwriteUserId: bookingCompanions.appwriteUserId,
          },
          priceNegotiationHistory: connectionRequests.priceNegotiationHistory,
        })
        .from(connectionRequests)
        .leftJoin(bookingTravelers, eq(connectionRequests.bookingTravelerId, bookingTravelers.id))
        .leftJoin(bookingCompanions, eq(connectionRequests.bookingCompanionId, bookingCompanions.id))
        .where(eq(connectionRequests.id, connectionRequestId))
        .limit(1);

      if (!request.length || !request[0]) {
        throw new Error("Connection request not found");
      }

      const requestData = request[0];

      // Verify user owns one of the bookings
      if (requestData.bookingTraveler?.appwriteUserId !== ctx.session.user.$id && 
          requestData.bookingCompanion?.appwriteUserId !== ctx.session.user.$id) {
        throw new Error("Unauthorized to negotiate this request");
      }

      // Verify request is in negotiation status
      if (requestData.status !== "PENDING_NEGOTIATION") {
        throw new Error("Request is not in negotiation status");
      }

      // Update negotiation history
      const updatedHistory = [
        ...(requestData.priceNegotiationHistory || []),
        {
          userId: ctx.session.user.$id as string,
          proposedPrice: proposedPrice,
          timestamp: new Date().toISOString(),
          message: message || "Price proposal",
        }
      ];

      // Update the request
      const [updatedRequest] = await ctx.db
        .update(connectionRequests)
        .set({
          proposedPrice: proposedPrice,
          priceNegotiationHistory: updatedHistory,
          updatedAt: new Date(),
        })
        .where(eq(connectionRequests.id, connectionRequestId))
        .returning();

      return updatedRequest;
    }),

  // Accept a negotiated price for a connection request
  acceptNegotiatedPrice: protectedProcedure
    .input(acceptNegotiatedPriceSchema)
    .mutation(async ({ ctx, input }) => {
      const { connectionRequestId, confirmPrice } = input;

      // Get the connection request
      const request = await ctx.db
        .select({
          id: connectionRequests.id,
          status: connectionRequests.status,
          proposedPrice: connectionRequests.proposedPrice,
          bookingTraveler: {
            id: bookingTravelers.id,
            appwriteUserId: bookingTravelers.appwriteUserId,
          },
          bookingCompanion: {
            id: bookingCompanions.id,
            appwriteUserId: bookingCompanions.appwriteUserId,
          },
        })
        .from(connectionRequests)
        .leftJoin(bookingTravelers, eq(connectionRequests.bookingTravelerId, bookingTravelers.id))
        .leftJoin(bookingCompanions, eq(connectionRequests.bookingCompanionId, bookingCompanions.id))
        .where(eq(connectionRequests.id, connectionRequestId))
        .limit(1);

      if (!request.length || !request[0]) {
        throw new Error("Connection request not found");
      }

      const requestData = request[0];

      // Verify user owns one of the bookings
      if (requestData.bookingTraveler?.appwriteUserId !== ctx.session.user.$id && 
          requestData.bookingCompanion?.appwriteUserId !== ctx.session.user.$id) {
        throw new Error("Unauthorized to accept this request");
      }

      // Verify request is in negotiation status
      if (requestData.status !== "PENDING_NEGOTIATION") {
        throw new Error("Request is not in negotiation status");
      }

      // Verify the confirmed price matches the proposed price
      if (requestData.proposedPrice !== confirmPrice) {
        throw new Error("Confirmed price does not match the proposed price");
      }

      // Update to PENDING status (ready for final acceptance)
      const [updatedRequest] = await ctx.db
        .update(connectionRequests)
        .set({
          status: "ACCEPTED",
          updatedAt: new Date(),
        })
        .where(eq(connectionRequests.id, connectionRequestId))
        .returning();

        await ctx.db.update(bookingTravelers).set({
          status: "CONFIRMED",
        }).where(eq(bookingTravelers.id, requestData.bookingTraveler?.id as string));

        await ctx.db.update(bookingCompanions).set({
          status: "CONFIRMED",
        }).where(eq(bookingCompanions.id, requestData.bookingCompanion?.id as string));

        // Create GetStream chat channel for the accepted connection
        try {
          const travelerUserId = requestData.bookingTraveler?.appwriteUserId;
          const companionUserId = requestData.bookingCompanion?.appwriteUserId;
          
          if (travelerUserId && companionUserId) {
            await streamChatService.createChannel(
              travelerUserId,
              companionUserId,
              `connection-${requestData.id}`
            );
          }
        } catch (error) {
          console.error('Failed to create chat channel for accepted connection:', error);
          // Don't fail the connection acceptance if chat creation fails
        }

      return updatedRequest;
    }),
}); 