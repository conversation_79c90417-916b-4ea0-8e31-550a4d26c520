import { z } from "zod";
import { eq, and, sql } from "drizzle-orm";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { 
  stripeConnectAccounts,
  stripeTransfers,
  walletTransactions,
  userProfiles
} from "@repo/db/schema";
import Stripe from "stripe";
import { TRPCError } from "@trpc/server";

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2025-06-30.basil",
});

// Input schemas for validation
const createConnectAccountSchema = z.object({
  email: z.string().email(),
  name: z.string().min(1),
  phone: z.string().min(1),
});

const getConnectAccountSchema = z.object({
  accountId: z.string(),
});

const createAccountLinkSchema = z.object({
  accountId: z.string(),
  refreshUrl: z.string().url(),
  returnUrl: z.string().url(),
});

const createTransferSchema = z.object({
  amount: z.number().positive(), // in cents
  currency: z.string().default("usd"),
  bookingTravelerId: z.string().uuid().optional(),
  bookingCompanionId: z.string().uuid().optional(),
  description: z.string().optional(),
});

const getWalletBalanceSchema = z.object({
  appwriteUserId: z.string(),
});

export const stripeCompanionRouter = createTRPCRouter({
  // Create Stripe Connect account for companion
  createConnectAccount: protectedProcedure
    .mutation(async ({ ctx, input }) => {
      try {
        // Check if user already has a Connect account
        const existingAccount = await ctx.db
          .select()
          .from(stripeConnectAccounts)
          .where(eq(stripeConnectAccounts.appwriteUserId, ctx.session.user.$id as string))
          .limit(1);

        if (existingAccount.length > 0 && existingAccount[0]) {
          return {
            success: true,
            accountId: existingAccount[0].stripeAccountId,
            capabilities: existingAccount[0].capabilities,
            requirements: existingAccount[0].requirements,
          };
        }

        // Create new Stripe Connect account
        const account = await stripe.accounts.create({
          controller: {
            fees: {
              payer: 'application',
            },
            losses: {
              payments: 'application',
            },
            stripe_dashboard: {
              type: "express",
            }
          }, 
          // email: ctx.session.user.email as string || "",
          metadata: {
            phone: ctx.session.user.phone as string || "",
            appwriteUserId: ctx.session.user.$id as string,
          },
        });

        // Save to database
        const [savedAccount] = await ctx.db
          .insert(stripeConnectAccounts)
          .values({
            appwriteUserId: ctx.session.user.$id as string,
            stripeAccountId: account.id,
            email: ctx.session.user.email as string || "",
            capabilities: account.capabilities,
            requirements: account.requirements,
            chargesEnabled: account.charges_enabled,
            payoutsEnabled: account.payouts_enabled,
            detailsSubmitted: account.details_submitted,
            metadata: account.metadata,
          })
          .returning();

        // Update user data with Connect account ID
        await ctx.db
          .update(userProfiles)
          .set({
            stripeConnectAccountId: account.id,
            updatedAt: new Date(),
          })
          .where(eq(userProfiles.appwriteUserId, ctx.session.user.$id as string));

        return {
          success: true,
          accountId: account.id,
          capabilities: account.capabilities,
          requirements: account.requirements,
        };
      } catch (error) {
        console.error("Error creating Connect account:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create Stripe Connect account",
        });
      }
    }),

  // Get Stripe Connect account details
  getConnectAccount: protectedProcedure
    .query(async ({ ctx }) => {
      // Verify ownership
      const accounts = await ctx.db
        .select()
        .from(stripeConnectAccounts)
        .where(
          and(
            eq(stripeConnectAccounts.appwriteUserId, ctx.session.user.$id as string)
          )
        )
        .limit(1);
        
      console.log('stripe account', accounts);
      
      if (accounts.length === 0 || !accounts[0]) {
        // If no account exists, create a new Stripe Connect account and save it
        // const user = await ctx.db
        //   .select()
        //   .from(userProfiles)
        //   .where(eq(userProfiles.appwriteUserId, ctx.session.user.$id as string))
        //   .limit(1);

        // if (!user.length || !user[0]) {
        //   throw new TRPCError({
        //     code: "NOT_FOUND",
        //     message: "User not found",
        //   });
        // }

        // Create a new Stripe Connect account
        const stripeAccount = await stripe.accounts.create({
          controller: {
            fees: {
              payer: 'application',
            },
            losses: {
              payments: 'application',
            },
            stripe_dashboard: {
              type: "express",
            }
          }, 
          // email: ctx.session.user.email as string || "",
          metadata: {
            phone: ctx.session.user.phone as string || "",
            appwriteUserId: ctx.session.user.$id as string,
          },
        });

        // Save the new account in the local database
        await ctx.db
          .insert(stripeConnectAccounts)
          .values({
            stripeAccountId: stripeAccount.id,
            appwriteUserId: ctx.session.user.$id as string,
            email: ctx.session.user.email as string || "",
            capabilities: stripeAccount.capabilities,
            requirements: stripeAccount.requirements,
            chargesEnabled: stripeAccount.charges_enabled,
            payoutsEnabled: stripeAccount.payouts_enabled,
            detailsSubmitted: stripeAccount.details_submitted,
            metadata: stripeAccount.metadata,
            createdAt: new Date(),
            updatedAt: new Date(),
          });

        // Optionally, update user profile with the new Stripe Connect Account ID
        await ctx.db
          .update(userProfiles)
          .set({
            stripeConnectAccountId: stripeAccount.id,
            updatedAt: new Date(),
          })
          .where(eq(userProfiles.appwriteUserId, ctx.session.user.$id as string));

        return {
          success: true,
          accountId: stripeAccount.id,
          capabilities: stripeAccount.capabilities,
          requirements: stripeAccount.requirements,
          chargesEnabled: stripeAccount.charges_enabled,
          payoutsEnabled: stripeAccount.payouts_enabled,
          detailsSubmitted: stripeAccount.details_submitted,
        };
      }

      console.log('stripe account', accounts[0]);

      // Get fresh data from Stripe
      const stripeAccount = await stripe.accounts.retrieve(accounts[0].stripeAccountId);

      // Update local database with fresh data
      await ctx.db
        .update(stripeConnectAccounts)
        .set({
          capabilities: stripeAccount.capabilities,
          requirements: stripeAccount.requirements,
          chargesEnabled: stripeAccount.charges_enabled,
          payoutsEnabled: stripeAccount.payouts_enabled,
          detailsSubmitted: stripeAccount.details_submitted,
          updatedAt: new Date(),
        })
        .where(eq(stripeConnectAccounts.appwriteUserId, ctx.session.user.$id as string));

      return {
        success: true,
        accountId: stripeAccount.id,
        capabilities: stripeAccount.capabilities,
        requirements: stripeAccount.requirements,
        chargesEnabled: stripeAccount.charges_enabled,
        payoutsEnabled: stripeAccount.payouts_enabled,
        detailsSubmitted: stripeAccount.details_submitted,
      };
    }),

  // Create account link for onboarding/updates
  createAccountLink: protectedProcedure
    .mutation(async ({ ctx }) => {
      try {
        // Verify ownership
        const account = await ctx.db
          .select()
          .from(stripeConnectAccounts)
          .where(
            and(
              eq(stripeConnectAccounts.appwriteUserId, ctx.session.user.$id as string)
            )
          )
          .limit(1);

        if (!account.length || !account[0]) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "Connect account not found or access denied",
          });
        }

        // Create account link
        const accountLink = await stripe.accountLinks.create({
          account: account[0].stripeAccountId,
          refresh_url: "https://thedalapp.com",
          return_url: "https://thedalapp.com",
          type: "account_onboarding",
        });

        return {
          success: true,
          accountLink: accountLink.url,
        };
      } catch (error) {
        console.error("Error creating account link:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create account link",
        });
      }
    }),

  // Create transfer to companion
  createTransfer: protectedProcedure
    .input(createTransferSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        // Get companion's Connect account
        const connectAccount = await ctx.db
          .select()
          .from(stripeConnectAccounts)
          .where(eq(stripeConnectAccounts.appwriteUserId, ctx.session.user.$id as string))
          .limit(1);

        if (!connectAccount.length || !connectAccount[0]) {
          throw new TRPCError({
            code: "NOT_FOUND",
            message: "No Stripe Connect account found",
          });
        }

        if (!connectAccount[0].chargesEnabled) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Connect account is not ready to receive payments",
          });
        }

        // Create transfer
        const transfer = await stripe.transfers.create({
          amount: input.amount,
          currency: input.currency,
          destination: connectAccount[0].stripeAccountId,
          metadata: {
            bookingTravelerId: input.bookingTravelerId || null,
            bookingCompanionId: input.bookingCompanionId || null,
            description: input.description || null,
          },
        });

        // Save transfer to database
        const [savedTransfer] = await ctx.db
          .insert(stripeTransfers)
          .values({
            appwriteUserId: ctx.session.user.$id as string,
            stripeTransferId: transfer.id,
            stripeConnectAccountId: connectAccount[0].stripeAccountId,
            amount: input.amount,
            currency: input.currency,
            status: "pending", // Stripe transfers are typically pending initially
            bookingTravelerId: input.bookingTravelerId,
            bookingCompanionId: input.bookingCompanionId,
            metadata: transfer.metadata,
          })
          .returning();

        // Create wallet transaction record
        await ctx.db
          .insert(walletTransactions)
          .values({
            appwriteUserId: ctx.session.user.$id as string,
            type: "credit",
            amount: input.amount,
            currency: input.currency,
            balance: 0, // This should be calculated from current balance
            stripeTransferId: transfer.id,
            bookingTravelerId: input.bookingTravelerId,
            bookingCompanionId: input.bookingCompanionId,
            description: input.description || "Companion payment",
            status: "completed",
          });

        return {
          success: true,
          transferId: transfer.id,
          status: "pending", // Stripe transfers are typically pending initially
        };
      } catch (error) {
        console.error("Error creating transfer:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create transfer",
        });
      }
    }),

  // Get companion's wallet balance
  getWalletBalance: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        // Get wallet balance from user profile
        const userProfile = await ctx.db
          .select({
            walletBalance: userProfiles.walletBalance,
          })
          .from(userProfiles)
          .where(eq(userProfiles.appwriteUserId, ctx.session.user.$id as string))
          .limit(1);

        if (userProfile.length === 0) {
          // If user profile does not exist, create it with the initial wallet balance
          await ctx.db
            .insert(userProfiles)
            .values({
              name: ctx.session.user.name as string || "",
              email: ctx.session.user.email as string || "",
              phone: ctx.session.user.phone as string || "",
              appwriteUserId: ctx.session.user.$id as string,
              walletBalance: 0,
              createdAt: new Date(),
              updatedAt: new Date(),
            });
          return {
            balance: 0,
            currency: "usd",
          };
        }

        // Get transaction count for additional info
        const transactionCount = await ctx.db
          .select({ count: sql<number>`count(*)` })
          .from(walletTransactions)
          .where(eq(walletTransactions.appwriteUserId, ctx.session.user.$id as string));

        return {
          balance: userProfile[0]?.walletBalance || 0,
          currency: "usd",
        };
      } catch (error) {
        console.error("Error getting wallet balance:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get wallet balance",
        });
      }
    }),

  // Get companion's transfer history
  getTransferHistory: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const transfers = await ctx.db
          .select({
            id: stripeTransfers.id,
            stripeTransferId: stripeTransfers.stripeTransferId,
            amount: stripeTransfers.amount,
            currency: stripeTransfers.currency,
            status: stripeTransfers.status,
            bookingTravelerId: stripeTransfers.bookingTravelerId,
            bookingCompanionId: stripeTransfers.bookingCompanionId,
            createdAt: stripeTransfers.createdAt,
            metadata: stripeTransfers.metadata,
          })
          .from(stripeTransfers)
          .where(eq(stripeTransfers.appwriteUserId, ctx.session.user.$id as string))
          .orderBy(stripeTransfers.createdAt);

        return transfers;
      } catch (error) {
        console.error("Error getting transfer history:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get transfer history",
        });
      }
    }),

  // Get companion's Connect account status
  getConnectAccountStatus: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const account = await ctx.db
          .select({
            stripeAccountId: stripeConnectAccounts.stripeAccountId,
            chargesEnabled: stripeConnectAccounts.chargesEnabled,
            payoutsEnabled: stripeConnectAccounts.payoutsEnabled,
            detailsSubmitted: stripeConnectAccounts.detailsSubmitted,
            capabilities: stripeConnectAccounts.capabilities,
            requirements: stripeConnectAccounts.requirements,
            createdAt: stripeConnectAccounts.createdAt,
          })
          .from(stripeConnectAccounts)
          .where(eq(stripeConnectAccounts.appwriteUserId, ctx.session.user.$id as string))
          .limit(1);

        if (!account.length) {
          return null;
        }

        return account[0];
      } catch (error) {
        console.error("Error getting Connect account status:", error);
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to get Connect account status",
        });
      }
    }),
}); 