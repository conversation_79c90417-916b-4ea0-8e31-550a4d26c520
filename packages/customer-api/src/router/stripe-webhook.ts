import { z } from "zod";
import { createTRPCRouter, publicProcedure } from "../trpc";
import { 
  stripePaymentIntents,
  stripePaymentSessions,
  stripeTransfers,
  walletTransactions,
  stripeWebhookEvents,
  userProfiles
} from "@repo/db";
import Stripe from "stripe";
import { eq, sql } from "drizzle-orm";
import { env } from "../../env";
import { DrizzleTransaction } from "@repo/db/client";

// Initialize Stripe
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2025-06-30.basil",
});

// Webhook event schema
const webhookEventSchema = z.any();

// Helper function to update user wallet balance within a transaction
async function updateUserWalletBalanceInTransaction(tx: DrizzleTransaction, appwriteUserId: string, amount: number, type: 'credit' | 'debit') {
  // Get current user profile
  let userProfile = await tx
    .select({ walletBalance: userProfiles.walletBalance })
    .from(userProfiles)
    .where(eq(userProfiles.appwriteUserId, appwriteUserId))
    .limit(1);

  if (userProfile.length === 0) {
    // If user profile does not exist, create it with the initial wallet balance
    await tx
      .insert(userProfiles)
      .values({
        appwriteUserId,
        walletBalance: type === 'credit' ? amount : -amount,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
    return type === 'credit' ? amount : -amount;
  }

  const currentBalance = userProfile[0].walletBalance || 0;
  const newBalance = type === 'credit' ? currentBalance + amount : currentBalance - amount;

  // Update user profile wallet balance
  await tx
    .update(userProfiles)
    .set({
      walletBalance: newBalance,
      updatedAt: new Date(),
    })
    .where(eq(userProfiles.appwriteUserId, appwriteUserId));

  return newBalance;
}

export const stripeWebhookRouter = createTRPCRouter({
  // Handle Stripe webhook events
  handleWebhook: publicProcedure
    .input(webhookEventSchema)
    .mutation(async ({ ctx, input }) => {
      const signature = ctx.signature;

      if (!signature) {
        throw new Error("Signature is required");
      }

      const event: Stripe.Event = stripe.webhooks.constructEvent(ctx.rawBody, signature, env.STRIPE_WEBHOOK_SECRET);

      // Check if event has already been processed
      const existingEvent = await ctx.db
        .select()
        .from(stripeWebhookEvents)
        .where(eq(stripeWebhookEvents.stripeEventId, event.id))
        .limit(1);

      if (existingEvent.length > 0) {
        console.log(`Event ${event.id} already processed`);
        return { success: true, message: "Event already processed" };
      }

      // Save event to database
      await ctx.db
        .insert(stripeWebhookEvents)
        .values({
          stripeEventId: event.id,
          type: event.type,
          data: event.data,
        });
      
      return await ctx.db.transaction(async (tx) => {
        try {
          // Process different event types
          switch (event.type) {
            case "payment_intent.succeeded":
              await handlePaymentIntentSucceededInTransaction(tx, event.data.object);
              break;
            
            case "checkout.session.completed":
              await handleCheckoutSessionCompletedInTransaction(tx, event.data.object);
              break;
            
            case "transfer.created":
              await handleTransferCreatedInTransaction(tx, event.data.object);
              break;
            
            // case "transfer.paid":
            //   await handleTransferPaidInTransaction(tx, event.data.object);
            //   break;
            
            default:
              console.log(`Unhandled event type: ${event.type}`);
          }

          // Mark event as processed
          await tx
            .update(stripeWebhookEvents)
            .set({
              processed: true,
              processedAt: new Date(),
            })
            .where(eq(stripeWebhookEvents.stripeEventId, event.id));

          return { success: true };
        } catch (error) {
          console.error("Error processing webhook:", error);
          
          // Mark event as failed
          await tx
            .update(stripeWebhookEvents)
            .set({
              error: error instanceof Error ? error.message : String(error),
              retryCount: sql`${stripeWebhookEvents.retryCount} + 1`,
            })
            .where(eq(stripeWebhookEvents.stripeEventId, event.id));

          throw new Error("Failed to process webhook");
        }
      });
    }),
});

// Helper functions for processing different event types within transactions
async function handlePaymentIntentSucceededInTransaction(tx: DrizzleTransaction, paymentIntent: Stripe.PaymentIntent) {
  // Update payment intent status
  const updatedPaymentIntent = await tx
    .update(stripePaymentIntents)
    .set({
      status: paymentIntent.status,
      updatedAt: new Date(),
    })
    .where(eq(stripePaymentIntents.stripePaymentIntentId, paymentIntent.id))
    .returning();

  console.log("updatedPaymentIntent", updatedPaymentIntent);

  // Update user wallet balance
  const newBalance = await updateUserWalletBalanceInTransaction(
    tx, 
    updatedPaymentIntent[0].appwriteUserId, 
    paymentIntent.amount, 
    'credit'
  );

  // Create wallet transaction
  await tx
    .insert(walletTransactions)
    .values({
      appwriteUserId: updatedPaymentIntent[0].appwriteUserId,
      stripeTransferId: paymentIntent.metadata.stripeTransferId,
      type: "credit",
      amount: paymentIntent.amount,
      currency: paymentIntent.currency,
      balance: newBalance,
      stripePaymentIntentId: paymentIntent.id,
      description: "Wallet top-up",
      status: "completed",
    });
}

async function handleCheckoutSessionCompletedInTransaction(tx: DrizzleTransaction, session: Stripe.Checkout.Session) {
  // Update session status
  const updatedSession = await tx
    .update(stripePaymentSessions)
    .set({
      status: session.status,
      updatedAt: new Date(),
    })
    .where(eq(stripePaymentSessions.stripeSessionId, session.id))
    .returning();

  console.log("updatedSession", updatedSession);

  // Create wallet transaction if amount_total exists
  if (session.amount_total && session.metadata?.stripeTransferId) {
    // Update user wallet balance
    const newBalance = await updateUserWalletBalanceInTransaction(
      tx, 
      updatedSession[0].appwriteUserId, 
      session.amount_total, 
      'credit'
    );

    await tx
      .insert(walletTransactions)
      .values({
        appwriteUserId: updatedSession[0].appwriteUserId,
        stripeTransferId: session.metadata.stripeTransferId,
        type: "credit",
        amount: session.amount_total,
        currency: session.currency || "usd",
        balance: newBalance,
        description: "Checkout payment",
        status: "completed",
      });
  }
}

async function handleTransferCreatedInTransaction(tx: DrizzleTransaction, transfer: Stripe.Transfer) {
  // Update transfer status
  await tx
    .update(stripeTransfers)
    .set({
      status: 'pending',
      updatedAt: new Date(),
    })
    .where(eq(stripeTransfers.stripeTransferId, transfer.id));
}

async function handleTransferPaidInTransaction(tx: DrizzleTransaction, transfer: Stripe.Transfer) {
  // Update transfer status
  const updatedTransfer = await tx
    .update(stripeTransfers)
    .set({
      status: 'paid',
      updatedAt: new Date(),
    })
    .where(eq(stripeTransfers.stripeTransferId, transfer.id))
    .returning();

  // Get companion's appwriteUserId from the transfer
  const companionAppwriteUserId = updatedTransfer[0]?.appwriteUserId;
  
  if (companionAppwriteUserId) {
    // Update companion's wallet balance
    const newBalance = await updateUserWalletBalanceInTransaction(
      tx, 
      companionAppwriteUserId, 
      transfer.amount, 
      'credit'
    );

    // Create wallet transaction for companion
    await tx
      .insert(walletTransactions)
      .values({
        appwriteUserId: companionAppwriteUserId,
        type: "credit",
        amount: transfer.amount,
        currency: transfer.currency,
        balance: newBalance,
        stripeTransferId: transfer.id,
        description: "Companion payment",
        status: "completed",
      });
  }
} 