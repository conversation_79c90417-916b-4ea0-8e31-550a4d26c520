import { z } from "zod";
import { desc, eq } from "drizzle-orm";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { 
  stripeCustomers,
  stripePaymentIntents,
  stripePaymentSessions,
  walletTransactions,
  userProfiles
} from "@repo/db/schema";
import <PERSON><PERSON> from "stripe";
import { TRPCError } from "@trpc/server";

// Initialize Stripe with current stable API version
const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: "2025-06-30.basil",
});

// Helper function to get or create a customer
// TODO: any is a temporary fix, we need to fix this
async function getOrCreateCustomer(ctx: any) {
  try {
    // Check if user already has a customer
    const existingCustomer = await ctx.db
      .select()
      .from(stripeCustomers)
      .where(eq(stripeCustomers.appwriteUserId, ctx.session.user.$id as string))
      .limit(1);

    if (existingCustomer.length > 0 && existingCustomer[0]) {
      // Return existing customer
      const stripeCustomer = await stripe.customers.retrieve(existingCustomer[0].stripeCustomerId);
      return stripeCustomer;
    }

    const userData = await ctx.session.user;

    // If no customer exists and we have user data, create one
    if (userData && userData.name && userData.phone) {
      // Search for existing customer by phone first
      const searchResult = await stripe.customers.search({
        query: `phone:"${userData.phone}"`,
      });

      if (searchResult.data.length > 0 && searchResult.data[0]) {
        // Update existing customer
        const customer = await stripe.customers.update(searchResult.data[0].id, {
          name: userData.name,
          email: userData.email,
        });

        // Save to database
        await ctx.db
          .insert(stripeCustomers)
          .values({
            appwriteUserId: ctx.session.user.$id as string,
            stripeCustomerId: customer.id,
            name: userData.name,
            email: userData.email,
            phone: userData.phone,
          });

        // Update user data with customer ID
        await ctx.db
          .update(userProfiles)
          .set({
            stripeCustomerId: customer.id,
            updatedAt: new Date(),
          })
          .where(eq(userProfiles.appwriteUserId, ctx.session.user.$id as string));

        return customer;
      }

      // Create new customer
      const customer = await stripe.customers.create({
        name: userData.name,
        email: userData.email,
        phone: userData.phone,
      });

      // Save to database
      await ctx.db
        .insert(stripeCustomers)
        .values({
          appwriteUserId: ctx.session.user.$id as string,
          stripeCustomerId: customer.id,
          name: userData.name,
          email: userData.email,
          phone: userData.phone,
        });

      // Update user data with customer ID
      await ctx.db
        .update(userProfiles)
        .set({
          stripeCustomerId: customer.id,
          updatedAt: new Date(),
        })
        .where(eq(userProfiles.appwriteUserId, ctx.session.user.$id as string));

      return customer;
    }

    // If no customer exists and no user data provided, throw error
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "No customer found and no user data provided to create one",
    });
  } catch (error) {
    console.error("Error in getOrCreateCustomer:", error);
    throw error;
  }
}

// Input schemas for validation
const createCustomerSchema = z.object({
  name: z.string().min(1),
  email: z.string().email(),
  phone: z.string().min(1),
});

const createPaymentIntentSchema = z.object({
  amount: z.number().positive(), // in cents
  currency: z.string().default("usd"),
});

const createCheckoutSessionSchema = z.object({
  amount: z.number().positive(), // in cents
  currency: z.string().default("usd"),
  successUrl: z.string().url(),
  cancelUrl: z.string().url(),
  bookingTravelerId: z.string().uuid().optional(),
  bookingCompanionId: z.string().uuid().optional(),
});

export const stripeTravelerRouter = createTRPCRouter({
  // Create or get Stripe customer
  createCustomer: protectedProcedure
    .input(createCustomerSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        // Use the helper function to get or create the customer
        const customer = await getOrCreateCustomer(ctx);

        return {
          success: true,
          customer,
        };
      } catch (error) {
        console.error("Error creating customer:", error);
        throw new Error("Failed to create Stripe customer");
      }
    }),

  // Get customer by user ID
  getCustomer: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const customer = await ctx.db
          .select()
          .from(stripeCustomers)
          .where(eq(stripeCustomers.appwriteUserId, ctx.session.user.$id as string))
          .limit(1);

        if (!customer.length || !customer[0]) {
          return null;
        }

        // Get fresh data from Stripe
        const stripeCustomer = await stripe.customers.retrieve(customer[0].stripeCustomerId);

        return stripeCustomer;
      } catch (error) {
        console.error("Error getting customer:", error);
        throw new Error("Failed to get Stripe customer");
      }
    }),

  // Create payment intent
  createPaymentIntent: protectedProcedure
    .input(createPaymentIntentSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        const customer = await getOrCreateCustomer(ctx);
        if (!customer.id) {
          throw new TRPCError({
            code: "BAD_REQUEST",
            message: "Customer not found",
          });
        }

        const customerId = customer.id;

        // Create payment intent
        const paymentIntent = await stripe.paymentIntents.create({
          amount: input.amount,
          currency: input.currency,
          customer: customerId,
          metadata: {
            appwriteUserId: ctx.session.user.$id as string,
          },
        });

        // Create ephemeral key
        const ephemeralKey = await stripe.ephemeralKeys.create({
          customer: customerId,
        }, {
          apiVersion: "2025-06-30.basil",
        });

        // Save payment intent to database
        await ctx.db
          .insert(stripePaymentIntents)
          .values({
            appwriteUserId: ctx.session.user.$id as string,
            stripePaymentIntentId: paymentIntent.id,
            stripeCustomerId: customerId,
            amount: input.amount,
            currency: input.currency,
            status: paymentIntent.status,
            paymentMethodTypes: paymentIntent.payment_method_types,
            metadata: paymentIntent.metadata,
          });

        return {
          success: true,
          paymentIntent: paymentIntent.client_secret,
          ephemeralKey: ephemeralKey.secret,
          customerId: customerId,
        };
      } catch (error) {
        console.error("Error creating payment intent:", error);
        throw new Error("Failed to create payment intent");
      }
    }),

  // Create checkout session
  createCheckoutSession: protectedProcedure
    .input(createCheckoutSessionSchema)
    .mutation(async ({ ctx, input }) => {
      try {
        // Get or create customer
        const customer = await getOrCreateCustomer(ctx);

        // Create checkout session
        const session = await stripe.checkout.sessions.create({
          customer: customer.id,
          payment_method_types: ["card"],
          line_items: [
            {
              price_data: {
                currency: input.currency,
                product_data: {
                  name: "TheDal Service",
                },
                unit_amount: input.amount,
              },
              quantity: 1,
            },
          ],
          mode: "payment",
          success_url: input.successUrl,
          cancel_url: input.cancelUrl,
          metadata: {
            appwriteUserId: ctx.session.user.$id as string,
            bookingTravelerId: input.bookingTravelerId || null,
            bookingCompanionId: input.bookingCompanionId || null,
          },
        } as Stripe.Checkout.SessionCreateParams);

        // Save session to database
        await ctx.db
          .insert(stripePaymentSessions)
          .values({
            appwriteUserId: ctx.session.user.$id as string,
            stripeSessionId: session.id,
            stripeCustomerId: customer.id,
            mode: session.mode || "payment",
            status: session.status || "open",
            amountTotal: session.amount_total || input.amount,
            currency: session.currency || input.currency,
            successUrl: input.successUrl,
            cancelUrl: input.cancelUrl,
            bookingTravelerId: input.bookingTravelerId,
            bookingCompanionId: input.bookingCompanionId,
            metadata: session.metadata,
          });

        return {
          success: true,
          sessionId: session.id,
          url: session.url,
        };
      } catch (error) {
        console.error("Error creating checkout session:", error);
        throw new Error("Failed to create checkout session");
      }
    }),

  // Get traveler's wallet balance
  getWalletBalance: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        // Get wallet balance from user profile
        const userProfile = await ctx.db
          .select({
            walletBalance: userProfiles.walletBalance,
          })
          .from(userProfiles)
          .where(eq(userProfiles.appwriteUserId, ctx.session.user.$id as string))
          .limit(1);

        if (userProfile.length === 0) {
          // If user profile does not exist, create it with the initial wallet balance
          await ctx.db
            .insert(userProfiles)
            .values({
              name: ctx.session.user.name as string || "",
              email: ctx.session.user.email as string || "",
              phone: ctx.session.user.phone as string || "",
              appwriteUserId: ctx.session.user.$id as string,
              walletBalance: 0,
              createdAt: new Date(),
              updatedAt: new Date(),
            });
          return {
            balance: 0,
            currency: "usd",
          };
        }

        return {
          balance: userProfile[0]?.walletBalance || 0,
          currency: "usd",
        };
      } catch (error) {
        console.error("Error getting wallet balance:", error);
        throw new Error("Failed to get wallet balance");
      }
    }),

  // Get traveler's payment history
  getPaymentHistory: protectedProcedure
    .query(async ({ ctx }) => {
      const payments = await ctx.db
        .select({
          id: walletTransactions.id,
          type: walletTransactions.type,
          stripePaymentIntentId: walletTransactions.stripePaymentIntentId,
          amount: walletTransactions.amount,
          currency: walletTransactions.currency,
          status: walletTransactions.status,
          description: walletTransactions.description,
          createdAt: walletTransactions.createdAt,
          metadata: walletTransactions.metadata,
        })
        .from(walletTransactions)
        .where(eq(walletTransactions.appwriteUserId, ctx.session.user.$id as string))
        .orderBy(desc(walletTransactions.createdAt));

      return payments || [];
    }),

  // Get traveler's customer status
  getCustomerStatus: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const customer = await ctx.db
          .select({
            stripeCustomerId: stripeCustomers.stripeCustomerId,
            name: stripeCustomers.name,
            email: stripeCustomers.email,
            phone: stripeCustomers.phone,
            createdAt: stripeCustomers.createdAt,
          })
          .from(stripeCustomers)
          .where(eq(stripeCustomers.appwriteUserId, ctx.session.user.$id as string))
          .limit(1);

        if (!customer.length) {
          return null;
        }

        return customer[0];
      } catch (error) {
        console.error("Error getting customer status:", error);
        throw new Error("Failed to get customer status");
      }
    }),
}); 