import { z } from "zod";
import { eq, and, or, ne, gte, lte, inArray, desc } from "drizzle-orm";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { 
  bookingTravelers, 
  bookingTravelerDestinations,
  bookingTravelerLanguages, 
  bookingCompanions,
  bookingCompanionDestinations,
  bookingCompanionLanguages,
  languages,
  airports,
  connectionRequests
} from "@repo/db";

// Input schemas for validation
const createTravelerSchema = z.object({
  name: z.string().min(1, "Name is required").min(3, "Name must be at least 3 characters long"),
  lastName: z.string().optional(),
  about: z.string().min(1, "About is required").min(5, "About must be at least 5 characters long"),
  gender: z.enum(["MALE", "FEMALE", "OTHERS"]),
  genderPreference: z.enum(["MALE", "FEMALE", "OTHERS"]),
  openToAllGenders: z.boolean().default(false),
  languageIds: z.array(z.string().uuid()).optional(),
});

const createFlightDetailsSchema = z.object({
  flightPNR: z.string().min(1, "Flight PNR is required"),
  flightTime: z.date(),
  flightEndTime: z.date(),
  timezone: z.string().min(1, "Timezone is required"),
  bookingDestinations: z.array(z.object({
    order: z.number(),
    airports: z.string(),
  })),
  searchField: z.string().min(1, "Search field is required"),
});

const createPhotoUploadsSchema = z.object({
  companionPhoto: z.string().min(1, "Companion photo is required"),
  passportPhoto: z.string().optional().nullable(),
  compensationValue: z.number().min(1, "Compensation value is required"),
});

const updateTravelerSchema = createTravelerSchema.partial().extend({
  id: z.string().uuid(),
});

const getTravelerSchema = z.object({
  id: z.string().uuid(),
});

// Add this new schema for companion searching travelers
const companionSearchTravelersSchema = z.object({
  bookingTravelerId: z.string(),
});

export const bookingTravelersRouter = createTRPCRouter({
  // Create a new traveler booking
  create: protectedProcedure
    .input(createTravelerSchema)
    .mutation(async ({ ctx, input }) => {
      const { languageIds, ...travelerData } = input;
      
      // Create the traveler booking
      const result = await ctx.db
        .insert(bookingTravelers)
        .values({
          appwriteUserId: ctx.session.user.$id as string,
          ...travelerData,
        })
        .returning();

      const newTraveler = result[0];
      if (!newTraveler) {
        throw new Error("Failed to create traveler booking");
      }

      // Associate languages if provided
      if (languageIds && languageIds.length > 0) {
        await ctx.db.insert(bookingTravelerLanguages).values(
          languageIds.map((languageId) => ({
            bookingTravelerId: newTraveler.id,
            languageId,
          }))
        );
      }

      // Fetch the created traveler with languages
      const travelerWithLanguages = await ctx.db
        .select({
          id: bookingTravelers.id,
          appwriteUserId: bookingTravelers.appwriteUserId,
          name: bookingTravelers.name,
          lastName: bookingTravelers.lastName,
          about: bookingTravelers.about,
          gender: bookingTravelers.gender,
          genderPreference: bookingTravelers.genderPreference,
          openToAllGenders: bookingTravelers.openToAllGenders,
          flightPNR: bookingTravelers.flightPNR,
          flightTime: bookingTravelers.flightTime,
          flightEndTime: bookingTravelers.flightEndTime,
          timezone: bookingTravelers.timezone,
          searchField: bookingTravelers.searchField,
          companionPhoto: bookingTravelers.companionPhoto,
          passportPhoto: bookingTravelers.passportPhoto,
          compensationValue: bookingTravelers.compensationValue,
          status: bookingTravelers.status,
          createdAt: bookingTravelers.createdAt,
          updatedAt: bookingTravelers.updatedAt,
          languages: languages,
        })
        .from(bookingTravelers)
        .leftJoin(bookingTravelerLanguages, eq(bookingTravelers.id, bookingTravelerLanguages.bookingTravelerId))
        .leftJoin(languages, eq(bookingTravelerLanguages.languageId, languages.id))
        .where(eq(bookingTravelers.id, newTraveler.id));

      return {
        ...newTraveler,
        languages: travelerWithLanguages
          .filter((row) => row.languages)
          .map((row) => row.languages),
      };
    }),

  // Update an existing traveler booking
  update: protectedProcedure
    .input(updateTravelerSchema)
    .mutation(async ({ ctx, input }) => {
      const { id, languageIds, ...updateData } = input;

      // Verify ownership
      const existingTraveler = await ctx.db
        .select()
        .from(bookingTravelers)
        .where(
          and(
            eq(bookingTravelers.id, id),
            eq(bookingTravelers.appwriteUserId, ctx.session.user.$id as string)
          )
        )
        .limit(1);

      if (!existingTraveler.length) {
        throw new Error("Traveler booking not found or access denied");
      }

      // Update the traveler booking
      const [updatedTraveler] = await ctx.db
        .update(bookingTravelers)
        .set({
          ...updateData,
          updatedAt: new Date(),
        })
        .where(eq(bookingTravelers.id, id))
        .returning();

      // Update language associations if provided
      if (languageIds !== undefined) {
        // Remove existing language associations
        await ctx.db
          .delete(bookingTravelerLanguages)
          .where(eq(bookingTravelerLanguages.bookingTravelerId, id));

        // Add new language associations
        if (languageIds.length > 0) {
          await ctx.db.insert(bookingTravelerLanguages).values(
            languageIds.map((languageId) => ({
              bookingTravelerId: id,
              languageId,
            }))
          );
        }
      }

      // Fetch the updated traveler with languages
      const travelerWithLanguages = await ctx.db
        .select({
          id: bookingTravelers.id,
          appwriteUserId: bookingTravelers.appwriteUserId,
          name: bookingTravelers.name,
          lastName: bookingTravelers.lastName,
          about: bookingTravelers.about,
          gender: bookingTravelers.gender,
          genderPreference: bookingTravelers.genderPreference,
          openToAllGenders: bookingTravelers.openToAllGenders,
          flightPNR: bookingTravelers.flightPNR,
          flightTime: bookingTravelers.flightTime,
          flightEndTime: bookingTravelers.flightEndTime,
          timezone: bookingTravelers.timezone,
          searchField: bookingTravelers.searchField,
          companionPhoto: bookingTravelers.companionPhoto,
          passportPhoto: bookingTravelers.passportPhoto,
          compensationValue: bookingTravelers.compensationValue,
          status: bookingTravelers.status,
          createdAt: bookingTravelers.createdAt,
          updatedAt: bookingTravelers.updatedAt,
          languages: languages,
        })
        .from(bookingTravelers)
        .leftJoin(bookingTravelerLanguages, eq(bookingTravelers.id, bookingTravelerLanguages.bookingTravelerId))
        .leftJoin(languages, eq(bookingTravelerLanguages.languageId, languages.id))
        .where(eq(bookingTravelers.id, id));

      return {
        ...updatedTraveler,
        languages: travelerWithLanguages
          .filter((row) => row.languages)
          .map((row) => row.languages),
      };
    }),

  // Create or update traveler booking (upsert functionality)
  upsert: protectedProcedure
    .input(createTravelerSchema.extend({
      id: z.string().uuid().optional(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, languageIds, ...travelerData } = input;

      if (id) {
        // Update existing traveler
        return await ctx.db.transaction(async (tx) => {
          // Verify ownership
          const existingTraveler = await tx
            .select()
            .from(bookingTravelers)
            .where(
              and(
                eq(bookingTravelers.id, id),
                eq(bookingTravelers.appwriteUserId, ctx.session.user.$id as string)
              )
            )
            .limit(1);

          if (!existingTraveler.length) {
            throw new Error("Traveler booking not found or access denied");
          }

          // Update the traveler booking
          const result = await tx
            .update(bookingTravelers)
            .set({
              ...travelerData,
              updatedAt: new Date(),
            })
            .where(eq(bookingTravelers.id, id))
            .returning();

          const updatedTraveler = result[0];
          if (!updatedTraveler) {
            throw new Error("Failed to update traveler booking");
          }

          // Update language associations
          await tx
            .delete(bookingTravelerLanguages)
            .where(eq(bookingTravelerLanguages.bookingTravelerId, id));

          if (languageIds && languageIds.length > 0) {
            await tx.insert(bookingTravelerLanguages).values(
              languageIds.map((languageId) => ({
                bookingTravelerId: id,
                languageId,
              }))
            );
          }

          return updatedTraveler;
        });
      } else {
        // Create new traveler
        return await ctx.db.transaction(async (tx) => {
          const result = await tx
            .insert(bookingTravelers)
            .values({
              appwriteUserId: ctx.session.user.$id as string,
              ...travelerData,
            })
            .returning();

          const newTraveler = result[0];
          if (!newTraveler) {
            throw new Error("Failed to create traveler booking");
          }

          // Associate languages if provided
          if (languageIds && languageIds.length > 0) {
            await tx.insert(bookingTravelerLanguages).values(
              languageIds.map((languageId) => ({
                bookingTravelerId: newTraveler.id,
                languageId,
              }))
            );
          }

          return newTraveler;
        });
      }
    }),

  upsertFlightDetails: protectedProcedure
    .input(createFlightDetailsSchema.extend({
      id: z.string(),
      flightPNR: z.string().min(1, "Flight PNR is required"),
      flightTime: z.date(),
      flightEndTime: z.date(),
      timezone: z.string().min(1, "Timezone is required"),
      bookingDestinations: z.array(z.object({
        order: z.number(),
        airports: z.string(),
      })),
      searchField: z.string().min(1, "Search field is required"),
    }))
    .mutation(async ({ ctx, input }) => {
      console.log('upsertFlightDetails', input);
      const { id, flightPNR, flightTime, flightEndTime, timezone, bookingDestinations, searchField } = input;

      if (!id) {
        throw new Error("Traveler booking ID is required");
      }

      const result = await ctx.db
        .update(bookingTravelers)
        .set({
          flightPNR,
          flightTime,
          flightEndTime,
          timezone,
          searchField,
        })
        .where(eq(bookingTravelers.id, id))
        .returning();

        // Now handle the relation: create destination records separately
        // First, remove existing destinations for this traveler booking
        await ctx.db
          .delete(bookingTravelerDestinations)
          .where(eq(bookingTravelerDestinations.bookingTravelerId, id));

        // Then, insert the new destinations
        if (bookingDestinations && bookingDestinations.length > 0) {
          await ctx.db.insert(bookingTravelerDestinations).values(
            bookingDestinations.map((dest) => ({
              bookingTravelerId: id,
              order: dest.order,
              airportId: dest.airports,
            }))
          );
        }

      return result[0];
    }),

  upsertPhotoUploads: protectedProcedure
    .input(createPhotoUploadsSchema.extend({
      id: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { id, ...photoUploadsData } = input;

      if (!id) {
        throw new Error("Traveler booking ID is required");
      }

      const result = await ctx.db
        .update(bookingTravelers)
        .set({
          ...photoUploadsData,
          status: 'ACTIVE',
          updatedAt: new Date(),
        })
        .where(eq(bookingTravelers.id, id))
        .returning();

      return result[0];
    }),

  // Get traveler booking by ID
  getById: protectedProcedure
    .input(getTravelerSchema)
    .query(async ({ ctx, input }) => {
      const bookingTraveler = await ctx.db
        .query.bookingTravelers.findFirst({
          where: eq(bookingTravelers.id, input.id),
          with: {
            languages: {
              with: {
                language: true,
              },
            },
            destinations: {
              with: {
                airport: true,
              },
            },
            connectionRequestsAsTraveler: true,
          },
        })

      return bookingTraveler ? {
        ...bookingTraveler,
        isEditable: bookingTraveler.status === 'ACTIVE' || bookingTraveler.status === 'DRAFT',
      } : null;
    }),

  // Get all traveler bookings for current user
  getAllByUser: protectedProcedure
    .query(async ({ ctx }) => {
      console.log('getAllByUser', ctx);
      const travelersWithLanguages = await ctx.db
        .select({
          id: bookingTravelers.id,
          appwriteUserId: bookingTravelers.appwriteUserId,
          name: bookingTravelers.name,
          lastName: bookingTravelers.lastName,
          about: bookingTravelers.about,
          gender: bookingTravelers.gender,
          genderPreference: bookingTravelers.genderPreference,
          openToAllGenders: bookingTravelers.openToAllGenders,
          flightPNR: bookingTravelers.flightPNR,
          flightTime: bookingTravelers.flightTime,
          flightEndTime: bookingTravelers.flightEndTime,
          timezone: bookingTravelers.timezone,
          searchField: bookingTravelers.searchField,
          companionPhoto: bookingTravelers.companionPhoto,
          passportPhoto: bookingTravelers.passportPhoto,
          compensationValue: bookingTravelers.compensationValue,
          status: bookingTravelers.status,
          createdAt: bookingTravelers.createdAt,
          updatedAt: bookingTravelers.updatedAt,
        })
        .from(bookingTravelers)
        .where(
          and(
            eq(bookingTravelers.appwriteUserId, ctx.session.user.$id as string),
            inArray(bookingTravelers.status, ["ACTIVE", "DRAFT", "CONFIRMED"])
          )
        )
        .orderBy(bookingTravelers.createdAt);

      console.log('travelersWithLanguages', travelersWithLanguages);
      return travelersWithLanguages;
    }),

  // Delete traveler booking
  delete: protectedProcedure
    .input(getTravelerSchema)
    .mutation(async ({ ctx, input }) => {
      // Verify ownership
      const existingTraveler = await ctx.db
        .select()
        .from(bookingTravelers)
        .where(
          and(
            eq(bookingTravelers.id, input.id),
            eq(bookingTravelers.appwriteUserId, ctx.session.user.$id as string)
          )
        )
        .limit(1);

      if (!existingTraveler.length) {
        throw new Error("Traveler booking not found or access denied");
      }

      // Delete the traveler booking (cascade will handle related records)
      await ctx.db
        .delete(bookingTravelers)
        .where(eq(bookingTravelers.id, input.id));

      return { success: true };
    }),

  // Companion searches for matching travelers based on their booking criteria
  travelersAvailableForBooking: protectedProcedure
    .input(companionSearchTravelersSchema)
    .query(async ({ ctx, input }) => {
      const { bookingTravelerId } = input;

      // First, get the companion's booking details
      const bookingTraveler = await ctx.db
        .select({
          id: bookingTravelers.id,
          appwriteUserId: bookingTravelers.appwriteUserId,
          name: bookingTravelers.name,
          lastName: bookingTravelers.lastName,
          about: bookingTravelers.about,
          gender: bookingTravelers.gender,
          genderPreference: bookingTravelers.genderPreference,
          openToAllGenders: bookingTravelers.openToAllGenders,
          flightPNR: bookingTravelers.flightPNR,
          flightTime: bookingTravelers.flightTime,
          flightEndTime: bookingTravelers.flightEndTime,
          timezone: bookingTravelers.timezone,
          searchField: bookingTravelers.searchField,
          companionPhoto: bookingTravelers.companionPhoto,
          passportPhoto: bookingTravelers.passportPhoto,
          compensationValue: bookingTravelers.compensationValue,
          status: bookingTravelers.status,
        })
        .from(bookingTravelers)
        .where(and(
          eq(bookingTravelers.id, bookingTravelerId),
          eq(bookingTravelers.appwriteUserId, ctx.session.user.$id as string)
        ))
        .limit(1);

      console.log('bookingTraveler', bookingTraveler);

      if (!bookingTraveler.length || !bookingTraveler[0]) {
        throw new Error("Companion booking not found");
      }

      const bookingTravelerData = bookingTraveler[0];

      if (bookingTravelerData.status === 'CONFIRMED') {
        const connectionRequestsAsCompanion = await ctx.db.query.connectionRequests.findFirst({
          with: {
            bookingCompanion: true,
          },
          where: and(
            eq(connectionRequests.bookingTravelerId, bookingTravelerData.id),
            eq(connectionRequests.status, "ACCEPTED")
          )
        })

        if (!connectionRequestsAsCompanion?.bookingCompanionId) {
          return {
            travelers: [],
            total: 0,
          };
        }
        
        const baseResults = await ctx.db.query.bookingCompanions.findMany({
          with: {
            languages: {
              with: {
                language: true,
              },
            },
            destinations: {
              with: {
                airport: true,
              },
            },
            connectionRequestsAsCompanion: {
              where: eq(connectionRequests.status, "ACCEPTED"),
            },
          },
          where: and(
            eq(bookingCompanions.id, connectionRequestsAsCompanion.bookingCompanionId),
            ne(bookingCompanions.appwriteUserId, ctx.session.user.$id as string),
            eq(bookingCompanions.status, "CONFIRMED")
          ),
          orderBy: desc(bookingCompanions.createdAt)
        })
        console.log('baseResults', baseResults);
        return {
          travelers: baseResults,
          total: baseResults.length,
        };
      }

      if (bookingTravelerData.status !== 'ACTIVE') {
        return {
          travelers: [],
          total: 0,
        };
      }

      // // Get companion's languages
      // const languages = await ctx.db
      //   .select({
      //     languageId: bookingTravelerLanguages.languageId,
      //   })
      //   .from(bookingCompanionLanguages)
      //   .where(eq(bookingCompanionLanguages.bookingCompanionId, bookingTravelerId));

      // const companionLanguageIds = languages.map(l => l.languageId);

      

      // // Build matching conditions
      // const conditions = [];

      // // Exclude current user's traveler bookings
      // conditions.push(ne(bookingCompanions.appwriteUserId, ctx.session.user.$id as string));

      // // Gender matching: companion's gender should match traveler's gender preference
      // conditions.push(
      //   or(
      //     eq(bookingCompanions.genderPreference, bookingTravelerData.gender),
      //     eq(bookingCompanions.openToAllGenders, true)
      //   )
      // );

      // // Gender preference matching: companion's gender preference should match traveler's gender
      // conditions.push(
      //   or(
      //     eq(bookingCompanions.genderPreference, bookingTravelers.gender),
      //     eq(bookingCompanions.openToAllGenders, true)
      //   )
      // );

      // // Flight time matching (within ±2 hours)
      // if (bookingCompanions.searchField) {
        
      //   conditions.push(
      //     eq(bookingCompanions.searchField, bookingCompanions.searchField)
      //   );
      // }

      // // Compensation matching (companion's compensation should be within traveler's range)
      // if (bookingCompanions.compensationValue) {
      //   // Assuming traveler's compensation is the maximum they're willing to pay
      //   // and companion's compensation is what they're asking for
      //   conditions.push(gte(bookingCompanions.compensationValue, bookingCompanions.compensationValue));
      // }

      // // Only active traveler bookings
      // conditions.push(eq(bookingCompanions.status, "ACTIVE"));

      // Apply conditions
      // Build the base query for matching travelers
      const baseResults = await ctx.db.query.bookingCompanions
        .findMany({
          with: {
            languages: {
              with: {
                language: true,
              },
            },
            destinations: {
              with: {
                airport: true,
              },
            },
            connectionRequestsAsCompanion: true,
          },
          where: and(
            eq(bookingCompanions.searchField, bookingTravelerData.searchField || ''),
            ne(bookingTravelers.appwriteUserId, ctx.session.user.$id as string),
            eq(bookingCompanions.status, "ACTIVE")
          ),
          orderBy: desc(bookingCompanions.createdAt)
        })
      // .where(and(...conditions));

      // Filter by matching destinations if companion has destinations
      let filteredResults = baseResults;

      // // Filter by matching languages if companion has languages
      // if (companionLanguageIds.length > 0) {
      //   const travelersWithMatchingLanguages = await ctx.db
      //     .select({
      //       bookingCompanionId: bookingCompanionLanguages.bookingCompanionId,
      //     })
      //     .from(bookingCompanionLanguages)
      //     .where(inArray(bookingCompanionLanguages.languageId, companionLanguageIds))
      //     .groupBy(bookingCompanionLanguages.bookingCompanionId);

      //   const matchingTravelerIds = travelersWithMatchingLanguages.map(t => t.bookingCompanionId);
      //   filteredResults = filteredResults.filter(companion => 
      //     matchingTravelerIds.includes(companion.id)
      //   );
      // }

      // Get detailed information for matching travelers
      // const detailedResults = await Promise.all(
      //   filteredResults.map(async (traveler) => {
      //     // Get traveler's languages
      //     const travelerLanguages = await ctx.db
      //       .select({
      //         language: languages,
      //       })
      //       .from(bookingTravelerLanguages)
      //       .leftJoin(languages, eq(bookingTravelerLanguages.languageId, languages.id))
      //       .where(eq(bookingTravelerLanguages.bookingTravelerId, traveler.id));

      //     // Get traveler's destinations
      //     const travelerDestinations = await ctx.db
      //       .select({
      //         airport: airports,
      //         order: bookingTravelerDestinations.order,
      //       })
      //       .from(bookingTravelerDestinations)
      //       .leftJoin(airports, eq(bookingTravelerDestinations.airportId, airports.id))
      //       .where(eq(bookingTravelerDestinations.bookingTravelerId, traveler.id))
      //       .orderBy(bookingTravelerDestinations.order);

      //     // Calculate match score based on various factors
      //     let matchScore = 0;
          
      //     // Gender match (20 points)
      //     if (traveler.genderPreference === companionData.gender || traveler.openToAllGenders) {
      //       matchScore += 20;
      //     }
      //     if (companionData.genderPreference === traveler.gender || companionData.openToAllGenders) {
      //       matchScore += 20;
      //     }

      //     // Flight time match (30 points)
      //     if (companionData.flightTime && traveler.flightTime) {
      //       const timeDiff = Math.abs(companionData.flightTime.getTime() - traveler.flightTime.getTime());
      //       const hoursDiff = timeDiff / (1000 * 60 * 60);
      //       if (hoursDiff <= 1) matchScore += 30;
      //       else if (hoursDiff <= 2) matchScore += 20;
      //       else if (hoursDiff <= 4) matchScore += 10;
      //     }

      //     // Destination match (25 points)
      //     const travelerAirportIds = travelerDestinations.map(d => d.airport?.id).filter(Boolean);
      //     const destinationMatches = companionAirportIds.filter(id => 
      //       travelerAirportIds.includes(id)
      //     ).length;
      //     if (destinationMatches > 0) {
      //       matchScore += Math.min(25, (destinationMatches / companionAirportIds.length) * 25);
      //     }

      //     // Language match (15 points)
      //     const travelerLanguageIds = travelerLanguages.map(l => l.language?.id).filter(Boolean);
      //     const languageMatches = companionLanguageIds.filter(id => 
      //       travelerLanguageIds.includes(id)
      //     ).length;
      //     if (languageMatches > 0) {
      //       matchScore += Math.min(15, (languageMatches / companionLanguageIds.length) * 15);
      //     }

      //     // Compensation match (10 points)
      //     if (companionData.compensationValue && traveler.compensationValue) {
      //       if (traveler.compensationValue >= companionData.compensationValue) {
      //         matchScore += 10;
      //       }
      //     }

      //     return {
      //       ...traveler,
      //       languages: travelerLanguages.map(tl => tl.language).filter(Boolean),
      //       destinations: travelerDestinations.map(td => ({
      //         airport: td.airport,
      //         order: td.order,
      //       })).filter(td => td.airport),
      //       matchScore: Math.round(matchScore),
      //       matchPercentage: Math.round((matchScore / 100) * 100),
      //     };
      //   })
      // );

      // // Sort by match score (highest first)
      // detailedResults.sort((a, b) => b.matchScore - a.matchScore);

      return {
        travelers: baseResults, // filteredResults,
        total: baseResults.length,
      };
    }),
}); 