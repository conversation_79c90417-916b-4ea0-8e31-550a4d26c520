import { z } from 'zod';
import { createTRPCRouter, protectedProcedure } from '../trpc';
import { streamChatService } from '../services/stream-chat.service';
import { TRPCError } from '@trpc/server';

export const chatRouter = createTRPCRouter({
  /**
   * Create or update a GetStream user
   */
  createUser: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1),
        image: z.string().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const userId = ctx.session.user.$id as string;
        await streamChatService.createUser({
          id: userId,
          name: input.name,
          image: input.image,
        });
        
        return { success: true };
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create GetStream user',
        });
      }
    }),

  /**
   * Update GetStream user profile (name and/or image)
   */
  updateUserProfile: protectedProcedure
    .input(
      z.object({
        name: z.string().min(1).optional(),
        image: z.string().url().optional(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const userId = ctx.session.user.$id as string;
        await streamChatService.updateUserProfile(
          userId,
          input.name,
          input.image
        );
        
        return { success: true };
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to update GetStream user profile',
        });
      }
    }),

  /**
   * Generate a user token for client-side authentication
   */
  getToken: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const userId = ctx.session.user.$id as string;

        const token = await streamChatService.generateToken({
          userId: userId,
          name: ctx.session.user.name as string,
          image: ctx.session.user.image as string,
        });
        
        return { token };
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to generate GetStream token',
        });
      }
    }),

  /**
   * Create a 1-to-1 channel for an accepted connection request
   */
  createChannel: protectedProcedure
    .input(
      z.object({
        connectionRequestId: z.string().uuid(),
        otherUserId: z.string(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const currentUserId = ctx.session.user.$id as string;
        
        // Create channel between current user and other user
        const channel = await streamChatService.createChannel(
          currentUserId,
          input.otherUserId,
          `connection-${input.connectionRequestId}`
        );
        
        return channel;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to create chat channel',
        });
      }
    }),

  /**
   * Get a specific channel by connection request ID
   */
  getChannel: protectedProcedure
    .input(
      z.object({
        connectionRequestId: z.string().uuid(),
      })
    )
    .query(async ({ input, ctx }) => {
      try {
        const currentUserId = ctx.session.user.$id as string;
        const channelId = `connection-${input.connectionRequestId}`;
        
        const channel = await streamChatService.getChannel(channelId);
        
        if (!channel) {
          // If the channel does not exist, create it on the fly
          const connectionRequest = await ctx.db.query.connectionRequests.findFirst({
            where: (cr, { eq }) => eq(cr.id, input.connectionRequestId),
          });

          if (!connectionRequest) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: 'Connection request not found',
            });
          }

          // Determine the other user in the connection request
          // The current user can be either the traveler or the companion
          let otherUserId: string | null = null;
          if (connectionRequest.bookingTravelerId && connectionRequest.bookingCompanionId) {
            // Fetch both user IDs from the booking tables
            const [traveler, companion] = await Promise.all([
              ctx.db.query.bookingTravelers.findFirst({
                where: (bt, { eq }) => eq(bt.id, connectionRequest.bookingTravelerId || ''),
              }),
              ctx.db.query.bookingCompanions.findFirst({
                where: (bc, { eq }) => eq(bc.id, connectionRequest.bookingCompanionId || ''),
              }),
            ]);
            if (!traveler || !companion) {
              throw new TRPCError({
                code: 'NOT_FOUND',
                message: 'Traveler or companion not found for this connection request',
              });
            }
            if (traveler.appwriteUserId === currentUserId) {
              otherUserId = companion.appwriteUserId;
            } else if (companion.appwriteUserId === currentUserId) {
              otherUserId = traveler.appwriteUserId;
            } else {
              throw new TRPCError({
                code: 'FORBIDDEN',
                message: 'You are not a participant in this connection request',
              });
            }
          } else {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: 'Invalid connection request data',
            });
          }
          if (!otherUserId) {
            throw new TRPCError({
              code: 'NOT_FOUND',
              message: 'Other user not found',
            });
          }

          // Create the channel
          const newChannel = await streamChatService.createChannel(
            currentUserId,
            otherUserId,
            channelId
          );

          return newChannel;
        }
        
        // Verify user is a member of this channel
        // if (!channel.members.includes(currentUserId)) {
        //   throw new TRPCError({
        //     code: 'FORBIDDEN',
        //     message: 'Access denied to this chat channel',
        //   });
        // }
        
        return channel;
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get chat channel',
        });
      }
    }),

  /**
   * Get all chat channels for the current user
   */
  getUserChannels: protectedProcedure
    .query(async ({ ctx }) => {
      try {
        const currentUserId = ctx.session.user.$id as string;
        const channels = await streamChatService.getUserChannels(currentUserId);
        
        return channels;
      } catch (error) {
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to get user channels',
        });
      }
    }),

  /**
   * Send a message to a channel
   */
  sendMessage: protectedProcedure
    .input(
      z.object({
        connectionRequestId: z.string().uuid(),
        message: z.string().min(1),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const currentUserId = ctx.session.user.$id as string;
        const channelId = `connection-${input.connectionRequestId}`;
        
        // Verify channel exists and user is a member
        const channel = await streamChatService.getChannel(channelId);
        if (!channel || !channel.members.includes(currentUserId)) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Access denied to this chat channel',
          });
        }
        
        await streamChatService.sendMessage(channelId, currentUserId, input.message);
        
        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to send message',
        });
      }
    }),

  /**
   * Delete a chat channel (when connection is removed)
   */
  deleteChannel: protectedProcedure
    .input(
      z.object({
        connectionRequestId: z.string().uuid(),
      })
    )
    .mutation(async ({ input, ctx }) => {
      try {
        const currentUserId = ctx.session.user.$id as string;
        const channelId = `connection-${input.connectionRequestId}`;
        
        // Verify channel exists and user is a member
        const channel = await streamChatService.getChannel(channelId);
        if (!channel || !channel.members.includes(currentUserId)) {
          throw new TRPCError({
            code: 'FORBIDDEN',
            message: 'Access denied to this chat channel',
          });
        }
        
        await streamChatService.deleteChannel(channelId);
        
        return { success: true };
      } catch (error) {
        if (error instanceof TRPCError) {
          throw error;
        }
        throw new TRPCError({
          code: 'INTERNAL_SERVER_ERROR',
          message: 'Failed to delete chat channel',
        });
      }
    }),
}); 