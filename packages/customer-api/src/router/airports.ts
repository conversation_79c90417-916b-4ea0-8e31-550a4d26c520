import { z } from "zod";
import { createTRPCRouter, protectedProcedure } from "../trpc";
import { airports } from "@repo/db";

export const airportRouter = createTRPCRouter({
  getAll: protectedProcedure.query(async ({ ctx }) => {
    const airportsData = await ctx.db
      .select({
        id: airports.id,
        name: airports.name,
        shortCode: airports.shortCode,
        city: airports.city,
        country: airports.country,
        state: airports.state,
        lat: airports.lat,
        lon: airports.lon,
        timezone: airports.timezone,
        type: airports.type,
        icao: airports.icao,
      })
      .from(airports)
      .orderBy(airports.name);

    return airportsData.map((airport) => ({
      ...airport,
      airportLocation: `${airport.city}, ${airport.state}`,
    }));
  }),
}); 