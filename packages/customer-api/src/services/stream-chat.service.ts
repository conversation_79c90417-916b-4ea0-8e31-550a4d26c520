import { StreamChat } from 'stream-chat';
import { env } from '../../env';
import { v4 as uuidv4 } from 'uuid';
import { db } from '@repo/db/client';

export interface StreamUser {
  id: string;
  name: string;
  image?: string;
}

export interface StreamChannel {
  id: string;
  type: string;
  members: string[];
}

class StreamChatService {
  private client: StreamChat;

  constructor() {
    this.client = StreamChat.getInstance(env.STREAM_API_KEY, env.STREAM_API_SECRET);
  }

  /**
   * Create or update a GetStream user
   */
  async createUser(user: StreamUser): Promise<void> {
    try {
      await this.client.upsertUser({
        id: user.id,
        name: user.name,
        image: user.image,
      });
    } catch (error) {
      console.error('Error creating GetStream user:', error);
      throw new Error('Failed to create GetStream user');
    }
  }

  /**
   * Update user profile in GetStream (name and/or image)
   * This is a more specific method for profile updates
   */
  async updateUserProfile(userId: string, name?: string, image?: string): Promise<void> {
    try {
      const updateData: any = { id: userId };
      
      if (name !== undefined) {
        updateData.name = name;
      }
      
      if (image !== undefined) {
        updateData.image = image;
      }
      
      await this.client.upsertUser(updateData);
    } catch (error) {
      console.error('Error updating GetStream user profile:', error);
      throw new Error('Failed to update GetStream user profile');
    }
  }

  /**
   * Generate a user token for client-side authentication
   */
  async generateToken({ userId, name, image }: { userId: string, name: string, image: string }): Promise<string> {
    try {
      const user = await this.client.upsertUser({
        id: userId,
        name: name,
        image: image,
      });
      return this.client.createToken(userId);
    } catch (error) {
      console.error('Error generating GetStream token:', error);
      throw new Error('Failed to generate GetStream token');
    }
  }

  /**
   * Create a 1-to-1 channel between two users
   */
  async createChannel(user1Id: string, user2Id: string, channelId?: string): Promise<StreamChannel> {
    try {
      const finalChannelId = channelId || uuidv4();
      const channel = this.client.channel('messaging', finalChannelId, {
        members: [user1Id, user2Id],
        created_by_id: user1Id,
      });

      await channel.create({
        members: {
          [user1Id]: {
            role: 'admin',
          },
          [user2Id]: {
            role: 'admin',
          },
        },
      });
      
      return {
        id: finalChannelId,
        type: channel.type,
        members: [user1Id, user2Id],
      };
    } catch (error) {
      console.error('Error creating GetStream channel:', error);
      throw new Error('Failed to create GetStream channel');
    }
  }

  /**
   * Get a channel by ID
   */
  async getChannel(channelId: string): Promise<StreamChannel | null> {
    try {
      const channel = this.client.channel('messaging', channelId);
      const state = await channel.watch();
      
      return {
        id: state.channel.id,
        type: state.channel.type,
        members: Object.keys(state.members),
      };
    } catch (error) {
      console.error('Error getting GetStream channel:', error);
      return null;
    }
  }

  /**
   * Get all channels for a user
   */
  async getUserChannels(userId: string): Promise<StreamChannel[]> {
    try {
      const filter = { members: { $in: [userId] } };
      const sort = [{ last_message_at: -1 }];
      
      const result = await this.client.queryChannels(filter, sort);
      
      return result.map(channel => ({
        id: channel.id || '',
        type: channel.type,
        members: Object.keys(channel.state.members),
      }));
    } catch (error) {
      console.error('Error getting user channels:', error);
      throw new Error('Failed to get user channels');
    }
  }

  /**
   * Delete a channel
   */
  async deleteChannel(channelId: string): Promise<void> {
    try {
      const channel = this.client.channel('messaging', channelId);
      await channel.delete();
    } catch (error) {
      console.error('Error deleting GetStream channel:', error);
      throw new Error('Failed to delete GetStream channel');
    }
  }

  /**
   * Add a message to a channel
   */
  async sendMessage(channelId: string, userId: string, message: string): Promise<void> {
    try {
      const channel = this.client.channel('messaging', channelId);
      await channel.sendMessage({
        text: message,
        user_id: userId,
      });
    } catch (error) {
      console.error('Error sending message:', error);
      throw new Error('Failed to send message');
    }
  }
}

export const streamChatService = new StreamChatService(); 