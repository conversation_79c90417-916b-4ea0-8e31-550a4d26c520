
import { Client, Databases, Account } from "node-appwrite";
import { env } from "../../env";

const createSessionClient = (session: string) => {
  const client: Client = new Client()
    .setEndpoint(env.APPWRITE_ENDPOINT)
    .setProject(env.APPWRITE_PROJECT_ID);

  if (session) {
    client.setJWT(session);
  }

  return {
    get account(): Account {
      return new Account(client);
    },

    get databases(): Databases {
      return new Databases(client);
    },
  };
};

export { createSessionClient };