# Customer API

This package provides a tRPC-based API for customer-facing operations in the TheDal application.

## Available Routers

### Airports
- `getAll` - Get all airports with location information

### Languages
- `getAll` - Get all available languages

### Booking Companions

The booking companions router provides comprehensive APIs for managing companion bookings in the traveler details form.

#### Endpoints

##### Create Companion Booking
```typescript
POST /api/trpc/bookingCompanions.create
```
Creates a new companion booking with the provided details.

**Input Schema:**
```typescript
{
  name: string; // Required, min 3 characters
  lastName?: string; // Optional
  about: string; // Required, min 5 characters
  gender: "MALE" | "FEMALE" | "OTHERS"; // Required
  typeOfTraveler: "SOLO" | "FAMILY" | "GROUP"; // Required
  genderPreference: "MALE" | "FEMALE" | "OTHERS"; // Required
  openToAllGender: boolean; // Default: false
  languageIds?: string[]; // Optional array of language UUIDs
}
```

##### Update Companion Booking
```typescript
POST /api/trpc/bookingCompanions.update
```
Updates an existing companion booking by ID.

**Input Schema:**
```typescript
{
  id: string; // Required UUID
  name?: string; // Optional, min 3 characters
  lastName?: string; // Optional
  about?: string; // Optional, min 5 characters
  gender?: "MALE" | "FEMALE" | "OTHERS"; // Optional
  typeOfTraveler?: "SOLO" | "FAMILY" | "GROUP"; // Optional
  genderPreference?: "MALE" | "FEMALE" | "OTHERS"; // Optional
  openToAllGender?: boolean; // Optional
  languageIds?: string[]; // Optional array of language UUIDs
}
```

##### Upsert Companion Booking
```typescript
POST /api/trpc/bookingCompanions.upsert
```
Creates a new companion booking or updates an existing one if an ID is provided.

**Input Schema:**
```typescript
{
  id?: string; // Optional UUID - if provided, updates existing; if not, creates new
  name: string; // Required, min 3 characters
  lastName?: string; // Optional
  about: string; // Required, min 5 characters
  gender: "MALE" | "FEMALE" | "OTHERS"; // Required
  typeOfTraveler: "SOLO" | "FAMILY" | "GROUP"; // Required
  genderPreference: "MALE" | "FEMALE" | "OTHERS"; // Required
  openToAllGender: boolean; // Default: false
  languageIds?: string[]; // Optional array of language UUIDs
}
```

##### Get Companion Booking by ID
```typescript
GET /api/trpc/bookingCompanions.getById
```
Retrieves a specific companion booking by ID.

**Input Schema:**
```typescript
{
  id: string; // Required UUID
}
```

**Response includes:**
- All companion booking fields
- Associated languages array

##### Get Companion Booking by Current User
```typescript
GET /api/trpc/bookingCompanions.getByUser
```
Retrieves the companion booking for the currently authenticated user.

**Response includes:**
- All companion booking fields
- Associated languages array
- Returns `null` if no booking exists

##### Get All Companion Bookings by User
```typescript
GET /api/trpc/bookingCompanions.getAllByUser
```
Retrieves all companion bookings for the currently authenticated user.

**Response includes:**
- Array of companion bookings with all fields
- Associated languages for each booking

##### Delete Companion Booking
```typescript
POST /api/trpc/bookingCompanions.delete
```
Deletes a companion booking by ID.

**Input Schema:**
```typescript
{
  id: string; // Required UUID
}
```

**Response:**
```typescript
{
  success: boolean;
}
```

## Authentication

All endpoints require authentication. The API uses the session context to identify the current user and ensure data access control.

## Database Schema

The booking companions API works with the following database tables:

- `booking_companions` - Main companion booking data
- `booking_languages` - Many-to-many relationship between companions and languages
- `languages` - Available languages reference table

## Error Handling

The API includes comprehensive error handling:

- **Validation Errors**: Input validation using Zod schemas
- **Authorization Errors**: Ensures users can only access their own data
- **Database Errors**: Proper error handling for database operations
- **Not Found Errors**: Returns appropriate errors when resources don't exist

## Usage Examples

### Creating a Companion Booking
```typescript
const result = await trpc.bookingCompanions.create.mutate({
  name: "John Doe",
  lastName: "Smith",
  about: "I'm a friendly traveler looking for companionship",
  gender: "MALE",
  typeOfTraveler: "SOLO",
  genderPreference: "FEMALE",
  openToAllGender: false,
  languageIds: ["lang-uuid-1", "lang-uuid-2"]
});
```

### Updating a Companion Booking
```typescript
const result = await trpc.bookingCompanions.update.mutate({
  id: "companion-uuid",
  about: "Updated description about myself",
  openToAllGender: true
});
```

### Getting User's Companion Booking
```typescript
const companion = await trpc.bookingCompanions.getByUser.query();
if (companion) {
  console.log("User has companion booking:", companion.name);
  console.log("Languages:", companion.languages);
}
```

## Integration with Mobile App

This API is designed to work seamlessly with the mobile app's traveler details form (`apps/mobile/src/app/form/companion-required/traveler-details.tsx`). The API endpoints match the data structure and validation requirements of the form. 