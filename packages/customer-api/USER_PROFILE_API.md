# User Profile API

This API provides endpoints for managing user profiles in the TheDal application.

## Endpoints

### GET /api/trpc/userProfiles.get

Fetches the current user's profile. If no profile exists, it creates one with default empty values.

**Authentication:** Required (protected endpoint)

**Response:**
```typescript
{
  id: string;
  appwriteUserId: string;
  name: string;
  lastName: string | null;
  email: string | null;
  phone: string | null;
  about: string | null;
  typeOfTraveller: "SOLO" | "FAMILY" | "GROUP" | null;
  gender: "MALE" | "FEMALE" | "OTHERS" | null;
  genderPreference: "MALE" | "FEMALE" | "OTHERS" | null;
  openToAllGenders: boolean;
  bookingFor: "Self" | "Other";
  typeOfUser: "TRAVELLER" | "COMPANION";
  userProfile: string | null;
  userProfileUrl: string | null;
  createdAt: Date;
  updatedAt: Date;
  languages: Array<{
    id: string;
    name: string;
  }>;
}
```

### POST /api/trpc/userProfiles.update

Updates the current user's profile. If no profile exists, it creates one.

**Authentication:** Required (protected endpoint)

**Request Body:**
```typescript
{
  name: string; // Required, min 3 characters
  lastName?: string;
  email?: string; // Must be valid email format
  phone?: string;
  about: string; // Required, min 5 characters
  typeOfTraveller: "SOLO" | "FAMILY" | "GROUP";
  gender: "MALE" | "FEMALE" | "OTHERS";
  genderPreference?: "MALE" | "FEMALE" | "OTHERS";
  openToAllGenders?: boolean; // Default: false
  bookingFor?: "Self" | "Other"; // Default: "Self"
  typeOfUser: "TRAVELLER" | "COMPANION";
  languages?: string[]; // Array of language UUIDs
  userProfile?: string;
  userProfileUrl?: string;
}
```

**Response:** Same as GET endpoint

## Usage Examples

### Fetch User Profile
```typescript
import { api } from '@/lib/api';

const profile = await api.userProfiles.get.query();
console.log(profile);
```

### Update User Profile
```typescript
import { api } from '@/lib/api';

const updatedProfile = await api.userProfiles.update.mutate({
  name: "John Doe",
  lastName: "Smith",
  email: "<EMAIL>",
  phone: "+1234567890",
  about: "I love traveling and meeting new people",
  typeOfTraveller: "SOLO",
  gender: "MALE",
  genderPreference: "FEMALE",
  openToAllGenders: false,
  bookingFor: "Self",
  typeOfUser: "TRAVELLER",
  languages: ["language-uuid-1", "language-uuid-2"],
  userProfile: "profile-image-id",
  userProfileUrl: "https://example.com/profile.jpg"
});
```

## Database Schema

The API uses the following database tables:

- `user_profiles`: Main profile information
- `user_profile_languages`: Many-to-many relationship between profiles and languages
- `languages`: Available languages (referenced from app-config-schema)

## Error Handling

The API returns appropriate error messages for:
- Validation errors (invalid email, required fields, etc.)
- Authentication errors (unauthorized access)
- Database errors (creation/update failures)

## Notes

- The API automatically creates a profile with default values if one doesn't exist
- Language relationships are managed automatically (old languages are deleted and new ones are added)
- All timestamps are automatically managed
- The API uses the authenticated user's Appwrite ID to identify profiles 