# Profile Picture API

This API provides endpoints for managing user profile pictures in the TheDal application.

## Endpoints

### POST /api/trpc/userProfiles.updateProfilePicture

Updates only the profile picture for the current user. This is a lightweight endpoint specifically designed for profile picture updates.

**Authentication:** Required (protected endpoint)

**Request Body:**
```typescript
{
  userProfile: string; // Required - File ID from storage (e.g., Appwrite file ID)
  userProfileUrl: string; // Required - Public URL to access the profile picture
}
```

**Response:**
```typescript
{
  id: string;
  appwriteUserId: string;
  name: string;
  lastName: string | null;
  email: string | null;
  phone: string | null;
  about: string | null;
  typeOfTraveller: "SOLO" | "FAMILY" | "GROUP" | null;
  gender: "MALE" | "FEMALE" | "OTHERS" | null;
  genderPreference: "MALE" | "FEMALE" | "OTHERS" | null;
  openToAllGenders: boolean;
  bookingFor: "SELF" | "FATHER" | "MOTHER" | "RELATIVE";
  userProfile: string | null;
  userProfileUrl: string | null;
  createdAt: Date;
  updatedAt: Date;
  languages: Array<{
    id: string;
    name: string;
  }>;
}
```

## Stream Chat Integration

When a profile picture is updated, the system automatically:

1. **Updates the database profile** with the new image information
2. **Upserts the Stream chat user** with the updated name and image
3. **Ensures consistency** between the app profile and chat profile

This integration ensures that when users update their profile pictures, their chat avatar and name are automatically updated in all their conversations.

### Stream User Update Process

The profile picture update triggers the following Stream operations:

```typescript
// Automatically called when profile picture is updated
await streamChatService.updateUserProfile(
  userId,
  profileName,
  newProfileImageUrl
);
```

This updates the user's appearance in:
- Chat list avatars
- Message sender information
- User search results
- All active conversations

## Usage Examples

### Update Profile Picture (Mobile App)
```typescript
import { trpc } from '@/lib/api';

const updateProfilePictureMutation = trpc.userProfiles.updateProfilePicture.mutationOptions();

// After uploading file to storage
const result = await updateProfilePictureMutation.mutateAsync({
  userProfile: 'uploaded_file_id',
  userProfileUrl: 'https://storage.example.com/profile.jpg'
});

// The Stream chat user is automatically updated
// No additional calls needed
```

### Update Profile Picture (Web App)
```typescript
import { trpc } from '@/lib/api';

const updateProfilePicture = trpc.userProfiles.updateProfilePicture.useMutation();

const handleProfilePictureUpdate = async (fileId: string, fileUrl: string) => {
  try {
    const result = await updateProfilePicture.mutateAsync({
      userProfile: fileId,
      userProfileUrl: fileUrl,
    });
    console.log('Profile picture updated:', result);
    // Stream chat user is automatically updated
  } catch (error) {
    console.error('Error updating profile picture:', error);
  }
};
```

### Manual Stream User Update (if needed)
```typescript
import { trpc } from '@/lib/api';

const updateStreamUser = trpc.chat.updateUserProfile.useMutation();

// Only use this if you need to update Stream user independently
const handleStreamUserUpdate = async (name?: string, image?: string) => {
  try {
    await updateStreamUser.mutateAsync({
      name,
      image,
    });
  } catch (error) {
    console.error('Error updating Stream user:', error);
  }
};
```

## Complete Flow Example

Here's a complete example of how to handle profile picture upload and update:

```typescript
import { trpc } from '@/lib/api';
import { storage } from '@/lib/appwrite'; // Your storage service

const updateProfilePictureMutation = trpc.userProfiles.updateProfilePicture.mutationOptions();

const uploadAndUpdateProfilePicture = async (imageFile: File) => {
  try {
    // 1. Upload file to storage
    const uploadedFile = await storage.createFile(
      'bucket_id',
      'unique_file_id',
      imageFile,
      ['read("any")']
    );

    // 2. Get the public URL
    const fileUrl = storage.getFileView('bucket_id', uploadedFile.$id);

    // 3. Update profile picture in database AND Stream chat user
    const updatedProfile = await updateProfilePictureMutation.mutateAsync({
      userProfile: uploadedFile.$id,
      userProfileUrl: fileUrl.href,
    });

    // 4. Stream chat user is automatically updated
    // No additional steps needed

    return {
      success: true,
      profile: updatedProfile,
      fileId: uploadedFile.$id,
      fileUrl: fileUrl.href,
    };
  } catch (error) {
    console.error('Error in profile picture update:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
};
```

## Error Handling

The endpoint will return appropriate error messages for:

- **Authentication errors**: User not authenticated
- **Validation errors**: Invalid file ID or URL format
- **Database errors**: Issues with profile creation or update
- **Stream errors**: Issues with chat user update (logged but doesn't break profile update)
- **Missing profile**: Creates a new profile if none exists

## Benefits of This Endpoint

1. **Lightweight**: Only updates profile picture fields, not the entire profile
2. **Fast**: Minimal database operations
3. **Focused**: Specific purpose for profile picture updates
4. **Consistent**: Returns the same profile structure as other endpoints
5. **Safe**: Creates profile if it doesn't exist
6. **Integrated**: Automatically updates Stream chat user for consistency
7. **Resilient**: Stream errors don't break profile updates

## Migration from Full Profile Update

If you're currently using the full `userProfiles.update` mutation for profile picture updates, you can migrate to this new endpoint for better performance:

**Before:**
```typescript
await trpc.userProfiles.update.mutate({
  name: currentProfile.name,
  email: currentProfile.email,
  // ... all other fields
  userProfile: newFileId,
  userProfileUrl: newFileUrl,
});
```

**After:**
```typescript
await trpc.userProfiles.updateProfilePicture.mutate({
  userProfile: newFileId,
  userProfileUrl: newFileUrl,
});
```

## Stream Chat User Management

The system provides several ways to manage Stream chat users:

### Automatic Updates
- Profile picture updates automatically update Stream user
- Profile name updates automatically update Stream user
- Full profile updates automatically update Stream user

### Manual Updates
```typescript
// Update Stream user independently
await trpc.chat.updateUserProfile.mutate({
  name: 'New Name',
  image: 'https://example.com/new-image.jpg'
});

// Create Stream user
await trpc.chat.createUser.mutate({
  name: 'User Name',
  image: 'https://example.com/image.jpg'
});
```

### Error Handling for Stream Updates
Stream user updates are designed to be non-blocking:
- If Stream update fails, the profile update still succeeds
- Stream errors are logged for debugging
- Users can still use the app even if Stream is temporarily unavailable 