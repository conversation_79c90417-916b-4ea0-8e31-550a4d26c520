{"name": "@repo/customer-api", "version": "0.0.1", "private": true, "license": "MIT", "type": "module", "exports": {".": {"types": "./dist/index.d.ts", "default": "./src/index.ts"}, "./env": "./env.ts", "./types": "./src/types.ts"}, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "typecheck": "tsc --noEmit --emitDeclarationOnly false"}, "prettier": "@repo/prettier-config", "dependencies": {"@repo/db": "workspace:*", "@t3-oss/env-nextjs": "catalog:", "@trpc/server": "catalog:", "date-fns": "catalog:", "node-appwrite": "catalog:", "stream-chat": "^9.13.0", "stripe": "catalog:", "superjson": "2.2.2", "uuid": "^11.1.0", "zod": "catalog:"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/tsconfig": "workspace:*", "@types/node": "catalog:", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}}