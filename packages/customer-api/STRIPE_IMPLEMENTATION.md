# Stripe Implementation for TheDal

This document outlines the comprehensive Stripe integration for TheDal, including database schemas, tRPC routers, and implementation details for both companions and travellers.

## Overview

The Stripe implementation provides separate functionality for:
- **Travellers**: Customer management, payment intents, checkout sessions, and wallet management
- **Companions**: Connect account management, transfers, and payment receiving
- **Webhooks**: Event processing for payment confirmations and status updates

## Database Schema

### Core Stripe Tables

#### `stripe_customers`
Stores Stripe customer information for travellers.

**Key Fields:**
- `id` (UUID, Primary Key)
- `user_id` (Text, Foreign Key to `user.id`, Unique)
- `stripe_customer_id` (Text, Unique)
- `name`, `email`, `phone` (Customer details)
- `metadata` (JSONB, Additional Stripe metadata)
- `created_at`, `updated_at` (Timestamps)

#### `stripe_connect_accounts`
Stores Stripe Connect account information for companions.

**Key Fields:**
- `id` (UUID, Primary Key)
- `user_id` (Text, Foreign Key to `user.id`, Unique)
- `stripe_account_id` (Text, Unique)
- `account_type` (Text, Default: "express")
- `email`, `business_profile` (Account details)
- `capabilities`, `requirements` (J<PERSON>NB, Stripe account status)
- `charges_enabled`, `payouts_enabled`, `details_submitted` (Boolean status)
- `metadata` (JSONB, Additional Stripe metadata)
- `created_at`, `updated_at` (Timestamps)

#### `stripe_payment_intents`
Tracks payment intents for wallet top-ups and other payments.

**Key Fields:**
- `id` (UUID, Primary Key)
- `user_id` (Text, Foreign Key to `user.id`)
- `stripe_payment_intent_id` (Text, Unique)
- `stripe_customer_id` (Text, Foreign Key to `stripe_customers.stripe_customer_id`)
- `amount` (Integer, in cents)
- `currency` (Text, Default: "usd")
- `status` (Text, Payment intent status)
- `payment_method_types` (JSONB, Allowed payment methods)
- `last_payment_error` (JSONB, Error details)
- `metadata` (JSONB, Additional metadata)
- `created_at`, `updated_at` (Timestamps)

#### `stripe_transfers`
Tracks transfers to companions for payments.

**Key Fields:**
- `id` (UUID, Primary Key)
- `user_id` (Text, Foreign Key to `user.id`)
- `stripe_transfer_id` (Text, Unique)
- `stripe_connect_account_id` (Text, Foreign Key to `stripe_connect_accounts.stripe_account_id`)
- `amount` (Integer, in cents)
- `currency` (Text, Default: "usd")
- `status` (Text, Transfer status)
- `booking_traveler_id`, `booking_companion_id` (UUID, Related bookings)
- `destination`, `transfer_group` (Transfer details)
- `metadata` (JSONB, Additional metadata)
- `created_at`, `updated_at` (Timestamps)

#### `stripe_payment_sessions`
Tracks checkout sessions for payments.

**Key Fields:**
- `id` (UUID, Primary Key)
- `user_id` (Text, Foreign Key to `user.id`)
- `stripe_session_id` (Text, Unique)
- `stripe_customer_id` (Text, Foreign Key to `stripe_customers.stripe_customer_id`)
- `mode` (Text, Session mode: payment, setup, subscription)
- `status` (Text, Session status)
- `amount_total` (Integer, in cents)
- `currency` (Text, Default: "usd")
- `success_url`, `cancel_url` (URLs)
- `booking_traveler_id`, `booking_companion_id` (UUID, Related bookings)
- `metadata` (JSONB, Additional metadata)
- `created_at`, `updated_at` (Timestamps)

#### `stripe_webhook_events`
Tracks Stripe webhook events for processing.

**Key Fields:**
- `id` (UUID, Primary Key)
- `stripe_event_id` (Text, Unique)
- `type` (Text, Event type)
- `api_version` (Text, Stripe API version)
- `data` (JSONB, Event data)
- `processed` (Boolean, Processing status)
- `processed_at` (Timestamp, Processing time)
- `error` (Text, Error message)
- `retry_count` (Integer, Retry attempts)
- `created_at` (Timestamp)

#### `wallet_transactions`
Tracks wallet balance changes for both travellers and companions.

**Key Fields:**
- `id` (UUID, Primary Key)
- `user_id` (Text, Foreign Key to `user.id`)
- `type` (Text, Transaction type: credit, debit, transfer)
- `amount` (Integer, in cents)
- `currency` (Text, Default: "usd")
- `balance` (Integer, Wallet balance after transaction)
- `stripe_payment_intent_id`, `stripe_transfer_id` (Text, Related Stripe entities)
- `booking_traveler_id`, `booking_companion_id` (UUID, Related bookings)
- `description` (Text, Transaction description)
- `status` (Text, Transaction status)
- `metadata` (JSONB, Additional metadata)
- `created_at` (Timestamp)

## tRPC Routers

### Stripe Traveller Router (`stripeTravellerRouter`)

Provides functionality for travellers to manage payments and wallet operations.

#### Endpoints

##### Create Customer
```typescript
POST /api/trpc/stripeTraveller.createCustomer
```
Creates or updates a Stripe customer for the authenticated user.

**Input Schema:**
```typescript
{
  name: string; // Required
  email: string; // Required, valid email
  phone: string; // Required
}
```

##### Get Customer
```typescript
GET /api/trpc/stripeTraveller.getCustomer
```
Retrieves the Stripe customer for the authenticated user.

##### Create Payment Intent
```typescript
POST /api/trpc/stripeTraveller.createPaymentIntent
```
Creates a payment intent for wallet top-ups.

**Input Schema:**
```typescript
{
  amount: number; // Required, positive number in cents
  currency: string; // Default: "usd"
  customerId: string; // Required, Stripe customer ID
}
```

##### Create Checkout Session
```typescript
POST /api/trpc/stripeTraveller.createCheckoutSession
```
Creates a checkout session for payments.

**Input Schema:**
```typescript
{
  amount: number; // Required, positive number in cents
  currency: string; // Default: "usd"
  successUrl: string; // Required, valid URL
  cancelUrl: string; // Required, valid URL
  bookingTravelerId?: string; // Optional UUID
  bookingCompanionId?: string; // Optional UUID
}
```

##### Get Wallet Balance
```typescript
GET /api/trpc/stripeTraveller.getWalletBalance
```
Retrieves the current wallet balance for the authenticated user.

##### Get Payment History
```typescript
GET /api/trpc/stripeTraveller.getPaymentHistory
```
Retrieves payment history for the authenticated user.

##### Get Customer Status
```typescript
GET /api/trpc/stripeTraveller.getCustomerStatus
```
Retrieves customer status and details.

### Stripe Companion Router (`stripeCompanionRouter`)

Provides functionality for companions to manage Connect accounts and receive payments.

#### Endpoints

##### Create Connect Account
```typescript
POST /api/trpc/stripeCompanion.createConnectAccount
```
Creates a Stripe Connect account for the authenticated companion.

**Input Schema:**
```typescript
{
  email: string; // Required, valid email
  name: string; // Required
  phone: string; // Required
}
```

##### Get Connect Account
```typescript
POST /api/trpc/stripeCompanion.getConnectAccount
```
Retrieves Connect account details.

**Input Schema:**
```typescript
{
  accountId: string; // Required, Stripe account ID
}
```

##### Create Account Link
```typescript
POST /api/trpc/stripeCompanion.createAccountLink
```
Creates an account link for onboarding/updates.

**Input Schema:**
```typescript
{
  accountId: string; // Required, Stripe account ID
  refreshUrl: string; // Required, valid URL
  returnUrl: string; // Required, valid URL
}
```

##### Create Transfer
```typescript
POST /api/trpc/stripeCompanion.createTransfer
```
Creates a transfer to the companion's Connect account.

**Input Schema:**
```typescript
{
  amount: number; // Required, positive number in cents
  currency: string; // Default: "usd"
  bookingTravelerId?: string; // Optional UUID
  bookingCompanionId?: string; // Optional UUID
  description?: string; // Optional
}
```

##### Get Wallet Balance
```typescript
GET /api/trpc/stripeCompanion.getWalletBalance
```
Retrieves the current wallet balance for the authenticated companion.

##### Get Transfer History
```typescript
GET /api/trpc/stripeCompanion.getTransferHistory
```
Retrieves transfer history for the authenticated companion.

##### Get Connect Account Status
```typescript
GET /api/trpc/stripeCompanion.getConnectAccountStatus
```
Retrieves Connect account status and requirements.

### Stripe Webhook Router (`stripeWebhookRouter`)

Handles Stripe webhook events for payment confirmations and status updates.

#### Endpoints

##### Handle Webhook
```typescript
POST /api/trpc/stripeWebhook.handleWebhook
```
Processes Stripe webhook events.

**Input Schema:**
```typescript
{
  id: string; // Required, Stripe event ID
  type: string; // Required, Event type
  data: any; // Required, Event data
}
```

**Supported Event Types:**
- `payment_intent.succeeded`: Updates payment intent status and creates wallet transaction
- `checkout.session.completed`: Updates session status and creates wallet transaction
- `transfer.created`: Updates transfer status
- `transfer.paid`: Updates transfer status and creates wallet transaction

## Environment Variables

Add the following environment variables to your `.env` file:

```env
# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_...
STRIPE_WEBHOOK_SECRET=whsec_...
```

## Usage Examples

### For Travellers

#### Creating a Customer
```typescript
const result = await trpc.stripeTraveller.createCustomer.mutate({
  name: "John Doe",
  email: "<EMAIL>",
  phone: "+**********"
});
```

#### Creating a Payment Intent
```typescript
const result = await trpc.stripeTraveller.createPaymentIntent.mutate({
  amount: 5000, // $50.00
  currency: "usd",
  customerId: "cus_**********"
});
```

#### Getting Wallet Balance
```typescript
const balance = await trpc.stripeTraveller.getWalletBalance.query();
console.log(`Current balance: $${balance.balance / 100}`);
```

### For Companions

#### Creating a Connect Account
```typescript
const result = await trpc.stripeCompanion.createConnectAccount.mutate({
  email: "<EMAIL>",
  name: "Jane Smith",
  phone: "+**********"
});
```

#### Creating an Account Link
```typescript
const result = await trpc.stripeCompanion.createAccountLink.mutate({
  accountId: "acct_**********",
  refreshUrl: "https://app.thedal.com/wallet/connect-account",
  returnUrl: "https://app.thedal.com/wallet"
});
```

#### Getting Transfer History
```typescript
const transfers = await trpc.stripeCompanion.getTransferHistory.query();
console.log(`Total transfers: ${transfers.length}`);
```

## Integration with Mobile App

The Stripe implementation is designed to work seamlessly with the existing mobile app hooks:

- `useWalletPayment`: Uses `stripeTraveller.createPaymentIntent`
- `useStripeConnect`: Uses `stripeCompanion.createConnectAccount` and related endpoints

## Security Considerations

1. **Authentication**: All endpoints require authentication via the session context
2. **Authorization**: Users can only access their own data
3. **Input Validation**: All inputs are validated using Zod schemas
4. **Webhook Verification**: Webhook events should be verified using the webhook secret
5. **Error Handling**: Comprehensive error handling with proper logging

## Database Migrations

To apply the new Stripe schema, run:

```bash
cd packages/db
pnpm db:generate
pnpm db:migrate
```

## Testing

The implementation includes comprehensive error handling and logging. Test the endpoints with:

1. Valid authentication tokens
2. Proper input validation
3. Stripe webhook events
4. Error scenarios

## Future Enhancements

1. **Subscription Management**: Add support for recurring payments
2. **Refund Processing**: Implement refund functionality
3. **Multi-Currency Support**: Expand beyond USD
4. **Advanced Analytics**: Add payment analytics and reporting
5. **Fraud Detection**: Integrate with Stripe Radar 