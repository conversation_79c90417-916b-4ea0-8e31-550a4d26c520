// Example service for using the User Profile API from the mobile app
// This file demonstrates how to integrate the new user profile API

// Note: This is an example file showing how to use the API
// In a real implementation, you would need to set up the tRPC client properly
// import { createTRPCReact } from '@trpc/react-query';
// import type { AppRouter } from '../src/root';

// For this example, we'll use a mock API interface
interface UserProfileAPI {
  userProfiles: {
    get: {
      query: () => Promise<any>;
    };
    update: {
      mutate: (data: any) => Promise<any>;
    };
  };
}

// Mock API - replace with actual tRPC client setup
const api: UserProfileAPI = {
  userProfiles: {
    get: {
      query: async () => {
        throw new Error('API not configured - see documentation for setup');
      },
    },
    update: {
      mutate: async () => {
        throw new Error('API not configured - see documentation for setup');
      },
    },
  },
};

export class UserProfileService {
  /**
   * Fetch the current user's profile
   * Creates a new profile with default values if none exists
   */
  static async getUserProfile() {
    try {
      const profile = await api.userProfiles.get.query();
      return {
        success: true,
        data: profile,
      };
    } catch (error) {
      console.error('Error fetching user profile:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Update the current user's profile
   * Creates a new profile if none exists
   */
  static async updateUserProfile(profileData: {
    name: string;
    lastName?: string;
    email?: string;
    phone?: string;
    about: string;
    typeOfTraveller: "SOLO" | "FAMILY" | "GROUP";
    gender: "MALE" | "FEMALE" | "OTHERS";
    genderPreference?: "MALE" | "FEMALE" | "OTHERS";
    openToAllGenders?: boolean;
    bookingFor?: "Self" | "Other";
    typeOfUser: "TRAVELLER" | "COMPANION";
    languages?: string[];
    userProfile?: string;
    userProfileUrl?: string;
  }) {
    try {
      const updatedProfile = await api.userProfiles.update.mutate(profileData);
      return {
        success: true,
        data: updatedProfile,
      };
    } catch (error) {
      console.error('Error updating user profile:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Update only specific fields of the user profile
   */
  static async updateProfileFields(updates: Partial<{
    name: string;
    lastName: string;
    email: string;
    phone: string;
    about: string;
    typeOfTraveller: "SOLO" | "FAMILY" | "GROUP";
    gender: "MALE" | "FEMALE" | "OTHERS";
    genderPreference: "MALE" | "FEMALE" | "OTHERS";
    openToAllGenders: boolean;
    bookingFor: "Self" | "Other";
    typeOfUser: "TRAVELLER" | "COMPANION";
    languages: string[];
    userProfile: string;
    userProfileUrl: string;
  }>) {
    try {
      // First get the current profile
      const currentProfile = await api.userProfiles.get.query();
      
      // Merge with updates
      const updatedData = {
        name: currentProfile.name,
        about: currentProfile.about || "",
        typeOfTraveller: currentProfile.typeOfTraveller || "SOLO",
        gender: currentProfile.gender || "MALE",
        typeOfUser: currentProfile.typeOfUser,
        ...updates,
      };

      const updatedProfile = await api.userProfiles.update.mutate(updatedData);
      return {
        success: true,
        data: updatedProfile,
      };
    } catch (error) {
      console.error('Error updating profile fields:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  /**
   * Update user profile image
   */
  static async updateProfileImage(userProfile: string, userProfileUrl: string) {
    return this.updateProfileFields({
      userProfile,
      userProfileUrl,
    });
  }

  /**
   * Update user languages
   */
  static async updateLanguages(languageIds: string[]) {
    return this.updateProfileFields({
      languages: languageIds,
    });
  }
}

// Example usage in React Native component:
/*
import { UserProfileService } from './user-profile-service';

const PersonalInfoScreen = () => {
  const [profile, setProfile] = useState(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadProfile();
  }, []);

  const loadProfile = async () => {
    setLoading(true);
    const result = await UserProfileService.getUserProfile();
    if (result.success) {
      setProfile(result.data);
    } else {
      console.error('Failed to load profile:', result.error);
    }
    setLoading(false);
  };

  const saveProfile = async (formData) => {
    setLoading(true);
    const result = await UserProfileService.updateUserProfile(formData);
    if (result.success) {
      setProfile(result.data);
      // Show success message
    } else {
      // Show error message
      console.error('Failed to save profile:', result.error);
    }
    setLoading(false);
  };

  // ... rest of component
};
*/ 