# Stream Chat Profile Integration

This document outlines the integration between user profile updates and Stream chat user management in the TheDal platform.

## Overview

The system automatically synchronizes user profile changes (name and image) with Stream chat users to ensure consistency across the application. When users update their profiles, their chat appearance is automatically updated in all conversations.

## Architecture

### Automatic Integration

The following profile update operations automatically trigger Stream user updates:

1. **Full Profile Update** (`userProfiles.update`)
   - Updates name, image, and other profile fields
   - Automatically upserts Stream user with new name and image

2. **Profile Picture Update** (`userProfiles.updateProfilePicture`)
   - Updates only profile picture fields
   - Automatically upserts Stream user with new image

### Manual Integration

Additional Stream user management endpoints are available for independent operations:

1. **Stream User Update** (`chat.updateUserProfile`)
   - Update Stream user name and/or image independently
   - Useful for manual synchronization

2. **Stream User Creation** (`chat.createUser`)
   - Create new Stream user
   - Used for new user onboarding

## Implementation Details

### Backend Service Layer

#### Stream Chat Service (`src/services/stream-chat.service.ts`)

```typescript
class StreamChatService {
  /**
   * Update user profile in GetStream (name and/or image)
   */
  async updateUserProfile(userId: string, name?: string, image?: string): Promise<void> {
    try {
      const updateData: any = { id: userId };
      
      if (name !== undefined) {
        updateData.name = name;
      }
      
      if (image !== undefined) {
        updateData.image = image;
      }
      
      await this.client.upsertUser(updateData);
    } catch (error) {
      console.error('Error updating GetStream user profile:', error);
      throw new Error('Failed to update GetStream user profile');
    }
  }
}
```

#### User Profile Router (`src/router/user-profiles.ts`)

```typescript
// In updateProfilePicture mutation
// Upsert Stream chat user with updated image
try {
  await streamChatService.updateUserProfile(
    appwriteUserId,
    updatedProfile[0]!.name,
    input.userProfileUrl
  );
} catch (error) {
  console.error('Error upserting Stream chat user:', error);
  // Don't throw error here as profile update was successful
  // Stream user update failure shouldn't break the profile update
}
```

#### Chat Router (`src/router/chat.ts`)

```typescript
/**
 * Update GetStream user profile (name and/or image)
 */
updateUserProfile: protectedProcedure
  .input(
    z.object({
      name: z.string().min(1).optional(),
      image: z.string().url().optional(),
    })
  )
  .mutation(async ({ input, ctx }) => {
    try {
      const userId = ctx.session.user.$id as string;
      await streamChatService.updateUserProfile(
        userId,
        input.name,
        input.image
      );
      
      return { success: true };
    } catch (error) {
      throw new TRPCError({
        code: 'INTERNAL_SERVER_ERROR',
        message: 'Failed to update GetStream user profile',
      });
    }
  }),
```

### Frontend Integration

#### Mobile App (`apps/mobile/src/app/personal-info/index.tsx`)

The mobile app automatically uses the integrated endpoints:

```typescript
// Profile picture update automatically syncs with Stream
const result = await updateProfilePictureMutation.mutateAsync({
  userProfile: uploadedUserPic.$id,
  userProfileUrl: url.href,
});

// Stream user is automatically updated
// No additional calls needed
```

## API Endpoints

### Automatic Integration Endpoints

#### `POST /api/trpc/userProfiles.update`
Updates full user profile and automatically syncs with Stream.

**Request:**
```typescript
{
  name: string;
  lastName?: string;
  email?: string;
  phone?: string;
  about: string;
  typeOfTraveler: "SOLO" | "FAMILY" | "GROUP";
  gender: "MALE" | "FEMALE" | "OTHERS";
  genderPreference?: "MALE" | "FEMALE" | "OTHERS";
  openToAllGenders?: boolean;
  bookingFor?: "SELF" | "FATHER" | "MOTHER" | "RELATIVE";
  languages?: string[];
  userProfile?: string;
  userProfileUrl?: string;
}
```

#### `POST /api/trpc/userProfiles.updateProfilePicture`
Updates only profile picture and automatically syncs with Stream.

**Request:**
```typescript
{
  userProfile: string; // File ID from storage
  userProfileUrl: string; // Public URL to access the image
}
```

### Manual Integration Endpoints

#### `POST /api/trpc/chat.updateUserProfile`
Manually update Stream user profile.

**Request:**
```typescript
{
  name?: string;
  image?: string; // URL
}
```

#### `POST /api/trpc/chat.createUser`
Create new Stream user.

**Request:**
```typescript
{
  name: string;
  image?: string; // URL
}
```

## Usage Examples

### Automatic Integration (Recommended)

```typescript
// Update profile picture - Stream user automatically updated
const result = await trpc.userProfiles.updateProfilePicture.mutate({
  userProfile: 'file_id',
  userProfileUrl: 'https://example.com/image.jpg'
});

// Update full profile - Stream user automatically updated
const fullResult = await trpc.userProfiles.update.mutate({
  name: 'John Doe',
  about: 'Travel enthusiast',
  userProfileUrl: 'https://example.com/image.jpg'
});
```

### Manual Integration (When Needed)

```typescript
// Update Stream user only
await trpc.chat.updateUserProfile.mutate({
  name: 'New Name',
  image: 'https://example.com/new-image.jpg'
});

// Create Stream user
await trpc.chat.createUser.mutate({
  name: 'User Name',
  image: 'https://example.com/image.jpg'
});
```

## Error Handling

### Non-Blocking Stream Updates

Stream user updates are designed to be non-blocking:

- **Profile updates succeed** even if Stream update fails
- **Stream errors are logged** for debugging
- **Users can continue using the app** even if Stream is temporarily unavailable

### Error Recovery

```typescript
// Stream update failure doesn't break profile update
try {
  await streamChatService.updateUserProfile(userId, name, image);
} catch (error) {
  console.error('Error upserting Stream chat user:', error);
  // Profile update still succeeds
  // Stream user can be updated later manually if needed
}
```

## Benefits

### 1. Consistency
- Profile changes automatically reflect in chat
- No manual synchronization required
- Consistent user experience across the app

### 2. Performance
- Lightweight profile picture updates
- Efficient Stream user upserts
- Minimal API calls

### 3. Reliability
- Non-blocking Stream updates
- Graceful error handling
- Fallback mechanisms available

### 4. User Experience
- Real-time profile updates in chat
- No need for users to manually sync
- Seamless integration

## Migration Guide

### From Manual Stream Updates

**Before:**
```typescript
// Update profile
await trpc.userProfiles.update.mutate(profileData);

// Manually update Stream user
await trpc.chat.createUser.mutate({
  name: profileData.name,
  image: profileData.userProfileUrl
});
```

**After:**
```typescript
// Stream user automatically updated
await trpc.userProfiles.update.mutate(profileData);
```

### From Separate Profile Picture Updates

**Before:**
```typescript
// Update profile picture
await trpc.userProfiles.update.mutate({
  ...currentProfile,
  userProfile: newFileId,
  userProfileUrl: newFileUrl
});

// Manually update Stream user
await trpc.chat.updateUserProfile.mutate({
  image: newFileUrl
});
```

**After:**
```typescript
// Stream user automatically updated
await trpc.userProfiles.updateProfilePicture.mutate({
  userProfile: newFileId,
  userProfileUrl: newFileUrl
});
```

## Testing

### Unit Tests

```typescript
// Test Stream integration
describe('Stream Profile Integration', () => {
  it('should update Stream user when profile picture changes', async () => {
    const result = await updateProfilePicture({
      userProfile: 'test_file_id',
      userProfileUrl: 'https://example.com/test.jpg'
    });
    
    expect(result.success).toBe(true);
    // Verify Stream user was updated (mock verification)
  });
});
```

### Integration Tests

```typescript
// Test complete flow
describe('Profile Picture Upload Flow', () => {
  it('should upload file and update both profile and Stream user', async () => {
    // 1. Upload file
    const uploadResult = await uploadFile(testFile);
    
    // 2. Update profile picture
    const profileResult = await updateProfilePicture({
      userProfile: uploadResult.id,
      userProfileUrl: uploadResult.url
    });
    
    // 3. Verify both updates succeeded
    expect(profileResult.success).toBe(true);
    expect(profileResult.streamUpdated).toBe(true);
  });
});
```

## Monitoring

### Logs to Monitor

1. **Stream Update Success:**
   ```
   Stream user updated successfully for user: {userId}
   ```

2. **Stream Update Failure:**
   ```
   Error upserting Stream chat user: {error}
   ```

3. **Profile Update Success:**
   ```
   Profile updated successfully for user: {userId}
   ```

### Metrics to Track

- Profile picture update success rate
- Stream user update success rate
- Profile update latency
- Stream update latency
- Error rates for both operations

## Troubleshooting

### Common Issues

1. **Stream user not updating**
   - Check Stream API credentials
   - Verify user ID consistency
   - Check Stream service logs

2. **Profile update failing**
   - Check database connectivity
   - Verify input validation
   - Check user authentication

3. **Stream errors breaking profile updates**
   - Ensure Stream updates are in try-catch blocks
   - Verify error handling doesn't throw

### Debug Steps

1. Check application logs for Stream errors
2. Verify Stream API credentials in environment
3. Test Stream service independently
4. Check user ID format consistency
5. Verify Stream user exists before updates

## Future Enhancements

### Planned Features

1. **Batch Stream Updates**
   - Update multiple users at once
   - Reduce API calls for bulk operations

2. **Stream User Caching**
   - Cache Stream user data locally
   - Reduce API calls for frequent updates

3. **Stream User Validation**
   - Validate Stream user exists before updates
   - Create user if not exists automatically

4. **Stream Update Retry Logic**
   - Retry failed Stream updates
   - Exponential backoff for retries

### Performance Optimizations

1. **Async Stream Updates**
   - Update Stream user in background
   - Don't wait for Stream response

2. **Stream Update Queuing**
   - Queue Stream updates for batch processing
   - Reduce API rate limiting issues

3. **Stream User Preloading**
   - Preload Stream user data
   - Reduce API calls for user information 