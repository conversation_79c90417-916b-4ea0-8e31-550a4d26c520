# Connection Requests API Documentation

This document outlines the tRPC API for managing connection requests between travelers and companions in the Thedal platform.

## Overview

The Connection Requests API provides functionality for:
- Creating connection requests from travelers to companions
- Retrieving connection requests for users
- Accepting or rejecting connection requests (companions only)
- Deleting pending connection requests (travelers only)
- Getting notification counts for pending requests

## Authentication

All endpoints require authentication. The API uses the session context to identify the current user and ensure proper access control.

## Database Schema

The connection requests API works with the following database tables:

- `connection_requests` - Main connection request data
- `booking_travelers` - Traveler booking information
- `booking_companions` - Companion booking information

## API Endpoints

### 1. Create Connection Request

**Endpoint:** `POST /api/trpc/connectionRequests.create`

Creates a new connection request from a traveler to a companion.

**Input Schema:**
```typescript
{
  bookingTravelerId: string; // Required UUID - traveler's booking ID
  bookingCompanionId: string; // Required UUID - companion's booking ID
}
```

**Validation Rules:**
- Current user must own the traveler booking
- Companion booking must exist and be active
- No duplicate connection request between the same traveler and companion
- Status defaults to "PENDING"

**Response:**
```typescript
{
  id: string;
  bookingTravelerId: string;
  bookingCompanionId: string;
  status: "PENDING";
  createdAt: Date;
  updatedAt: Date;
}
```

**Example Usage:**
```typescript
const result = await trpc.connectionRequests.create.mutate({
  bookingTravelerId: "traveler-uuid",
  bookingCompanionId: "companion-uuid"
});
```

### 2. Get Connection Request by ID

**Endpoint:** `GET /api/trpc/connectionRequests.getById`

Retrieves a specific connection request by ID.

**Input Schema:**
```typescript
{
  id: string; // Required UUID - connection request ID
}
```

**Access Control:**
- Current user must be either the traveler or companion involved in the request

**Response:**
```typescript
{
  id: string;
  status: "PENDING" | "ACCEPTED" | "REJECTED";
  createdAt: Date;
  updatedAt: Date;
  bookingTraveler: {
    id: string;
    name: string;
    lastName?: string;
    about: string;
    gender: "MALE" | "FEMALE" | "OTHERS";
    genderPreference: "MALE" | "FEMALE" | "OTHERS";
    openToAllGenders: boolean;
    flightPNR?: string;
    flightTime?: Date;
    flightEndTime?: Date;
    timezone?: string;
    searchField?: string;
    companionPhoto?: string;
    compensationValue?: number;
    status: "DRAFT" | "ACTIVE" | "CONFIRMED" | "CANCELLED";
    appwriteUserId: string;
  };
  bookingCompanion: {
    id: string;
    name: string;
    lastName?: string;
    about: string;
    gender: "MALE" | "FEMALE" | "OTHERS";
    typeOfTraveler: "SOLO" | "FAMILY" | "GROUP";
    genderPreference: "MALE" | "FEMALE" | "OTHERS";
    openToAllGenders: boolean;
    flightPNR?: string;
    flightTime?: Date;
    flightEndTime?: Date;
    timezone?: string;
    searchField?: string;
    travelersPhoto?: string;
    compensationValue?: number;
    status: "DRAFT" | "ACTIVE" | "CONFIRMED" | "CANCELLED";
    appwriteUserId: string;
  };
}
```

### 3. Get Connection Requests by User

**Endpoint:** `GET /api/trpc/connectionRequests.getByUser`

Retrieves connection requests for the current user with filtering options.

**Input Schema:**
```typescript
{
  status?: "PENDING" | "ACCEPTED" | "REJECTED"; // Optional filter by status
  type?: "SENT" | "RECEIVED" | "ALL"; // Default: "ALL"
}
```

**Response:**
```typescript
Array<{
  id: string;
  status: "PENDING" | "ACCEPTED" | "REJECTED";
  createdAt: Date;
  updatedAt: Date;
  bookingTraveler: {
    // Same structure as above
  };
  bookingCompanion: {
    // Same structure as above
  };
}>
```

**Example Usage:**
```typescript
// Get all connection requests
const allRequests = await trpc.connectionRequests.getByUser.query({});

// Get only pending requests
const pendingRequests = await trpc.connectionRequests.getByUser.query({
  status: "PENDING"
});

// Get only sent requests
const sentRequests = await trpc.connectionRequests.getByUser.query({
  type: "SENT"
});

// Get only received requests
const receivedRequests = await trpc.connectionRequests.getByUser.query({
  type: "RECEIVED"
});
```

### 4. Update Connection Request Status

**Endpoint:** `POST /api/trpc/connectionRequests.updateStatus`

Allows companions to accept or reject connection requests.

**Input Schema:**
```typescript
{
  id: string; // Required UUID - connection request ID
  status: "ACCEPTED" | "REJECTED"; // New status
}
```

**Access Control:**
- Only the companion (receiver) can accept or reject requests
- Request must be in "PENDING" status

**Response:**
```typescript
{
  id: string;
  bookingTravelerId: string;
  bookingCompanionId: string;
  status: "ACCEPTED" | "REJECTED";
  createdAt: Date;
  updatedAt: Date;
}
```

**Example Usage:**
```typescript
// Accept a connection request
const result = await trpc.connectionRequests.updateStatus.mutate({
  id: "request-uuid",
  status: "ACCEPTED"
});

// Reject a connection request
const result = await trpc.connectionRequests.updateStatus.mutate({
  id: "request-uuid",
  status: "REJECTED"
});
```

### 5. Delete Connection Request

**Endpoint:** `POST /api/trpc/connectionRequests.delete`

Allows travelers to delete pending connection requests.

**Input Schema:**
```typescript
{
  id: string; // Required UUID - connection request ID
}
```

**Access Control:**
- Only the traveler (sender) can delete requests
- Request must be in "PENDING" status

**Response:**
```typescript
{
  success: true;
}
```

**Example Usage:**
```typescript
const result = await trpc.connectionRequests.delete.mutate({
  id: "request-uuid"
});
```

### 6. Get Pending Connection Requests Count

**Endpoint:** `GET /api/trpc/connectionRequests.getPendingCount`

Retrieves the count of pending connection requests for companions (useful for notifications).

**Response:**
```typescript
{
  count: number;
}
```

**Example Usage:**
```typescript
const result = await trpc.connectionRequests.getPendingCount.query();
console.log(`You have ${result.count} pending connection requests`);
```

## Error Handling

The API returns appropriate error messages for various scenarios:

- **"Traveler booking not found or access denied"** - User doesn't own the traveler booking
- **"Companion booking not found or not active"** - Companion booking doesn't exist or isn't active
- **"Connection request already exists"** - Duplicate request attempted
- **"Connection request not found"** - Request ID doesn't exist
- **"Access denied"** - User doesn't have permission to access the request
- **"Only the companion can accept or reject connection requests"** - Wrong user trying to accept/reject
- **"Connection request has already been processed"** - Trying to modify a non-pending request
- **"Only the sender can delete connection requests"** - Wrong user trying to delete
- **"Cannot delete a processed connection request"** - Trying to delete a non-pending request

## Business Logic

### Connection Request Flow

1. **Creation**: Traveler creates a connection request to a companion
   - Validates traveler ownership
   - Validates companion booking exists and is active
   - Prevents duplicate requests
   - Sets status to "PENDING"

2. **Acceptance/Rejection**: Companion can accept or reject the request
   - Only companions can perform this action
   - Only pending requests can be modified
   - Updates status to "ACCEPTED" or "REJECTED"

3. **Deletion**: Traveler can delete pending requests
   - Only travelers can delete their sent requests
   - Only pending requests can be deleted

### Status Transitions

- **PENDING** → **ACCEPTED** (by companion)
- **PENDING** → **REJECTED** (by companion)
- **PENDING** → **DELETED** (by traveler)

Once a request is accepted or rejected, it cannot be modified further.

## Integration with Mobile App

The mobile app can use this API to:

1. **Send connection requests** when travelers find matching companions
2. **Display pending requests** for companions to review
3. **Show request status** to travelers (pending, accepted, rejected)
4. **Handle notifications** using the pending count endpoint
5. **Manage request lifecycle** with proper access controls

## Security Considerations

- All endpoints require authentication
- Users can only access their own connection requests
- Proper ownership validation for all operations
- Status-based access control (only pending requests can be modified)
- Role-based permissions (travelers vs companions) 