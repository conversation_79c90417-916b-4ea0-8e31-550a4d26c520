{"name": "@repo/db", "version": "0.1.0", "private": true, "type": "module", "exports": {".": "./src/index.ts", "./client": "./src/client.ts", "./schema": "./drizzle/schema.ts"}, "scripts": {"clean": "git clean -xdf .cache .turbo dist node_modules", "format": "prettier --check . --ignore-path ../../.gitignore", "lint": "eslint", "seed:admin": "pnpm with-env pnpx tsx src/seed/admin.ts", "seed:languages": "pnpm with-env pnpx tsx src/seed/languages.ts", "db:push": "pnpm with-env drizzle-kit  push", "db:generate": "pnpm with-env drizzle-kit generate", "db:migrate": "pnpm with-env drizzle-kit migrate", "db:pull": "pnpm with-env drizzle-kit pull", "db:studio": "pnpm with-env drizzle-kit studio", "typecheck": "tsc --noEmit --emitDeclarationOnly false", "with-env": "dotenv -e ../../.env --"}, "dependencies": {"@neondatabase/serverless": "^1.0.0", "@t3-oss/env-core": "catalog:", "drizzle-orm": "catalog:", "drizzle-seed": "catalog:", "drizzle-zod": "catalog:", "postgres": "catalog:", "zod": "catalog:"}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/prettier-config": "workspace:*", "@repo/tsconfig": "workspace:*", "@faker-js/faker": "^9.9.0", "@types/pg": "^8.11.14", "bcryptjs": "catalog:", "dotenv-cli": "catalog:", "drizzle-kit": "catalog:", "eslint": "catalog:", "prettier": "catalog:", "typescript": "catalog:"}, "prettier": "@repo/prettier-config"}