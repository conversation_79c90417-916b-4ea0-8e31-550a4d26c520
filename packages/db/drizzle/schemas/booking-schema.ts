import {
  pgTable,
  text,
  timestamp,
  boolean,
  integer,
  uuid,
  index,
  jsonb,
} from "drizzle-orm/pg-core";
import { airports, languages } from "./app-config-schema";
import { relations } from "drizzle-orm";

// Booking Travellers (main booking table for travelers)
export const bookingTravelers = pgTable("booking_travelers", {
  id: uuid("id").primaryKey().defaultRandom(),
  appwriteUserId: text("appwrite_user_id"),
  
  // Personal Information
  name: text("name").notNull(),
  lastName: text("last_name"),
  about: text("about"),
  gender: text("gender", { enum: ["MALE", "FEMALE", "OTHERS"] }).notNull().default("MALE"),
  typeOfTraveler: text("type_of_traveler", { enum: ["SOLO", "GROUP", "FAMILY"] }).notNull().default("SOLO"),
  
  // Booking Preferences
  genderPreference: text("gender_preference", { enum: ["MALE", "FEMALE", "OTHERS"] }).notNull().default("MALE"),
  openToAllGenders: boolean("open_to_all_genders").default(false),
  
  // Flight Information
  flightPNR: text("flight_pnr"),
  flightTime: timestamp("flight_time"),
  flightEndTime: timestamp("flight_end_time"),
  timezone: text("timezone"),
  searchField: text("search_field"),
  
  // Photos
  companionPhoto: text("companion_photo"),
  passportPhoto: text("passport_photo"),
  
  // Compensation
  compensationValue: integer("compensation_value"), // in cents
  
  // Status
  status: text("status", { enum: ["DRAFT", "ACTIVE", "CONFIRMED", "COMPLETED", "CANCELLED"] }).notNull().default("DRAFT"),
  
  // Timestamps
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp("updated_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  searchFieldIdx: index("booking_trav_search_field_idx").on(table.searchField),
  appwriteUserIdIdx: index("booking_trav_appwrite_user_id_idx").on(table.appwriteUserId),
}));

// Booking Companions (companion bookings)
export const bookingCompanions = pgTable("booking_companions", {
  id: uuid("id").primaryKey().defaultRandom(),
  appwriteUserId: text("appwrite_user_id"),
  
  // Personal Information
  name: text("name").notNull(),
  lastName: text("last_name"),
  about: text("about"),
  bookingFor: text("booking_for", { enum: ["SELF", "FATHER", "MOTHER", "RELATIVE"] }).notNull().default("SELF"),
  gender: text("gender", { enum: ["MALE", "FEMALE", "OTHERS"] }).notNull(),
  typeOfTraveler: text("type_of_traveler", { enum: ["SOLO", "GROUP", "FAMILY"] }).notNull(),
  
  // Preferences
  genderPreference: text("gender_preference", { enum: ["MALE", "FEMALE", "OTHERS"] }).notNull(),
  openToAllGenders: boolean("open_to_all_genders").default(false), // Fixed naming consistency
  
  // Flight Information
  flightPNR: text("flight_pnr"),
  flightTime: timestamp("flight_time"),
  flightEndTime: timestamp("flight_end_time"),
  timezone: text("timezone"),
  searchField: text("search_field"),
  
  // Photos
  travelersPhoto: text("travelers_photo"),
  passportPhoto: text("passport_photo"),
  
  // Compensation
  compensationValue: integer("compensation_value"), // in cents
  
  // Status
  status: text("status", { enum: ["DRAFT", "ACTIVE", "CONFIRMED", "COMPLETED", "CANCELLED"] }).notNull().default("DRAFT"),
  
  // Timestamps
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp("updated_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  searchFieldIdx: index("booking_companions_search_field_idx").on(table.searchField),
  appwriteUserIdIdx: index("booking_companions_appwrite_user_id_idx").on(table.appwriteUserId),
}));

// Booking Destinations (flight routes)
export const bookingCompanionDestinations = pgTable("booking_companion_destinations", {
  id: uuid("id").primaryKey().defaultRandom(),
  bookingCompanionId: uuid("booking_companion_id").references(() => bookingCompanions.id, { onDelete: "cascade" }),
  airportId: uuid("airport_id").references(() => airports.id, { onDelete: "cascade" }),
  order: integer("order").notNull(), // sequence of destinations
  
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp("updated_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  companionIdx: index("booking_companion_destinations_companion_idx").on(table.bookingCompanionId),
  airportIdx: index("booking_companion_destinations_airport_idx").on(table.airportId),
}));

export const bookingTravelerDestinations = pgTable("booking_traveler_destinations", {
  id: uuid("id").primaryKey().defaultRandom(),
  bookingTravelerId: uuid("booking_traveler_id").references(() => bookingTravelers.id, { onDelete: "cascade" }),
  airportId: uuid("airport_id").references(() => airports.id, { onDelete: "cascade" }),
  order: integer("order").notNull(), // sequence of destinations
  
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp("updated_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  travelerIdx: index("booking_traveler_destinations_traveler_idx").on(table.bookingTravelerId),
  airportIdx: index("booking_traveler_destinations_airport_idx").on(table.airportId),
}));

// Fixed relations - removed incorrect cross-references
export const bookingCompanionDestinationsRelations = relations(bookingCompanionDestinations, ({ one }) => ({
  bookingCompanion: one(bookingCompanions, {
    fields: [bookingCompanionDestinations.bookingCompanionId],
    references: [bookingCompanions.id],
  }),
  airport: one(airports, {
    fields: [bookingCompanionDestinations.airportId],
    references: [airports.id],
  }),
}));

export const bookingTravelerDestinationsRelations = relations(bookingTravelerDestinations, ({ one }) => ({
  bookingTraveler: one(bookingTravelers, {
    fields: [bookingTravelerDestinations.bookingTravelerId],
    references: [bookingTravelers.id],
  }),
  airport: one(airports, {
    fields: [bookingTravelerDestinations.airportId],
    references: [airports.id],
  }),
}));

// Booking Companion Languages (many-to-many relationship)
export const bookingCompanionLanguages = pgTable("booking_companion_languages", {
  id: uuid("id").primaryKey().defaultRandom(),
  bookingCompanionId: uuid("booking_companion_id").references(() => bookingCompanions.id, { onDelete: "cascade" }).notNull(),
  languageId: uuid("language_id").references(() => languages.id, { onDelete: "cascade" }).notNull(),
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  companionIdx: index("booking_companion_lang_companion_idx").on(table.bookingCompanionId),
  languageIdx: index("booking_companion_lang_language_idx").on(table.languageId),
  uniqueCompanionLanguage: index("unique_booking_companion_language_idx").on(table.bookingCompanionId, table.languageId),
}));

export const bookingCompanionLanguagesRelations = relations(bookingCompanionLanguages, ({ one }) => ({
  bookingCompanion: one(bookingCompanions, {
    fields: [bookingCompanionLanguages.bookingCompanionId],
    references: [bookingCompanions.id],
  }),
  language: one(languages, {
    fields: [bookingCompanionLanguages.languageId],
    references: [languages.id],
  }),
}));

// Booking Traveler Languages (many-to-many relationship)
export const bookingTravelerLanguages = pgTable("booking_traveler_languages", {
  id: uuid("id").primaryKey().defaultRandom(),
  bookingTravelerId: uuid("booking_traveler_id").references(() => bookingTravelers.id, { onDelete: "cascade" }).notNull(),
  languageId: uuid("language_id").references(() => languages.id, { onDelete: "cascade" }).notNull(),
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  travelerIdx: index("booking_traveler_lang_traveler_idx").on(table.bookingTravelerId),
  languageIdx: index("booking_traveler_lang_language_idx").on(table.languageId),
  uniqueTravelerLanguage: index("unique_booking_traveler_language_idx").on(table.bookingTravelerId, table.languageId),
}));

export const bookingTravelerLanguagesRelations = relations(bookingTravelerLanguages, ({ one }) => ({
  bookingTraveler: one(bookingTravelers, {
    fields: [bookingTravelerLanguages.bookingTravelerId],
    references: [bookingTravelers.id],
  }),
  language: one(languages, {
    fields: [bookingTravelerLanguages.languageId],
    references: [languages.id],
  }),
}));

// Connection Requests (between travelers and companions)
export const connectionRequests = pgTable("connection_requests", {
  id: uuid("id").primaryKey().defaultRandom(),
  bookingTravelerId: uuid("booking_traveler_id").references(() => bookingTravelers.id, { onDelete: "cascade" }),
  bookingCompanionId: uuid("booking_companion_id").references(() => bookingCompanions.id, { onDelete: "cascade" }),
  initiator: text("initiator", { enum: ["TRAVELER", "COMPANION"] }).notNull().default("TRAVELER"),
  status: text("status", { enum: ["PENDING", "PENDING_NEGOTIATION", "ACCEPTED", "REJECTED", "CANCELLED_BY_TRAVELER", "CANCELLED_BY_COMPANION"] }).notNull().default("PENDING"),
  
  // Price negotiation fields
  originalTravelerPrice: integer("original_traveler_price"), // in cents
  originalCompanionPrice: integer("original_companion_price"), // in cents
  proposedPrice: integer("proposed_price"), // in cents - the negotiated price
  priceNegotiationHistory: jsonb("price_negotiation_history").$type<{
    userId: string;
    proposedPrice: number;
    timestamp: string;
    message?: string;
  }[]>(),
  
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp("updated_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  travelerIdx: index("connection_req_traveller_idx").on(table.bookingTravelerId),
  companionIdx: index("connection_req_companion_idx").on(table.bookingCompanionId),
  // Prevent duplicate connection requests
  uniqueConnection: index("unique_connection_request_idx").on(table.bookingTravelerId, table.bookingCompanionId),
}));

// Added relations for connectionRequests
export const connectionRequestsRelations = relations(connectionRequests, ({ one }) => ({
  bookingTraveler: one(bookingTravelers, {
    fields: [connectionRequests.bookingTravelerId],
    references: [bookingTravelers.id],
  }),
  bookingCompanion: one(bookingCompanions, {
    fields: [connectionRequests.bookingCompanionId],
    references: [bookingCompanions.id],
  }),
}));

// Booking Chats (chat messages between travelers and companions)
export const bookingChats = pgTable("booking_chats", {
  id: uuid("id").primaryKey().defaultRandom(),
  connectionRequestId: uuid("connection_request_id").references(() => connectionRequests.id, { onDelete: "cascade" }),
  senderId: text("sender_id"),
  message: text("message").notNull(),
  messageType: text("message_type", { enum: ["TEXT", "IMAGE", "FILE"] }).default("TEXT"),
  
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  connectionIdx: index("booking_chats_connection_idx").on(table.connectionRequestId),
  senderIdx: index("booking_chats_sender_idx").on(table.senderId),
}));

// Added relations for bookingChats
export const bookingChatsRelations = relations(bookingChats, ({ one }) => ({
  connectionRequest: one(connectionRequests, {
    fields: [bookingChats.connectionRequestId],
    references: [connectionRequests.id],
  }),
}));

// Feedback
export const feedback = pgTable("feedback", {
  id: uuid("id").primaryKey().defaultRandom(),
  appwriteUserId: text("appwrite_user_id"),
  
  rating: integer("rating").notNull(), // 1-5 stars
  likedOption: text("liked_option"), // What they liked
  improvementOption: text("improvement_option"), // What could be improved
  additionalFeedback: text("additional_feedback"),
  
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  appwriteUserIdIdx: index("feedback_appwrite_user_id_idx").on(table.appwriteUserId),
}));

// Added relations for main tables
export const bookingTravelersRelations = relations(bookingTravelers, ({ many }) => ({
  destinations: many(bookingTravelerDestinations),
  languages: many(bookingTravelerLanguages),
  connectionRequestsAsTraveler: many(connectionRequests),
}));

export const bookingCompanionsRelations = relations(bookingCompanions, ({ many }) => ({
  destinations: many(bookingCompanionDestinations),
  languages: many(bookingCompanionLanguages),
  connectionRequestsAsCompanion: many(connectionRequests),
})); 