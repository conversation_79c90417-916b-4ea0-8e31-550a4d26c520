import {
  pgTable,
  text,
  timestamp,
  integer,
  uuid,
  decimal,
} from "drizzle-orm/pg-core";

export const posts = pgTable("posts", {
  id: integer("id").primaryKey().generatedByDefaultAsIdentity(),
  name: text("name"),
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date())
    .notNull(),
  updatedAt: timestamp("updated_at")
    .$defaultFn(() => new Date())
    .notNull(),
});

export const airports = pgTable("airports", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: text("name").notNull(),
  locationCity: text("location_city").notNull(),
  shortCode: text("short_code").notNull().unique(),
  // Additional fields from airports.json
  lat: decimal("lat", { precision: 10, scale: 6 }),
  lon: decimal("lon", { precision: 10, scale: 6 }),
  city: text("city"),
  state: text("state"),
  country: text("country"),
  timezone: text("timezone"),
  type: text("type"),
  icao: text("icao"),
  runwayLength: text("runway_length"),
  elevation: text("elevation"),
  directFlights: text("direct_flights"),
  carriers: text("carriers"),
  phone: text("phone"),
  email: text("email"),
  url: text("url"),
  woeid: text("woeid"),
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date())
    .notNull(),
  updatedAt: timestamp("updated_at")
    .$defaultFn(() => new Date())
    .notNull(),
});

export const cities = pgTable("cities", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: text("name").notNull(),
  country: text("country").notNull(),
  state: text("state").notNull(),
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date())
    .notNull(),
  updatedAt: timestamp("updated_at")
    .$defaultFn(() => new Date())
    .notNull(),
});

export const languages = pgTable("languages", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: text("name").notNull().unique(),
  createdAt: timestamp("created_at")
    .$defaultFn(() => new Date())
    .notNull(),
  updatedAt: timestamp("updated_at")
    .$defaultFn(() => new Date())
    .notNull(),
}); 