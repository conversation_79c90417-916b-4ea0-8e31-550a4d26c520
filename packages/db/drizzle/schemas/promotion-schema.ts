import {
  pgTable,
  text,
  timestamp,
  boolean,
  integer,
  uuid,
  index,
  jsonb,
} from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { adminUser } from "./admin-auth-schema";

// Promotions table
export const promotions = pgTable("promotions", {
  id: uuid("id").primaryKey().defaultRandom(),
  
  // Basic promotion info
  title: text("title").notNull(),
  imageUrl: text("image_url"), // Appwrite storage URL
  imageFileId: text("image_file_id"), // Appwrite file ID for management
  url: text("url"), // External URL or deep link
  
  // Validity
  startDate: timestamp("start_date").notNull(),
  endDate: timestamp("end_date").notNull(),
  isActive: boolean("is_active").default(true),
  
  // Targeting options
  targetUserType: text("target_user_type", { 
    enum: ["ALL", "NEW_USERS", "EXISTING_USERS", "TRAVELERS", "COMPANIONS"] 
  }).default("ALL"),
  
  priority: integer("priority").default(0), // higher priority shows first
  
  // Admin tracking
  createdBy: text("created_by").references(() => adminUser.id),
  
  // Timestamps
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp("updated_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  activeIdx: index("promotions_active_idx").on(table.isActive),
  dateRangeIdx: index("promotions_date_range_idx").on(table.startDate, table.endDate),
  priorityIdx: index("promotions_priority_idx").on(table.priority),
}));

// Promotion clicks/views tracking
export const promotionAnalytics = pgTable("promotion_analytics", {
  id: uuid("id").primaryKey().defaultRandom(),
  promotionId: uuid("promotion_id").references(() => promotions.id, { onDelete: "cascade" }).notNull(),
  
  // User tracking
  appwriteUserId: text("appwrite_user_id"), // can be null for anonymous users
  userType: text("user_type", { enum: ["TRAVELER", "COMPANION", "ANONYMOUS"] }),
  
  // Analytics data
  deviceInfo: jsonb("device_info").$type<{
    platform?: string;
    deviceType?: string;
    userAgent?: string;
  }>(),
  
  // Location data (optional)
  ipAddress: text("ip_address"),
  location: jsonb("location").$type<{
    country?: string;
    city?: string;
    region?: string;
  }>(),
  
  // Timestamps
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  promotionIdx: index("promotion_analytics_promotion_idx").on(table.promotionId),
  userIdx: index("promotion_analytics_user_idx").on(table.appwriteUserId),
  dateIdx: index("promotion_analytics_date_idx").on(table.createdAt),
}));



// Relations
export const promotionsRelations = relations(promotions, ({ one, many }) => ({
  createdBy: one(adminUser, {
    fields: [promotions.createdBy],
    references: [adminUser.id],
  }),
  analytics: many(promotionAnalytics),
}));

export const promotionAnalyticsRelations = relations(promotionAnalytics, ({ one }) => ({
  promotion: one(promotions, {
    fields: [promotionAnalytics.promotionId],
    references: [promotions.id],
  }),
}));
