import {
  pgTable,
  text,
  timestamp,
  boolean,
  integer,
  uuid,
  index,
  jsonb,
} from "drizzle-orm/pg-core";
import { bookingTravelers, bookingCompanions } from "./booking-schema";

// Stripe Customers
export const stripeCustomers = pgTable("stripe_customers", {
  id: uuid("id").primaryKey().defaultRandom(),
  appwriteUserId: text("appwrite_user_id").unique(),
  stripeCustomerId: text("stripe_customer_id").notNull().unique(),
  name: text("name"),
  email: text("email"),
  phone: text("phone"),
  
  // Metadata
  metadata: jsonb("metadata"),
  
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp("updated_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  stripeCustomerIdIdx: index("stripe_customers_stripe_id_idx").on(table.stripeCustomerId),
}));

// Stripe Connect Accounts (for companions to receive payments)
export const stripeConnectAccounts = pgTable("stripe_connect_accounts", {
  id: uuid("id").primaryKey().defaultRandom(),
  appwriteUserId: text("appwrite_user_id").unique(),
  stripeAccountId: text("stripe_account_id").notNull().unique(),
  accountType: text("account_type").notNull().default("express"), // express, standard, custom
  
  // Account details
  email: text("email"),
  businessProfile: jsonb("business_profile"),
  capabilities: jsonb("capabilities"),
  requirements: jsonb("requirements"),
  
  // Status
  chargesEnabled: boolean("charges_enabled").default(false),
  payoutsEnabled: boolean("payouts_enabled").default(false),
  detailsSubmitted: boolean("details_submitted").default(false),
  
  // Metadata
  metadata: jsonb("metadata"),
  
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp("updated_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  stripeAccountIdIdx: index("stripe_connect_accounts_stripe_id_idx").on(table.stripeAccountId),
}));

// Payment Intents (for wallet top-ups and other payments)
export const stripePaymentIntents = pgTable("stripe_payment_intents", {
  id: uuid("id").primaryKey().defaultRandom(),
  appwriteUserId: text("appwrite_user_id"),
  stripePaymentIntentId: text("stripe_payment_intent_id").notNull().unique(),
  stripeCustomerId: text("stripe_customer_id").references(() => stripeCustomers.stripeCustomerId),
  
  // Payment details
  amount: integer("amount").notNull(), // in cents
  currency: text("currency").notNull().default("usd"),
  status: text("status").notNull(), // requires_payment_method, requires_confirmation, requires_action, processing, requires_capture, canceled, succeeded
  
  // Payment method
  paymentMethodTypes: jsonb("payment_method_types"),
  lastPaymentError: jsonb("last_payment_error"),
  
  // Metadata
  metadata: jsonb("metadata"),
  
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp("updated_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  stripePaymentIntentIdIdx: index("stripe_payment_intents_stripe_id_idx").on(table.stripePaymentIntentId),
  statusIdx: index("stripe_payment_intents_status_idx").on(table.status),
}));

// Transfers (for companion payments)
export const stripeTransfers = pgTable("stripe_transfers", {
  id: uuid("id").primaryKey().defaultRandom(),
  appwriteUserId: text("appwrite_user_id"),
  stripeTransferId: text("stripe_transfer_id").notNull().unique(),
  stripeConnectAccountId: text("stripe_connect_account_id").references(() => stripeConnectAccounts.stripeAccountId),
  
  // Transfer details
  amount: integer("amount").notNull(), // in cents
  currency: text("currency").notNull().default("usd"),
  status: text("status").notNull(), // pending, in_transit, paid, failed, canceled
  
  // Related booking
  bookingTravelerId: uuid("booking_traveler_id").references(() => bookingTravelers.id),
  bookingCompanionId: uuid("booking_companion_id").references(() => bookingCompanions.id),
  
  // Transfer details
  destination: text("destination"), // bank account or card
  transferGroup: text("transfer_group"),
  
  // Metadata
  metadata: jsonb("metadata"),
  
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp("updated_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  stripeTransferIdIdx: index("stripe_transfers_stripe_id_idx").on(table.stripeTransferId),
  statusIdx: index("stripe_transfers_status_idx").on(table.status),
  bookingTravelerIdx: index("stripe_transfers_booking_traveler_idx").on(table.bookingTravelerId),
  bookingCompanionIdx: index("stripe_transfers_booking_companion_idx").on(table.bookingCompanionId),
}));

// Payment Sessions (for checkout flows)
export const stripePaymentSessions = pgTable("stripe_payment_sessions", {
  id: uuid("id").primaryKey().defaultRandom(),
  appwriteUserId: text("appwrite_user_id"),
  stripeSessionId: text("stripe_session_id").notNull().unique(),
  stripeCustomerId: text("stripe_customer_id").references(() => stripeCustomers.stripeCustomerId),
  
  // Session details
  mode: text("mode").notNull(), // payment, setup, subscription
  status: text("status").notNull(), // open, complete, expired
  
  // Payment details
  amountTotal: integer("amount_total"), // in cents
  currency: text("currency").default("usd"),
  
  // URLs
  successUrl: text("success_url"),
  cancelUrl: text("cancel_url"),
  
  // Related booking
  bookingTravelerId: uuid("booking_traveler_id").references(() => bookingTravelers.id),
  bookingCompanionId: uuid("booking_companion_id").references(() => bookingCompanions.id),
  
  // Metadata
  metadata: jsonb("metadata"),
  
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp("updated_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  stripeSessionIdIdx: index("stripe_payment_sessions_stripe_id_idx").on(table.stripeSessionId),
  statusIdx: index("stripe_payment_sessions_status_idx").on(table.status),
  bookingTravelerIdx: index("stripe_payment_sessions_booking_traveler_idx").on(table.bookingTravelerId),
  bookingCompanionIdx: index("stripe_payment_sessions_booking_companion_idx").on(table.bookingCompanionId),
}));

// Webhook Events (for tracking Stripe webhooks)
export const stripeWebhookEvents = pgTable("stripe_webhook_events", {
  id: uuid("id").primaryKey().defaultRandom(),
  stripeEventId: text("stripe_event_id").notNull().unique(),
  
  // Event details
  type: text("type").notNull(), // payment_intent.succeeded, checkout.session.completed, etc.
  apiVersion: text("api_version"),
  
  // Event data
  data: jsonb("data").notNull(),
  
  // Processing status
  processed: boolean("processed").default(false),
  processedAt: timestamp("processed_at"),
  
  // Error handling
  error: text("error"),
  retryCount: integer("retry_count").default(0),
  
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  stripeEventIdIdx: index("stripe_webhook_events_stripe_id_idx").on(table.stripeEventId),
  typeIdx: index("stripe_webhook_events_type_idx").on(table.type),
  processedIdx: index("stripe_webhook_events_processed_idx").on(table.processed),
}));

// Wallet Transactions (for tracking wallet balance changes)
export const walletTransactions = pgTable("wallet_transactions", {
  id: uuid("id").primaryKey().defaultRandom(),
  appwriteUserId: text("appwrite_user_id"),
  
  // Transaction details
  type: text("type").notNull(), // credit, debit, transfer
  amount: integer("amount").notNull(), // in cents
  currency: text("currency").notNull().default("usd"),
  balance: integer("balance").notNull(), // wallet balance after transaction
  
  // Related Stripe entities
  stripePaymentIntentId: text("stripe_payment_intent_id").references(() => stripePaymentIntents.stripePaymentIntentId),
  stripeTransferId: text("stripe_transfer_id").references(() => stripeTransfers.stripeTransferId),
  
  // Related booking
  bookingTravelerId: uuid("booking_traveler_id").references(() => bookingTravelers.id),
  bookingCompanionId: uuid("booking_companion_id").references(() => bookingCompanions.id),
  
  // Description
  description: text("description"),
  
  // Status
  status: text("status").notNull().default("completed"), // pending, completed, failed, canceled
  
  // Metadata
  metadata: jsonb("metadata"),
  
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  typeIdx: index("wallet_transactions_type_idx").on(table.type),
  statusIdx: index("wallet_transactions_status_idx").on(table.status),
  bookingTravelerIdx: index("wallet_transactions_booking_traveler_idx").on(table.bookingTravelerId),
  bookingCompanionIdx: index("wallet_transactions_booking_companion_idx").on(table.bookingCompanionId),
})); 