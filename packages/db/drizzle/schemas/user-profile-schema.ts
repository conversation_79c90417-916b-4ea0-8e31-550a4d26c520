import {
  pgTable,
  text,
  timestamp,
  boolean,
  uuid,
  index,
  integer,
} from "drizzle-orm/pg-core";
import { languages } from "./app-config-schema";
import { relations } from "drizzle-orm";

// User Profile table for storing user profile information
export const userProfiles = pgTable("user_profiles", {
  id: uuid("id").primaryKey().defaultRandom(),
  appwriteUserId: text("appwrite_user_id").unique(),
  
  // Preferences
  notification: boolean("notification").default(true),
  muteChat: boolean("mute_chat").default(false),
  openMicroPhone: boolean("open_micro_phone").default(false),
  
  // Personal Information
  name: text("name").notNull(),
  lastName: text("last_name"),
  email: text("email"),
  phone: text("phone"),
  about: text("about"),
  
  // Traveler Information
  typeOfTraveller: text("type_of_traveller", { enum: ["<PERSON><PERSON><PERSON>", "FAMILY", "GROUP"] }),
  gender: text("gender", { enum: ["MALE", "FEMALE", "OTHERS"] }),
  genderPreference: text("gender_preference", { enum: ["MALE", "FEMALE", "OTHERS"] }),
  openToAllGenders: boolean("open_to_all_genders").default(false),
  
  // Profile Settings
  bookingFor: text("booking_for", { enum: ["SELF", "FATHER", "MOTHER", "RELATIVE"] }).default("SELF"),
  // typeOfUser: text("type_of_user", { enum: ["TRAVELLER", "COMPANION"] }).notNull(),
  
  // Profile Image
  userProfile: text("user_profile"),
  userProfileUrl: text("user_profile_url"),
  
  // Wallet Balance
  walletBalance: integer("wallet_balance").default(0), // in cents
  
  // Stripe Integration
  stripeCustomerId: text("stripe_customer_id"),
  stripeConnectAccountId: text("stripe_connect_account_id"),
  
  // Timestamps
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp("updated_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  appwriteUserIdIdx: index("user_profiles_appwrite_user_id_idx").on(table.appwriteUserId),
}));

// User Profile Languages (many-to-many relationship)
export const userProfileLanguages = pgTable("user_profile_languages", {
  id: uuid("id").primaryKey().defaultRandom(),
  userProfileId: uuid("user_profile_id").references(() => userProfiles.id, { onDelete: "cascade" }).notNull(),
  languageId: uuid("language_id").references(() => languages.id, { onDelete: "cascade" }).notNull(),
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  profileIdx: index("user_profile_lang_profile_idx").on(table.userProfileId),
  languageIdx: index("user_profile_lang_language_idx").on(table.languageId),
  uniqueProfileLanguage: index("unique_user_profile_language_idx").on(table.userProfileId, table.languageId),
}));

// Account Deletion Requests
export const accountDeletionRequests = pgTable("account_deletion_requests", {
  id: uuid("id").primaryKey().defaultRandom(),
  phone: text("phone").notNull(),
  reason: text("reason").notNull(),
  status: text("status", { enum: ["PENDING", "APPROVED", "REJECTED"] }).default("PENDING").notNull(),
  adminNotes: text("admin_notes"),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  createdAt: timestamp("created_at").$defaultFn(() => new Date()).notNull(),
  updatedAt: timestamp("updated_at").$defaultFn(() => new Date()).notNull(),
}, (table) => ({
  statusIdx: index("account_deletion_requests_status_idx").on(table.status),
  phoneIdx: index("account_deletion_requests_phone_idx").on(table.phone),
}));

// Relations
export const userProfilesRelations = relations(userProfiles, ({ many }) => ({
  languages: many(userProfileLanguages),
}));

export const userProfileLanguagesRelations = relations(userProfileLanguages, ({ one }) => ({
  userProfile: one(userProfiles, {
    fields: [userProfileLanguages.userProfileId],
    references: [userProfiles.id],
  }),
  language: one(languages, {
    fields: [userProfileLanguages.languageId],
    references: [languages.id],
  }),
})); 