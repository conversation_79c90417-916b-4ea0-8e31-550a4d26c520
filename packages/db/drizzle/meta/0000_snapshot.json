{"id": "a01dbdac-6e97-4d72-8ebc-71def73d0782", "prevId": "********-0000-0000-0000-************", "version": "7", "dialect": "postgresql", "tables": {"public.Account": {"name": "Account", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "adminId": {"name": "adminId", "type": "uuid", "primaryKey": false, "notNull": true}, "accountId": {"name": "accountId", "type": "text", "primaryKey": false, "notNull": true}, "providerId": {"name": "providerId", "type": "text", "primaryKey": false, "notNull": true}, "accessToken": {"name": "accessToken", "type": "text", "primaryKey": false, "notNull": false}, "refreshToken": {"name": "refreshToken", "type": "text", "primaryKey": false, "notNull": false}, "accessTokenExpiresAt": {"name": "accessTokenExpiresAt", "type": "timestamp (3)", "primaryKey": false, "notNull": false}, "refreshTokenExpiresAt": {"name": "refreshTokenExpiresAt", "type": "timestamp (3)", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "idToken": {"name": "idToken", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp (3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp (3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"Account_providerId_accountId_key": {"name": "Account_providerId_accountId_key", "columns": [{"expression": "providerId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}, {"expression": "accountId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Admin": {"name": "Admin", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "emailVerified": {"name": "emailVerified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"Admin_email_key": {"name": "Admin_email_key", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Blog": {"name": "Blog", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "text", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "text", "primaryKey": false, "notNull": true}, "excerpt": {"name": "excerpt", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "authorName": {"name": "<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": true}, "tags": {"name": "tags", "type": "text[]", "primaryKey": false, "notNull": false}, "published": {"name": "published", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"Blog_slug_key": {"name": "Blog_slug_key", "columns": [{"expression": "slug", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ContactRequest": {"name": "ContactRequest", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "preferredCommunication": {"name": "preferredCommunication", "type": "CommunicationMode", "typeSchema": "public", "primaryKey": false, "notNull": true}, "preferredDate": {"name": "preferredDate", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "preferredTimeSlot": {"name": "preferredTimeSlot", "type": "text", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "text", "primaryKey": false, "notNull": true}, "state": {"name": "state", "type": "text", "primaryKey": false, "notNull": true}, "referralSource": {"name": "referralSource", "type": "ReferralSource", "typeSchema": "public", "primaryKey": false, "notNull": true}, "otherReferralDetails": {"name": "otherReferralDetails", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "ContactStatus", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Customer": {"name": "Customer", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "appwriteId": {"name": "appwriteId", "type": "text", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": false}, "dateOfBirth": {"name": "dateOfBirth", "type": "text", "primaryKey": false, "notNull": false}, "panNumber": {"name": "panNumber", "type": "text", "primaryKey": false, "notNull": false}, "panVerifiedResult": {"name": "panVerifiedResult", "type": "jsonb", "primaryKey": false, "notNull": false}, "lastOtpSentAt": {"name": "lastOtpSentAt", "type": "timestamp(3)", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "isPhoneVerified": {"name": "isPhoneVerified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"Customer_appwriteId_key": {"name": "Customer_appwriteId_key", "columns": [{"expression": "appwriteId", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "Customer_phone_key": {"name": "Customer_phone_key", "columns": [{"expression": "phone", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.CustomerSupportMessage": {"name": "CustomerSupportMessage", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "message": {"name": "message", "type": "text", "primaryKey": false, "notNull": true}, "fullName": {"name": "fullName", "type": "text", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "text", "primaryKey": false, "notNull": true}, "issueType": {"name": "issueType", "type": "IssueType", "typeSchema": "public", "primaryKey": false, "notNull": true}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "customerId": {"name": "customerId", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {"CustomerSupportMessage_customerId_key": {"name": "CustomerSupportMessage_customerId_key", "columns": [{"expression": "customerId", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Faq": {"name": "Faq", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "question": {"name": "question", "type": "text", "primaryKey": false, "notNull": true}, "answer": {"name": "answer", "type": "text", "primaryKey": false, "notNull": true}, "order": {"name": "order", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.MailSubscription": {"name": "MailSubscription", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "customerEmail": {"name": "customerEmail", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"MailSubscription_customerEmail_key": {"name": "MailSubscription_customerEmail_key", "columns": [{"expression": "customerEmail", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Session": {"name": "Session", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "adminId": {"name": "adminId", "type": "uuid", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp (3)", "primaryKey": false, "notNull": true}, "ipAddress": {"name": "ip<PERSON><PERSON><PERSON>", "type": "text", "primaryKey": false, "notNull": false}, "userAgent": {"name": "userAgent", "type": "text", "primaryKey": false, "notNull": false}, "createdAt": {"name": "createdAt", "type": "timestamp (3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp (3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"Session_token_key": {"name": "Session_token_key", "columns": [{"expression": "token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Testimonial": {"name": "Testimonial", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true}, "designation": {"name": "designation", "type": "text", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "text", "primaryKey": false, "notNull": true}, "imageUrl": {"name": "imageUrl", "type": "text", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.Verification": {"name": "Verification", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "identifier": {"name": "identifier", "type": "text", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "expiresAt": {"name": "expiresAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true}, "createdAt": {"name": "createdAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}, "updatedAt": {"name": "updatedAt", "type": "timestamp(3)", "primaryKey": false, "notNull": true, "default": "CURRENT_TIMESTAMP"}}, "indexes": {"Verification_identifier_value_key": {"name": "Verification_identifier_value_key", "columns": [{"expression": "identifier", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}, {"expression": "value", "isExpression": false, "asc": true, "nulls": "last", "opclass": "text_ops"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.CommunicationMode": {"name": "CommunicationMode", "schema": "public", "values": ["PHONE_CALL", "EMAIL", "WHATSAPP"]}, "public.ContactStatus": {"name": "ContactStatus", "schema": "public", "values": ["PENDING", "CONTACTED", "SCHEDULED", "COMPLETED", "NO_RESPONSE"]}, "public.IssueType": {"name": "IssueType", "schema": "public", "values": ["PAYMENT_ISSUE", "ACCOUNT_ISSUE", "LOAN_ISSUE", "GENERAL_ISSUE", "OTHER"]}, "public.ReferralSource": {"name": "ReferralSource", "schema": "public", "values": ["FRIEND_OR_FAMILY", "SOCIAL_MEDIA", "GOOGLE_SEARCH", "EVENT_OR_WEBINAR", "OTHER"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}