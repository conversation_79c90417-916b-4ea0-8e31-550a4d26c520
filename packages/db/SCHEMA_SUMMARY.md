# Database Schema Summary

## Overview

This document provides a comprehensive overview of the PostgreSQL database schema designed to replace the Appwrite collections in the Thedal mobile app.

## Table Structure

### Core Booking Tables

#### `booking_travellers`
Stores traveler booking information.

**Key Fields:**
- `id` (UUID, Primary Key)
- `user_id` (Text, Foreign Key to `user.id`)
- `name`, `last_name`, `about` (Personal info)
- `gender`, `type_of_traveller` (User characteristics)
- `booking_for`, `gender_preference`, `open_to_all_genders` (Preferences)
- `flight_pnr`, `flight_time`, `flight_end_time`, `timezone` (Flight details)
- `travellers_photo`, `passport_photo` (Documents)
- `companion_compensation` (Integer, in cents)
- `status` (Text: DRAFT, ACTIVE, CONFIRMED, CANCELLED)
- `search_field` (Text, for matching)
- `created_at`, `updated_at` (Timestamps)

**Indexes:**
- `booking_travellers_user_id_idx`
- `booking_travellers_status_idx`
- `booking_travellers_search_field_idx`

#### `booking_companions`
Stores companion booking information.

**Key Fields:**
- `id` (UUID, Primary Key)
- `user_id` (Text, Foreign Key to `user.id`)
- `name`, `last_name`, `about` (Personal info)
- `gender`, `type_of_traveller` (User characteristics)
- `gender_preference`, `open_to_all_gender` (Preferences)
- `flight_pnr`, `flight_time`, `flight_end_time`, `timezone` (Flight details)
- `companion_photo`, `passport_photo` (Documents)
- `compensation_value` (Integer, in cents)
- `status` (Text: DRAFT, ACTIVE, CONFIRMED, CANCELLED)
- `search_field` (Text, for matching)
- `created_at`, `updated_at` (Timestamps)

**Indexes:**
- `booking_companions_user_id_idx`
- `booking_companions_status_idx`
- `booking_companions_search_field_idx`

### Relationship Tables

#### `booking_destinations`
Normalized table for flight destinations.

**Key Fields:**
- `id` (UUID, Primary Key)
- `booking_traveller_id` (UUID, Foreign Key to `booking_travellers.id`)
- `booking_companion_id` (UUID, Foreign Key to `booking_companions.id`)
- `airport_id` (UUID, Foreign Key to `airports.id`)
- `order` (Integer, sequence of destinations)
- `created_at`, `updated_at` (Timestamps)

**Indexes:**
- `booking_destinations_traveller_idx`
- `booking_destinations_companion_idx`
- `booking_destinations_airport_idx`

#### `booking_languages`
Many-to-many relationship for languages.

**Key Fields:**
- `id` (UUID, Primary Key)
- `booking_traveller_id` (UUID, Foreign Key to `booking_travellers.id`)
- `booking_companion_id` (UUID, Foreign Key to `booking_companions.id`)
- `language_id` (UUID, Foreign Key to `languages.id`)
- `created_at` (Timestamp)

**Indexes:**
- `booking_languages_traveller_idx`
- `booking_languages_companion_idx`
- `booking_languages_language_idx`

### Communication Tables

#### `connection_requests`
Manages connection requests between travelers and companions.

**Key Fields:**
- `id` (UUID, Primary Key)
- `booking_traveller_id` (UUID, Foreign Key to `booking_travellers.id`)
- `booking_companion_id` (UUID, Foreign Key to `booking_companions.id`)
- `status` (Text: PENDING, ACCEPTED, REJECTED)
- `created_at`, `updated_at` (Timestamps)

**Indexes:**
- `connection_requests_traveller_idx`
- `connection_requests_companion_idx`
- `connection_requests_status_idx`

#### `booking_chats`
Stores chat messages between connected users.

**Key Fields:**
- `id` (UUID, Primary Key)
- `connection_request_id` (UUID, Foreign Key to `connection_requests.id`)
- `sender_id` (Text, Foreign Key to `user.id`)
- `message` (Text)
- `message_type` (Text: TEXT, IMAGE, FILE)
- `created_at` (Timestamp)

**Indexes:**
- `booking_chats_connection_idx`
- `booking_chats_sender_idx`

### User Data Tables

#### `user_data`
Stores user preferences and settings.

**Key Fields:**
- `id` (UUID, Primary Key)
- `user_id` (Text, Foreign Key to `user.id`, Unique)
- `notification`, `mute_chat`, `open_micro_phone` (Boolean preferences)
- `user_profile`, `user_profile_url` (Profile data)
- `traveller_profile`, `companion_profile` (Profile types)
- `stripe_customer_id`, `stripe_connect_account_id` (Payment integration)
- `created_at`, `updated_at` (Timestamps)

**Indexes:**
- `user_data_user_id_idx`

#### `feedback`
Stores user feedback and ratings.

**Key Fields:**
- `id` (UUID, Primary Key)
- `user_id` (Text, Foreign Key to `user.id`)
- `rating` (Integer, 1-5 stars)
- `liked_option`, `improvement_option`, `additional_feedback` (Text)
- `created_at` (Timestamp)

**Indexes:**
- `feedback_user_id_idx`

### Existing Tables (Already Migrated)

#### `airports`
- `id` (UUID, Primary Key)
- `name`, `location_city`, `short_code` (Airport info)
- `lat`, `lon` (Coordinates)
- `city`, `state`, `country` (Location)
- `timezone`, `type`, `icao` (Additional info)
- `created_at`, `updated_at` (Timestamps)

#### `cities`
- `id` (UUID, Primary Key)
- `name`, `country`, `state` (City info)
- `created_at`, `updated_at` (Timestamps)

#### `languages`
- `id` (UUID, Primary Key)
- `name` (Text, Unique)
- `created_at`, `updated_at` (Timestamps)

#### `user` (Auth)
- `id` (Text, Primary Key)
- `name`, `email`, `image` (User info)
- `email_verified` (Boolean)
- `created_at`, `updated_at` (Timestamps)

#### `session`, `account`, `verification` (Auth)
- Standard authentication tables for user sessions and account management

## Key Design Principles

### 1. Normalization
- Separated nested objects into proper relational tables
- Used foreign keys to maintain referential integrity
- Avoided data duplication

### 2. Performance Optimization
- Added indexes on frequently queried fields
- Used UUIDs for primary keys to maintain Appwrite ID compatibility
- Structured queries to minimize joins where possible

### 3. Data Integrity
- Cascade deletes for related records
- Proper foreign key constraints
- Timestamp tracking for all records

### 4. Scalability
- Normalized structure allows for efficient queries
- Indexes support common query patterns
- UUIDs prevent ID conflicts during migration

## Migration Considerations

### Data Transformation
- **Nested Arrays → Separate Tables**: `bookingDestinations` array becomes `booking_destinations` table
- **Object References → Foreign Keys**: Airport objects become `airport_id` foreign keys
- **ID Mapping**: Appwrite `$id` values map directly to PostgreSQL UUIDs

### Query Patterns
- **Booking Lookups**: Use `search_field` index for matching
- **User Bookings**: Use `user_id` index for user-specific queries
- **Status Filtering**: Use `status` index for active/draft bookings
- **Relationship Queries**: Join tables for complete booking data

### Performance Notes
- Consider denormalization for frequently accessed data
- Monitor query performance after migration
- Add additional indexes based on usage patterns
- Use database views for complex queries if needed

## Next Steps

1. **Generate Migration Files**: Use Drizzle to create migration files
2. **Test Schema**: Validate relationships and constraints
3. **Migrate Data**: Use provided migration scripts
4. **Update Application**: Replace Appwrite calls with tRPC
5. **Performance Testing**: Monitor and optimize queries
6. **Cleanup**: Remove Appwrite dependencies 