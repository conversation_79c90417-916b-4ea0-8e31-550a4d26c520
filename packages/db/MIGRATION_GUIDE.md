# Appwrite to Drizzle Migration Guide

This guide outlines the migration from Appwrite collections to PostgreSQL tables using Drizzle ORM for the Thedal mobile app.

## Overview

The migration involves converting the following Appwrite collections to PostgreSQL tables:

### Core Collections
1. **booking_travellers** → `booking_travellers` table
2. **booking_companions** → `booking_companions` table
3. **booking_destinations** → `booking_destinations` table (normalized)
4. **booking_languages** → `booking_languages` table (many-to-many)
5. **connection_requests** → `connection_requests` table
6. **booking_chats** → `booking_chats` table
7. **user_data** → `user_data` table
8. **feedback** → `feedback` table

### Existing Tables (Already Migrated)
- `airports` - Airport information
- `cities` - City information  
- `languages` - Language information
- `user` - User authentication
- `session` - User sessions
- `account` - User accounts
- `verification` - Email verification

## Schema Design

### Key Design Decisions

1. **Normalized Structure**: Instead of storing nested objects (like `bookingDestinations` as an array), we've normalized the data into separate tables with proper foreign key relationships.

2. **UUID Primary Keys**: Using UUIDs for primary keys to maintain compatibility with existing Appwrite IDs.

3. **Proper Indexing**: Added indexes on frequently queried fields like `userId`, `status`, and `searchField`.

4. **Cascade Deletes**: Set up proper cascade delete relationships to maintain data integrity.

5. **Timestamp Fields**: All tables include `createdAt` and `updatedAt` timestamps.

## Migration Process

### Step 1: Database Setup

1. Ensure PostgreSQL is running and accessible
2. Update environment variables to point to the new database
3. Run Drizzle migrations to create the new schema

### Step 2: Data Migration

1. **Export Appwrite Data**: Export all collections from Appwrite
2. **Transform Data**: Use the migration helpers in `src/migrations/appwrite-to-drizzle-migration.ts`
3. **Import Data**: Run the migration script to populate PostgreSQL tables

### Step 3: Update Application Code

1. **Replace Appwrite SDK**: Remove `react-native-appwrite` dependencies
2. **Update API Calls**: Replace Appwrite database calls with tRPC calls
3. **Update Environment Variables**: Remove Appwrite-specific env vars
4. **Update Types**: Update TypeScript interfaces to match new schema

## Migration Scripts

### Helper Functions

The migration script provides several helper functions:

```typescript
// Migrate a single booking traveller
await migrateBookingTraveller(appwriteTraveller);

// Migrate booking destinations
await migrateBookingDestinations(bookingId, destinations, isTraveller);

// Migrate languages
await migrateLanguages(bookingId, languages, isTraveller);
```

### Data Transformation Examples

#### Appwrite Booking Destinations → PostgreSQL
```javascript
// Appwrite structure
{
  bookingDestinations: [
    {
      order: 0,
      airports: { $id: "airport1", name: "LAX" }
    },
    {
      order: 1, 
      airports: { $id: "airport2", name: "JFK" }
    }
  ]
}

// PostgreSQL structure
// booking_destinations table:
// - booking_traveller_id: "booking123"
// - airport_id: "airport1"
// - order: 0
// - booking_traveller_id: "booking123" 
// - airport_id: "airport2"
// - order: 1
```

#### Appwrite Languages → PostgreSQL
```javascript
// Appwrite structure
{
  languages: [
    { $id: "lang1", name: "English" },
    { $id: "lang2", name: "Spanish" }
  ]
}

// PostgreSQL structure
// booking_languages table:
// - booking_traveller_id: "booking123"
// - language_id: "lang1"
// - booking_traveller_id: "booking123"
// - language_id: "lang2"
```

## API Changes

### Before (Appwrite)
```typescript
// Create booking
const booking = await databases.createDocument(
  collectionId,
  'booking_travellers',
  ID.unique(),
  bookingData
);

// Get booking with destinations
const booking = await databases.getDocument(
  collectionId,
  'booking_travellers',
  bookingId
);
// booking.bookingDestinations contains nested data
```

### After (tRPC + Drizzle)
```typescript
// Create booking
const booking = await trpc.booking.create.mutate(bookingData);

// Get booking with destinations
const booking = await trpc.booking.getById.query({ id: bookingId });
// booking.destinations contains joined data from booking_destinations table
```

## Environment Variables

### Remove (Appwrite)
```bash
COLLECTION_ID=...
COLLECTION_BOOKING_TRAVELLERS=...
COLLECTION_BOOKING_COMPANION=...
COLLECTION_BOOKING_DESTINATIONS=...
COLLECTION_BOOKING_LANGUAGES=...
COLLECTION_CONNECTION_REQUESTS=...
COLLECTION_BOOKING_CHATS=...
COLLECTION_USER=...
COLLECTION_FEEDBACK=...
```

### Add (PostgreSQL)
```bash
POSTGRES_URL=postgresql://user:password@localhost:5432/thedal
```

## Testing Migration

1. **Data Integrity**: Verify all data was migrated correctly
2. **Relationships**: Test foreign key relationships work properly
3. **Performance**: Ensure queries perform well with the new schema
4. **Functionality**: Test all app features work with the new database

## Rollback Plan

1. Keep Appwrite data as backup
2. Maintain feature flags to switch between databases
3. Have rollback scripts ready
4. Monitor application performance and errors

## Post-Migration Tasks

1. **Clean Up**: Remove Appwrite dependencies and configuration
2. **Optimize**: Add additional indexes based on query patterns
3. **Monitor**: Set up monitoring for database performance
4. **Document**: Update API documentation for new endpoints

## Common Issues and Solutions

### Issue: Nested Data Migration
**Problem**: Appwrite stores nested objects, PostgreSQL uses normalized tables
**Solution**: Use the migration helper functions to transform nested data into separate records

### Issue: ID Compatibility
**Problem**: Appwrite uses string IDs, need to maintain compatibility
**Solution**: Use UUID primary keys and map Appwrite IDs directly

### Issue: Relationship Queries
**Problem**: Need to join multiple tables instead of accessing nested data
**Solution**: Create tRPC procedures that handle the joins and return structured data

### Issue: Performance
**Problem**: Normalized queries might be slower than nested object access
**Solution**: Add proper indexes and consider denormalization for frequently accessed data

## Support

For issues during migration:
1. Check the migration logs
2. Verify data transformation logic
3. Test with small datasets first
4. Use database transaction rollbacks for failed migrations 