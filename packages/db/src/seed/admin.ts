import bcrypt from "bcryptjs";
import { randomUUID } from "crypto";

import { adminAccount, adminUser } from "../../drizzle/schema";
import { db } from "../client";
import { eq } from "../index";

const password = "Thedal@123#_admin";

async function main() {
  try {
    // Seed default admin
    const existingAdmin = await db.query.adminUser.findFirst({
      where: eq(adminUser.email, "<EMAIL>"),
    });

    if (existingAdmin) {
      console.log("Default admin already exists, skipping seed.");
    } else {
      const adminId = randomUUID();
      const [adminRes] = await db
        .insert(adminUser)
        .values({
          id: adminId,
          name: "Admin User",
          email: "<EMAIL>",
          emailVerified: true,
          image: null,
        })
        .returning();

      if (!adminRes) {
        throw new Error("Failed to create admin user");
      }

      const hashedPassword = await bcrypt.hash(password, 10);
      await db.insert(adminAccount).values({
        id: randomUUID(),
        adminUserId: adminRes.id,
        accountId: adminRes.id,
        providerId: "credential",
        password: hashedPassword,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      console.log(`Created default admin with id: ${adminRes.id}`);
    }
  } catch (error) {
    console.error("Error seeding admin accounts:", error);
    throw error;
  } finally {
    console.log("Seeding completed");
    process.exit(0);
  }
}

main()
  .then(() => {
    process.exit(0);
  })
  .catch((e) => {
    console.error(e);
    process.exit(1);
  });
