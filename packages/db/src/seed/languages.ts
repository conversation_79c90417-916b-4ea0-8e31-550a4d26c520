// Seed script for top international languages

import { db } from "../client";
import { languages } from "../../drizzle/schema";
import { eq } from "../index";
const topLanguages = [
  { name: "English" },
  { name: "Mandarin Chinese" },
  { name: "Hindi" },
  { name: "Spanish" },
  { name: "French" },
  { name: "Arabic" },
  { name: "Bengali" },
  { name: "Russian" },
  { name: "Portuguese" },
  { name: "Urdu" },
  { name: "Indonesian" },
  { name: "German" },
  { name: "Japanese" },
  { name: "Swahili" },
  { name: "Marathi" },
  { name: "Telugu" },
  { name: "Turkish" },
  { name: "Tamil" },
  { name: "Vietnamese" },
  { name: "Italian" },
  { name: "Korean" },
  { name: "Persian" },
  { name: "Polish" },
  { name: "Ukrainian" },
  { name: "Dutch" },
  { name: "Thai" },
  { name: "Gujarati" },
  { name: "Malayalam" },
  { name: "Kannada" },
  { name: "Punjabi" },
  { name: "Romanian" },
  { name: "Greek" },
  { name: "Czech" },
  { name: "Hungarian" },
  { name: "Swedish" },
  { name: "Finnish" },
  { name: "Hebrew" },
  { name: "Norwegian" },
  { name: "Danish" },
  { name: "Slovak" },
  { name: "Bulgarian" },
  { name: "Serbian" },
  { name: "Croatian" },
  { name: "Slovenian" },
  { name: "Lithuanian" },
  { name: "Latvian" },
  { name: "Estonian" },
  { name: "Filipino" },
  { name: "Malay" },
  { name: "Burmese" },
];

async function seedLanguages() {
  for (const lang of topLanguages) {
    // Check if language already exists
    const exists = await db
      .select()
      .from(languages)
      .where(eq(languages.name, lang.name))
      .limit(1);

    if (exists.length === 0) {
      await db.insert(languages).values(lang);
      console.log(`Inserted language: ${lang.name}`);
    } else {
      console.log(`Language already exists: ${lang.name}`);
    }
  }
}

seedLanguages()
  .then(() => {
    console.log("Language seeding complete.");
    process.exit(0);
  })
  .catch((err) => {
    console.error("Error seeding languages:", err);
    process.exit(1);
  });
