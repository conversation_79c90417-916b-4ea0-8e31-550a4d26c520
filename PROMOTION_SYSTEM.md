# Promotion System Implementation

## Overview

A comprehensive promotion system has been implemented that allows admins to create and manage promotional offers, with mobile app integration for users to view and redeem promotions. The system includes analytics tracking for promotion performance.

## Features Implemented

### 1. Database Schema
- **Promotions Table**: Core promotion information with support for different promotion types
- **Promotion Analytics**: Tracks user interactions (views, clicks, redemptions)  
- **Promotion Redemptions**: Records when users successfully apply promotions
- Full support for discount percentages, fixed amounts, promo codes, usage limits, and targeting

### 2. Admin Panel Features
- **Promotion Creation**: Rich form with image upload via Appwrite storage
- **Promotion Management**: View, edit, activate/deactivate, and delete promotions
- **Analytics Dashboard**: View promotion performance metrics
- **Data Table**: Sortable, filterable table showing all promotions with real-time stats

### 3. Mobile API Features
- **Active Promotions**: Fetch promotions based on user type and current date
- **Promotion Details**: Get detailed promotion information with view tracking
- **Click Tracking**: Track user clicks on promotions
- **Promo Code Validation**: Validate and calculate discounts for promo codes
- **Redemption System**: Apply promotions to bookings with usage tracking

### 4. Analytics & Tracking
- **View Tracking**: Automatic view tracking when users see promotions
- **Click Tracking**: Manual click tracking when users interact with promotions
- **Redemption Tracking**: Full redemption tracking with discount calculations
- **Performance Metrics**: CTR, conversion rates, total usage statistics

## Database Tables

### `promotions`
- Basic promotion information (title, description, image)
- Promotion type (DISCOUNT, CASHBACK, FREE_SERVICE, SPECIAL_OFFER)
- Discount configuration (percentage or fixed amount)
- Constraints (min booking amount, max discount, usage limits)
- Targeting (user types, validity period)
- Status and priority management

### `promotion_analytics`
- User interaction tracking (VIEW, CLICK, REDEEM actions)
- Device and location information
- Timestamp tracking for analytics

### `promotion_redemptions`
- Successful promotion applications
- Booking association and discount calculations
- Status tracking (PENDING, APPLIED, REFUNDED)

## API Endpoints

### Admin API (`/api/trpc/promotions`)
- `getAll` - Get all promotions with analytics
- `getCount` - Get total promotion count for pagination
- `getById` - Get specific promotion details
- `create` - Create new promotion
- `update` - Update existing promotion
- `toggleActive` - Activate/deactivate promotion
- `delete` - Delete promotion
- `getAnalytics` - Get detailed analytics for a promotion
- `getUsageStats` - Get overall system statistics

### Customer API (`/api/trpc/promotions`)
- `getActivePromotions` - Get active promotions for mobile app
- `getById` - Get promotion details with view tracking
- `trackClick` - Track user clicks on promotions
- `validatePromoCode` - Validate promo codes and calculate discounts
- `applyPromotion` - Apply promotion to booking
- `getUserRedemptions` - Get user's promotion history

## File Structure

```
packages/db/drizzle/schemas/
└── promotion-schema.ts              # Database schema definitions

apps/admin/src/
├── app/(with-layout)/promotions/
│   ├── page.tsx                     # Main promotions page
│   ├── columns.tsx                  # Table columns definition
│   ├── heading.tsx                  # Page header with create button
│   ├── create-promotion-dialog.tsx  # Promotion creation form
│   └── schema.ts                    # TypeScript schemas
├── server/api/routers/
│   └── promotions.ts                # Admin tRPC router
├── app/api/upload-promotion-image/
│   └── route.ts                     # Appwrite image upload API
└── lib/
    └── appwrite-storage.ts          # Appwrite storage utilities

packages/customer-api/src/router/
└── promotions.ts                    # Customer tRPC router
```

## Environment Variables Required

### Admin Panel
```env
APPWRITE_API_KEY=your_appwrite_api_key
```

### Customer API  
```env
APPWRITE_API_KEY=your_appwrite_api_key
APPWRITE_ENDPOINT=https://cloud.appwrite.io/v1
APPWRITE_PROJECT_ID=6711297700083cd76ad6
```

## Appwrite Storage Setup

1. Create a storage bucket named `promotion-images` in your Appwrite console
2. Set appropriate permissions for the bucket
3. Configure bucket settings for image files (size limits, allowed file types)

## Usage Examples

### Admin: Create Promotion
1. Navigate to `/promotions` in admin panel
2. Click "Create Promotion" button
3. Fill out the form with promotion details
4. Upload promotional image
5. Set targeting and constraints
6. Save promotion

### Mobile: View Promotions
```typescript
// Get active promotions for travelers
const promotions = await api.promotions.getActivePromotions.query({
  userType: "TRAVELER",
  limit: 10
});

// Track promotion view
await api.promotions.getById.query({
  id: promotionId,
  trackView: true
});

// Track promotion click
await api.promotions.trackClick.mutate({
  promotionId: promotionId,
  deviceInfo: { platform: "mobile" }
});
```

### Mobile: Apply Promo Code
```typescript
// Validate promo code
const validation = await api.promotions.validatePromoCode.query({
  promoCode: "SAVE20",
  bookingAmount: 10000, // $100.00 in cents
  userType: "TRAVELER"
});

if (validation.valid) {
  // Apply the promotion
  await api.promotions.applyPromotion.mutate({
    promotionId: validation.promotion.id,
    bookingId: "booking_123",
    bookingType: "TRAVELER",
    originalAmount: 10000,
    discountAmount: validation.calculation.discountAmount,
    finalAmount: validation.calculation.finalAmount,
    promoCode: "SAVE20"
  });
}
```

## Analytics Features

- **Real-time metrics** in admin dashboard
- **Click-through rates** and conversion tracking  
- **Usage statistics** per promotion
- **Performance comparison** across promotions
- **Historical data** with date range filtering

## Security Considerations

- All promotion redemptions are validated server-side
- Promo codes are case-sensitive and unique
- Usage limits are enforced at the database level
- File uploads are validated for type and size
- Admin operations require authentication

## Next Steps

1. **Database Migration**: Run `npm run db:generate` and `npm run db:push` to create tables
2. **Environment Setup**: Add required environment variables
3. **Appwrite Bucket**: Create the `promotion-images` storage bucket
4. **Testing**: Test the complete flow from admin creation to mobile redemption
5. **Monitoring**: Set up analytics monitoring for promotion performance

## Migration Commands

```bash
# Generate migration
cd packages/db
npm run db:generate

# Apply migration  
npm run db:push
```
