{"$schema": "https://turbo.build/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "env": [], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "dev": {"cache": false, "persistent": true}}, "globalEnv": ["POSTGRES_URL", "AUTH_REDIRECT_PROXY_URL", "PORT"], "globalPassThroughEnv": ["CI", "NODE_ENV", "APPWRITE_API_KEY", "NEXT_PUBLIC_APP_URL", "DATABASE_ID", "COLLECTION_ID_LANGUAGES", "COLLECTION_ID_LOCATION_CITY", "COLLECTION_ID_LOCATION_STATE", "COLLECTION_ID_LOCATION_COUNTRY", "COLLECTION_ID_AIRPORTS", "POSTGRES_URL", "HCAPTCHA_SECRET_KEY", "npm_lifecycle_event", "APPWRITE_ENDPOINT", "APPWRITE_PROJECT_ID", "BETTER_AUTH_SECRET", "NEXT_PUBLIC_HCAPTCHA_SITE_KEY", "NEXT_PUBLIC_BETTER_AUTH_URL", "STRIPE_SECRET_KEY", "STRIPE_WEBHOOK_SECRET"]}