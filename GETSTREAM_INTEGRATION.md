# GetStream Chat Integration

This document outlines the GetStream chat integration for the Thedal platform, enabling 1-to-1 messaging between travelers and companions once their connection request is finalized.

## Overview

The GetStream integration provides real-time 1-to-1 chat functionality for accepted connections between travelers and companions. The integration includes:

- **Backend**: Node.js service with tRPC endpoints for user management and channel creation
- **Frontend**: React Native components for chat list and individual conversations
- **Automatic Integration**: Chat channels are created automatically when connections are accepted

## Architecture

### Backend (Customer API)

**Location**: `packages/customer-api/`

**Key Components**:
- `src/services/stream-chat.service.ts` - GetStream service for user and channel management
- `src/router/chat.ts` - tRPC router with chat endpoints
- `src/router/connection-requests.ts` - Updated to create chat channels on acceptance

**Environment Variables**:
```env
STREAM_API_KEY=your_getstream_api_key
STREAM_API_SECRET=your_getstream_api_secret
STREAM_APP_ID=your_getstream_app_id
```

### Frontend (Mobile App)

**Location**: `apps/mobile/`

**Key Components**:
- `src/hooks/use-stream-chat.ts` - React hooks for chat functionality
- `src/components/chat/chat-list.tsx` - Chat list component
- `src/components/chat/chat-screen.tsx` - Individual chat screen
- `src/components/chat/chat-input.tsx` - Message input component

**Environment Variables**:
```env
STREAM_API_KEY=your_getstream_api_key
STREAM_APP_ID=your_getstream_app_id
```

## Installation

### Backend Dependencies

```bash
cd packages/customer-api
pnpm add stream-chat uuid
```

### Frontend Dependencies

```bash
cd apps/mobile
pnpm add stream-chat-react-native @react-native-community/netinfo react-native-vector-icons
```

## API Endpoints

### Chat Router (`/api/trpc/chat`)

#### `createUser`
Creates or updates a GetStream user.

**Input**:
```typescript
{
  name: string;
  image?: string;
}
```

#### `getToken`
Generates a user token for client-side authentication.

**Output**:
```typescript
{
  token: string;
}
```

#### `createChannel`
Creates a 1-to-1 channel for an accepted connection request.

**Input**:
```typescript
{
  connectionRequestId: string;
  otherUserId: string;
}
```

#### `getChannel`
Gets a specific channel by connection request ID.

**Input**:
```typescript
{
  connectionRequestId: string;
}
```

#### `getUserChannels`
Gets all chat channels for the current user.

#### `sendMessage`
Sends a message to a channel.

**Input**:
```typescript
{
  connectionRequestId: string;
  message: string;
}
```

#### `deleteChannel`
Deletes a chat channel.

**Input**:
```typescript
{
  connectionRequestId: string;
}
```

## Integration Flow

### 1. Connection Request Acceptance

When a connection request is accepted:

1. Connection status is updated to "ACCEPTED"
2. Booking statuses are updated to "CONFIRMED"
3. GetStream chat channel is automatically created
4. Channel ID is stored as `connection-{connectionRequestId}`

### 2. Chat Access

Users can access chat functionality through:

1. **Chat List**: Shows all accepted connections with chat capability
2. **Individual Chat**: Full-screen conversation with GetStream UI components
3. **Real-time Messaging**: Instant message delivery and updates

### 3. User Authentication

1. User creates/updates GetStream profile via `createUser` endpoint
2. User token is generated via `getToken` endpoint
3. Client connects to GetStream using token
4. Real-time messaging begins

## Usage Examples

### Backend Usage

```typescript
// Create a user
await trpc.chat.createUser.mutate({
  name: "John Doe",
  image: "https://example.com/avatar.jpg"
});

// Get user token
const { token } = await trpc.chat.getToken.query();

// Create channel for accepted connection
await trpc.chat.createChannel.mutate({
  connectionRequestId: "uuid",
  otherUserId: "other-user-id"
});
```

### Frontend Usage

```typescript
// Get chat token
const { data: tokenData } = useGetStreamToken();

// Connect to GetStream
await streamChatClient.connectUser(
  userId,
  userName,
  tokenData.token
);

// Send message
const sendMessage = useSendMessage();
await sendMessage.mutateAsync({
  connectionRequestId: "uuid",
  message: "Hello!"
});
```

## Security Considerations

1. **Token-based Authentication**: All chat operations require valid user tokens
2. **Channel Access Control**: Users can only access channels they're members of
3. **Connection Validation**: Chat is only available for accepted connections
4. **User Validation**: GetStream users are created/updated through secure backend endpoints

## Error Handling

The integration includes comprehensive error handling:

- **Backend**: TRPCError with appropriate HTTP status codes
- **Frontend**: User-friendly error messages and retry mechanisms
- **Network**: Graceful handling of connection issues
- **Authentication**: Proper token refresh and reconnection logic

## Configuration

### GetStream Setup

1. Create a GetStream account at https://getstream.io/
2. Create a new app for your project
3. Note your API Key, API Secret, and App ID
4. Add these to your environment variables

### Environment Variables

**Backend** (`packages/customer-api/env.ts`):
```typescript
STREAM_API_KEY: z.string().nonempty(),
STREAM_API_SECRET: z.string().nonempty(),
STREAM_APP_ID: z.string().nonempty(),
```

**Frontend** (`apps/mobile/env.js`):
```javascript
STREAM_API_KEY: z.string(),
STREAM_APP_ID: z.string(),
```

## Testing

### Backend Testing

```bash
cd packages/customer-api
pnpm test
```

### Frontend Testing

```bash
cd apps/mobile
pnpm test
```

## Deployment

### Backend Deployment

1. Ensure GetStream environment variables are set
2. Deploy customer-api package
3. Verify chat endpoints are accessible

### Frontend Deployment

1. Ensure GetStream environment variables are set
2. Build and deploy mobile app
3. Test chat functionality in production

## Troubleshooting

### Common Issues

1. **Token Generation Fails**
   - Verify GetStream API credentials
   - Check user authentication status

2. **Channel Creation Fails**
   - Verify connection request exists and is accepted
   - Check GetStream API limits and quotas

3. **Real-time Messages Not Working**
   - Verify client connection to GetStream
   - Check network connectivity
   - Verify channel membership

### Debug Mode

Enable debug logging by setting:
```env
NODE_ENV=development
```

## Future Enhancements

1. **Message Persistence**: Store messages in local database for offline access
2. **Push Notifications**: Integrate with push notification service
3. **File Sharing**: Support for image and file sharing
4. **Typing Indicators**: Show when users are typing
5. **Read Receipts**: Track message read status
6. **Message Search**: Search through chat history
7. **Chat Groups**: Support for group conversations

## Support

For issues related to:
- **GetStream Integration**: Check GetStream documentation
- **Backend Issues**: Review tRPC and database logs
- **Frontend Issues**: Check React Native and GetStream React Native logs
- **Authentication**: Verify Appwrite integration and token generation 