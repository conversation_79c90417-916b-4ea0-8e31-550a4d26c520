packages:
  - apps/*
  - packages/*
  - tooling/*
catalogs:
  react19:
    react: 19.0.0
    react-dom: 19.0.0
    "@types/react": 19.0.10
    "@types/react-dom": 19.0.0
catalog:
  "@auth/core": 0.37.2
  "@auth/prisma-adapter": ^2.7.4
  "@eslint/compat": ^1.2.5
  "@hookform/resolvers": ^4.1.3
  "@ianvs/prettier-plugin-sort-imports": ^4.4.1
  "@next/eslint-plugin-next": ^15.3.0
  "@prisma/client": 6.6.0
  "@prisma/nextjs-monorepo-workaround-plugin": 6.6.0
  "@prisma/extension-accelerate": ^1.2.2
  "@uploadthing/react": 7.3.0
  "@radix-ui/react-accordion": ^1.2.4
  "@radix-ui/react-alert-dialog": ^1.1.7
  "@radix-ui/react-aspect-ratio": ^1.1.3
  "@radix-ui/react-avatar": ^1.1.4
  "@radix-ui/react-checkbox": ^1.1.5
  "@radix-ui/react-collapsible": ^1.1.4
  "@radix-ui/react-context-menu": ^2.2.7
  "@radix-ui/react-dialog": ^1.1.7
  "@radix-ui/react-dropdown-menu": ^2.1.7
  "@radix-ui/react-hover-card": ^1.1.7
  "@radix-ui/react-label": ^2.1.3
  "@radix-ui/react-menubar": ^1.1.7
  "@radix-ui/react-navigation-menu": ^1.2.6
  "@radix-ui/react-popover": ^1.1.7
  "@radix-ui/react-progress": ^1.1.3
  "@radix-ui/react-radio-group": ^1.2.4
  "@radix-ui/react-scroll-area": ^1.2.2
  "@radix-ui/react-select": ^2.1.7
  "@radix-ui/react-separator": ^1.1.3
  "@radix-ui/react-slider": ^1.2.4
  "@radix-ui/react-slot": ^1.2.0
  "@radix-ui/react-switch": ^1.1.4
  "@radix-ui/react-tabs": ^1.1.4
  "@radix-ui/react-toast": ^1.2.7
  "@radix-ui/react-toggle": ^1.1.3
  "@radix-ui/react-toggle-group": ^1.1.3
  "@radix-ui/react-tooltip": ^1.2.0
  "@t3-oss/env-core": ^0.12.0
  "@t3-oss/env-nextjs": ^0.12.0
  "@tailwindcss/typography": ^0.5.16
  "@tanstack/react-query": ^5.67.1
  "@trpc/client": 11.1.1
  "@trpc/react-query": 11.1.1
  "@trpc/tanstack-react-query": 11.1.1
  "@trpc/server": 11.1.1
  "@turbo/gen": ^2.4.2
  "@types/eslint__js": 8.42.3
  "@types/node": ^22.10.10
  autoprefixer: ^10.4.20
  bcryptjs: ^3.0.2
  class-variance-authority: ^0.7.1
  clsx: ^2.1.1
  cmdk: 1.1.1
  date-fns: ^4.1.0
  dotenv-cli: ^8.0.0
  embla-carousel-react: ^8.6.0
  eslint: ^9.21.0
  eslint-plugin-import: ^2.31.0
  eslint-plugin-jsx-a11y: ^6.10.2
  eslint-plugin-react: ^7.37.4
  eslint-plugin-react-hooks: ^5.1.0
  eslint-plugin-turbo: ^2.5.5
  geist: ^1.3.1
  input-otp: ^1.4.2
  jiti: ^1.21.7
  lucide-react: ^0.477.0
  motion: ^12.4.10
  next: 15.3.5
  next-auth: 5.0.0-beta.25
  next-themes: ^0.4.4
  nuqs: ^2.4.3
  postcss: ^8.5.3
  prettier: ^3.3.3
  prettier-plugin-tailwindcss: ^0.6.11
  uploadthing: 7.6.0
  react-day-picker: ^9
  react-hook-form: 7.54.2
  react-resizable-panels: ^2.1.7
  react-use-measure: ^2.1.7
  recharts: ^2.15.2
  resend: ^4.2.0
  sonner: ^2.0.3
  superjson: 2.2.2
  tailwind-merge: ^3.2.0
  tailwindcss: 3.4.14
  tailwindcss-animate: ^1.0.7
  timescape: ^0.7.1
  turbo: ^2.4.2
  typescript: ^5.8.3
  typescript-eslint: ^8.21.0
  vaul: ^1.1.2
  zod: ^3.24.1
  zustand: ^5.0.4
  node-appwrite: 17.1.0
  eslint-config-next: ^15.2.3
  eslint-plugin-drizzle: ^0.2.3
  server-only: ^0.0.1
  stripe: ^18.3.0
  drizzle-kit: ^0.31.4
  drizzle-orm: ^0.44.4
  drizzle-zod: ^0.8.2
  drizzle-seed: ^0.3.1
  postgres: ^3.4.5